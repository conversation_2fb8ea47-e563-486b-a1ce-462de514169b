#!/usr/bin/env node
import 'source-map-support/register';

import { App } from 'aws-cdk-lib';

import { config } from ':config';
import { AiDescriptionGeneratorStack } from ':services/ai-description-generator/ai-description-generator.stack';
import { StoreLocatorContentGeneratorStack } from ':services/store-locator-content-generator/store-locator-content-generator.stack';

const COMMON_STACK_PROPS = {
    env: { region: config.awsRegion },
};

const app = new App();
new StoreLocatorContentGeneratorStack(app, `StoreLocatorContentGenerator${config.appSuffix}`, COMMON_STACK_PROPS);
new AiDescriptionGeneratorStack(app, `AiDescriptionGenerator${config.appSuffix}`, COMMON_STACK_PROPS);
