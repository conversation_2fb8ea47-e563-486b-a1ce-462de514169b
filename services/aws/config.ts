import { configDotenv } from 'dotenv';
import path from 'path';
import z from 'zod';

enum StackSuffix {
    LOCAL = 'Local',
    TEST = 'Test',
    DEVELOPMENT = 'Development',
    STAGING = 'Staging',
    PRODUCTION = 'Production',
}

enum Environment {
    LOCAL = 'local',
    TEST = 'test',
    DEVELOPMENT = 'development',
    STAGING = 'staging',
    PRODUCTION = 'production',
}

function getStackSuffixFromEnv(env: Environment): StackSuffix {
    return (
        {
            [Environment.LOCAL]: StackSuffix.LOCAL,
            [Environment.TEST]: StackSuffix.TEST,
            [Environment.DEVELOPMENT]: StackSuffix.DEVELOPMENT,
            [Environment.STAGING]: StackSuffix.STAGING,
            [Environment.PRODUCTION]: StackSuffix.PRODUCTION,
        }[env] || StackSuffix.LOCAL
    );
}

// Load environment variables from .env files and override them with default values if present
configDotenv({ path: path.resolve(__dirname, `.env.${process.env.NODE_ENV}`) });
configDotenv({
    path: path.resolve(__dirname, `.env.default`),
});

const configValidator = z.object({
    env: z.nativeEnum(Environment).default(Environment.LOCAL),
    awsRegion: z.string().default('eu-west-3'),
    appSuffix: z.nativeEnum(StackSuffix).default(StackSuffix.LOCAL),
    openaiApiKeys: z.object({
        storeLocatorContentGenerator: z.string(),
        aiDescriptionGenerator: z.string(),
    }),
    anthropicApiKeys: z.object({
        storeLocatorContentGenerator: z.string(),
        aiDescriptionGenerator: z.string(),
    }),
});

const initialConfig = {
    env: process.env.NODE_ENV || 'local',
    awsRegion: process.env.AWS_REGION || 'eu-west-3',
    appSuffix: getStackSuffixFromEnv((process.env.NODE_ENV || 'local') as Environment),
    openaiApiKeys: {
        storeLocatorContentGenerator: process.env.OPENAI_API_KEY_STORE_LOCATOR_CONTENT_GENERATOR,
        aiDescriptionGenerator: process.env.OPENAI_API_KEY_AI_DESCRIPTION_GENERATION,
    },
    anthropicApiKeys: {
        storeLocatorContentGenerator: process.env.ANTHROPIC_API_KEY_STORE_LOCATOR_CONTENT_GENERATOR,
        aiDescriptionGenerator: process.env.ANTHROPIC_API_KEY_AI_DESCRIPTION_GENERATION,
    },
};

export const config = configValidator.parse(initialConfig);
