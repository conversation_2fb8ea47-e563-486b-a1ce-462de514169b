from typing import Any
from pydantic import BaseModel
from enum import Enum


class RequestRelatedEntityCollectionEnum(str, Enum):
    STORE_LOCATOR = "STORE_LOCATOR_RESTAURANT_PAGE"


class ProviderEnum(str, Enum):
    OPENAI = "OpenAI"
    ANTHROPIC = "Anthropic"


class RequestTypeEnum(str, Enum):
    FAQ_SINGULAR_GENERATION = "faq_block_singular_generation"
    FAQ_GENERATION = "faq_block_generation"
    FAQ_QUESTION_GENERATION = "faq_block_question_generation"
    FAQ_ANSWER_GENERATION = "faq_block_answer_generation"
    RESTAURANT_RESTAURANT_PAGE_URL_GENERATION = "restaurant_page_url_generation"
    H1_TITLE_GENERATION = "h1_title_generation"
    HEAD_META_DESCRIPTION_GENERATION = "head_meta_description_generation"
    HEAD_META_TWITTER_DESCRIPTION_GENERATION = (
        "head_meta_twitter_description_generation"
    )
    GALLERY_BLOCK_TITLE_GENERATION = "gallery_block_title_generation"
    GALLERY_BLOCK_SUBTITLE_GENERATION = "gallery_block_subtitle_generation"
    DESCRIPTION_BLOCK_GENERATION = "description_block_generation"
    ORGANIZATION_KEYWORDS_GENERATION = "organization_keywords_generation"
    REVIEWS_BLOCK_TITLE_GENERATION = "reviews_block_title_generation"
    SOCIAL_MEDIA_BLOCK_TITLE_GENERATION = "social_media_block_title_generation"
    CTA_BLOCK_TITLE_GENERATION = "cta_block_title_generation"

    # Optimize
    FAQ_QUESTION_OPTIMIZATION = "faq_block_question_optimization"
    FAQ_ANSWER_OPTIMIZATION = "faq_block_answer_optimization"
    H1_TITLE_OPTIMIZATION = "h1_title_optimization"
    HEAD_META_DESCRIPTION_OPTIMIZATION = "head_meta_description_optimization"
    GALLERY_BLOCK_TITLE_OPTIMIZATION = "gallery_block_title_optimization"
    GALLERY_BLOCK_SUBTITLE_OPTIMIZATION = "gallery_block_subtitle_optimization"
    DESCRIPTION_BLOCK_TITLE_OPTIMIZATION = "description_block_title_optimization"
    DESCRIPTION_BLOCK_SUBTITLE_OPTIMIZATION = "description_block_subtitle_optimization"
    DESCRIPTION_BLOCK_CONTENT_OPTIMIZATION = "description_block_content_optimization"
    SOCIAL_MEDIA_BLOCK_TITLE_OPTIMIZATION = "social_media_block_title_optimization"
    CTA_BLOCK_TITLE_OPTIMIZATION = "cta_block_title_optimization"
    REVIEWS_BLOCK_TITLE_OPTIMIZATION = "reviews_block_title_optimization"
    # Map
    MAP_BLOCK_TITLE_GENERATION = "map_block_title_generation"
    MAP_DESCRIPTION_GENERATION = "map_description_generation"
    MAP_TWITTER_DESCRIPTION_GENERATION = "map_twitter_description_generation"
    MAP_KEYWORDS_GENERATION = "map_keywords_generation"
    # Description block elements
    DESCRIPTION_BLOCK_TITLE_GENERATION = "description_block_title_generation"
    DESCRIPTION_BLOCK_SUBTITLE_GENERATION = "description_block_subtitle_generation"
    DESCRIPTION_BLOCK_CONTENT_GENERATION = "description_block_content_generation"


class EventModel(BaseModel):
    relatedEntityCollection: RequestRelatedEntityCollectionEnum
    type: RequestTypeEnum
    restaurantData: Any
