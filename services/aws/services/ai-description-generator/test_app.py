import pytest
from unittest.mock import MagicMock
from app import handler
from core.models.event_model import RequestTypeEnum, RequestRelatedEntityCollectionEnum
from aws_lambda_powertools.utilities.typing import LambdaContext
import json
import os

BASE_PAYLOAD = {
    "relatedEntityCollection": RequestRelatedEntityCollectionEnum.DESCRIPTION,
    "restaurantData": {
        "bricks": ["pizza", "italian", "family-friendly"],
        "restaurantName": "Luigi's Pizzeria",
        "restaurantCategory": "Italian",
        "address": {"locality": "lyon", "postalCode": "75012"},
        "regularHours": [
      {
        "openDay": "SUNDAY",
        "openTime": "11:00",
        "closeDay": "SUNDAY",
        "closeTime": "23:00",
        "isClosed": False,
        "isPrimaryPeriod": True,
      },
      {
        "openDay": "MONDAY",
        "openTime": "11:00",
        "closeDay": "MONDAY",
        "closeTime": "23:00",
        "isClosed": False,
        "isPrimaryPeriod": True,
      },
      {
        "openDay": "TUESDAY",
        "openTime": "11:00",
        "closeDay": "TUESDAY",
        "closeTime": "23:00",
        "isClosed": False,
        "isPrimaryPeriod": True,
      },
      {
        "openDay": "WEDNESDAY",
        "openTime": "11:00",
        "closeDay": "WEDNESDAY",
        "closeTime": "23:00",
        "isClosed": False,
        "isPrimaryPeriod": True,
      },
      {
        "openDay": "THURSDAY",
        "openTime": "11:00",
        "closeDay": "THURSDAY",
        "closeTime": "23:00",
        "isClosed": False,
        "isPrimaryPeriod": True,
      },
      {
        "openDay": "FRIDAY",
        "openTime": "11:00",
        "closeDay": "FRIDAY",
        "closeTime": "23:00",
        "isClosed": False,
        "isPrimaryPeriod": True,
      },
      {
        "openDay": "SATURDAY",
        "openTime": "11:00",
        "closeDay": "SATURDAY",
        "closeTime": "23:00",
        "isClosed": False,
        "isPrimaryPeriod": True,
      },
    ],
        "language": "en",
    },
}


def append_result(result, output_file="output.json"):
    # Read existing data if file exists
    if os.path.exists(output_file):
        with open(output_file, "r", encoding="utf-8") as f:
            try:
                data = json.load(f)
                if not isinstance(data, list):
                    data = [data]
            except json.JSONDecodeError:
                data = []
    else:
        data = []

    data.append(result)

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)


def test_handler_long_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.LONG_DESCRIPTION_GENERATION

    result = handler(event, context)
    append_result(result)

    assert result is not None


def test_handler_short_description_generation():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.SHORT_DESCRIPTION_GENERATION

    result = handler(event, context)
    append_result(result)

    assert result is not None

def test_handler_short_description_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.SHORT_DESCRIPTION_OPTIMIZATION
    event['restaurantData']['description'] = "Italian pizzeria in Lyon serving wood-fired pizza in a family-friendly setting"
    
    result = handler(event, context)
    append_result(result)

    assert result is not None
    
def test_handler_long_description_optimization():
    context = MagicMock(spec=LambdaContext)
    event = BASE_PAYLOAD.copy()
    event["type"] = RequestTypeEnum.LONG_DESCRIPTION_OPTIMIZATION
    event['restaurantData']['description'] = "Luigi's Pizzeria, an Italian restaurant in Lyon, delivers authentic wood-fired pizzas and traditional Italian flavors in a welcoming, family-friendly atmosphere. This beloved neighborhood establishment has been serving generations of locals with recipes passed down through Italian tradition and genuine hospitality. Open daily from 11am to 11pm, Luigi's creates the perfect setting for family gatherings and casual dining. Come experience the taste of Italy right in your neighborhood."
    
    result = handler(event, context)
    append_result(result)

    assert result is not None
