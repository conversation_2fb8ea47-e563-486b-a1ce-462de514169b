from aws_lambda_powertools.utilities.parser import event_parser
from aws_lambda_powertools.utilities.typing import LambdaContext
from core.models.event_model import (
    EventModel,
    RequestTypeEnum,
    RequestRelatedEntityCollectionEnum,
)
from modules.long.generation.controller import (
    TextGeneratorController as LongDescriptionGeneratorController,
)
from modules.long.generation.model import (
    DescriptionGenerationPayload as LongDescriptionGenerationPayload,
)
from modules.short.generation.controller import (
    TextGeneratorController as ShortDescriptionGeneratorController,
)
from modules.short.generation.model import (
    DescriptionGenerationPayload as ShortDescriptionGenerationPayload,
)

from modules.short.optimization.controller import (
    TextOptimizerController as ShortDescriptionOptimizerController,
)
from modules.short.optimization.model import (
    DescriptionOptimizationPayload as ShortDescriptionOptimizationPayload,
)
from modules.long.optimization.controller import (
    DescriptionOptimizerController as LongDescriptionOptimizerController,
)
from modules.long.optimization.model import (
    DescriptionOptimizationPayload as LongDescriptionOptimizationPayload,
)


@event_parser(model=EventModel)
def handler(event: EventModel, context: LambdaContext):
    try:
        if (
            event.relatedEntityCollection
            == RequestRelatedEntityCollectionEnum.DESCRIPTION
        ):
            if event.type == RequestTypeEnum.SHORT_DESCRIPTION_GENERATION:
                return ShortDescriptionGeneratorController().generate(
                    ShortDescriptionGenerationPayload(
                        restaurantName=event.restaurantData.get("restaurantName", ""),
                        bricks=event.restaurantData.get("bricks", []),
                        restaurantCategory=event.restaurantData.get("restaurantCategory", ""),
                        address=event.restaurantData.get("address", ""),
                        regularHours=event.restaurantData.get("regularHours", []),
                        language=event.restaurantData.get("language", ""),
                    )
                )
            elif event.type == RequestTypeEnum.SHORT_DESCRIPTION_OPTIMIZATION:
                return ShortDescriptionOptimizerController().optimize(
                    ShortDescriptionOptimizationPayload(
                        description=event.restaurantData.get("description",""),
                        restaurantName=event.restaurantData.get("restaurantName", ""),
                        bricks=event.restaurantData.get("bricks", []),
                        restaurantCategory=event.restaurantData.get("restaurantCategory", ""),
                        address=event.restaurantData.get("address", ""),
                        regularHours=event.restaurantData.get("regularHours", []),
                        language=event.restaurantData.get("language", ""),
                    )
                )
            elif event.type == RequestTypeEnum.LONG_DESCRIPTION_GENERATION:
                return LongDescriptionGeneratorController().generate(
                    LongDescriptionGenerationPayload(
                        restaurantName=event.restaurantData.get("restaurantName", ""),
                        bricks=event.restaurantData.get("bricks", []),
                        restaurantCategory=event.restaurantData.get("restaurantCategory", ""),
                        address=event.restaurantData.get("address", ""),
                        regularHours=event.restaurantData.get("regularHours", []),
                        language=event.restaurantData.get("language", ""),
                    )
                )
            elif event.type == RequestTypeEnum.LONG_DESCRIPTION_OPTIMIZATION:
                return LongDescriptionOptimizerController().optimize(
                    LongDescriptionOptimizationPayload(
                        description=event.restaurantData.get("description",""),
                        restaurantName=event.restaurantData.get("restaurantName", ""),
                        bricks=event.restaurantData.get("bricks", []),
                        restaurantCategory=event.restaurantData.get("restaurantCategory", ""),
                        address=event.restaurantData.get("address", ""),
                        regularHours=event.restaurantData.get("regularHours", []),
                        language=event.restaurantData.get("language", ""),
                    )
                )
    except Exception as e:
        print(f"Error in handler: {e}")
        raise
