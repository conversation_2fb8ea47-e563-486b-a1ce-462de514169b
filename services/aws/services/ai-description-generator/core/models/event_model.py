from typing import Any
from pydantic import BaseModel
from enum import Enum


class RequestRelatedEntityCollectionEnum(str, Enum):
    DESCRIPTION = "DESCRIPTION"


class ProviderEnum(str, Enum):
    OPENAI = "OpenAI"


class RequestTypeEnum(str, Enum):
    LONG_DESCRIPTION_GENERATION = "long_description_generation"
    SHORT_DESCRIPTION_GENERATION = "short_description_generation"
    LONG_DESCRIPTION_OPTIMIZATION = "long_description_optimization"
    SHORT_DESCRIPTION_OPTIMIZATION = "short_description_optimization"


class EventModel(BaseModel):
    relatedEntityCollection: RequestRelatedEntityCollectionEnum
    type: RequestTypeEnum
    restaurantData: Any
