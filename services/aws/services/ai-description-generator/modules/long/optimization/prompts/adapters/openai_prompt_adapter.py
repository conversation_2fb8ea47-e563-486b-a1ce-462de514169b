from modules.long.optimization.model import (
    DescriptionOptimizationPayload,
        Schedule
)
from modules.long.optimization.prompts.prompt_provider_abstraction import (
    PromptProviderAbstraction,
)

from typing import Optional, Dict, List

class OpenAiPromptAdapter(PromptProviderAbstraction):
    def summarize_schedule(self,schedules: List[Schedule]) -> str:
            # Initialize variables
            schedule_ranges = []
            current_range = []

            for i, schedule in enumerate(schedules):
                if schedule.isClosed:
                    continue

                # If it's the first schedule or if it continues the same day range
                if not current_range:
                    current_range.append(schedule)
                else:
                    # Check if we can merge consecutive days with the same hours
                    last_schedule = current_range[-1]
                    if (
                        last_schedule.closeDay == schedule.openDay
                        and last_schedule.openTime == schedule.openTime
                        and last_schedule.closeTime == schedule.closeTime
                    ):
                        current_range.append(schedule)
                    else:
                        # Otherwise, store the current range and start a new one
                        schedule_ranges.append(current_range)
                        current_range = [schedule]

            # Add the last range if it exists
            if current_range:
                schedule_ranges.append(current_range)

            # Construct the output string
            summary = []
            for range_group in schedule_ranges:
                # For each group of schedules, determine the range
                start_day = range_group[0].openDay
                end_day = range_group[-1].closeDay
                open_time = range_group[0].openTime
                close_time = range_group[-1].closeTime

                # If the schedule spans multiple days with the same time
                if start_day == end_day:
                    summary.append(
                        f"{start_day.capitalize()} from {open_time} to {close_time}"
                    )
                else:
                    # Check if there are consecutive days with the same schedule
                    consecutive_days = [start_day]
                    for j in range(1, len(range_group)):
                        if (
                            range_group[j].openTime == open_time
                            and range_group[j].closeTime == close_time
                        ):
                            consecutive_days.append(range_group[j].openDay)
                        else:
                            break

                    # If we have consecutive days with the same schedule, group them
                    if len(consecutive_days) > 1:
                        summary.append(
                            f"{consecutive_days[0].capitalize()} to {consecutive_days[-1].capitalize()} from {open_time} to {close_time}"
                        )
                    else:
                        summary.append(
                            f"{start_day.capitalize()} from {open_time} to {close_time}"
                        )

            # Join the different parts and return the complete string
            return ", ".join(summary) + "."

    def get_prompt(self, payload: DescriptionOptimizationPayload):
        language = payload.language
        description = payload.description
        address = payload.address
        bricks = payload.bricks
        restaurant_name = payload.restaurantName
        restaurant_category = payload.restaurantCategory
        regular_hours = self.summarize_schedule(payload.regularHours)
        context = """
        You are an expert copywriter specializing in concise, informative, and honest restaurant descriptions for hospitality businesses.



    Task: Rewrite and refine the provided existing description to produce a new description that strictly follows the structure and constraints below. Use the existing description only as inspiration; improve clarity, specificity, flow, and tone while removing fluff and marketing hype.



    Required structure (in this exact order):

    1) name + category/type (avoid parentheses; use commas or natural phrasing)

    2) location hint only (neighborhood, landmark, or general area; not full address)

    3) value proposition (what the place uniquely offers: ambiance, cuisine, quality, services)

    4) uniqueness/history (story, signature features, awards, sustainability, etc.)

    5) opening hours integrated naturally into the flow (a brief, smooth summary; partial overview allowed)

    6) subtle but clear call-to-action (CTA) inviting guests to visit/experience



    Instructions:

    - Write in the requested language.

    - Do not exceed 750 characters.

    - From provided keywords, keep only those that reveal specialties and services; use the bare minimum so the text remains smooth and natural.

    - Mention differentiators (ambiance, cuisine, product quality, special events, services, accessibility, spoken languages) with rich, specific language matching the restaurant and category.

    - Avoid parentheses, superlatives like "the best", discounts/promos, links, emojis, keyword stuffing, or offensive/vulgar content.

    - Output only the final description.

    - The description must strictly follow the field order: name+category → location (hint only) → value proposition → uniqueness/history → opening hours (integrated) → CTA.

    - If some inputs are missing, craft a high-quality line that remains truthful and non-speculative; omit unknowns rather than inventing facts.
        """
        prompt = f"""
        Existing Description: {description}

        Restaurant Name: {restaurant_name}

        Restaurant Category: {restaurant_category}

        Address: {address}

        Opening Hours: {regular_hours}

        Keywords: {bricks}

        Language: {language}"""

        return context, prompt
