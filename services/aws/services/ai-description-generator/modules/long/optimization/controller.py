from modules.long.optimization.handler import DescriptionOptimizer
from modules.long.optimization.model import (
    DescriptionOptimizationPayload,
    DescriptionOptimization,
)


class DescriptionOptimizerController:

    def __init__(
        self,
        descriptionOptimizer: DescriptionOptimizer = DescriptionOptimizer(),
    ):
        self._generate = descriptionOptimizer

    def optimize(self, event: DescriptionOptimizationPayload) -> DescriptionOptimization:
        return self._generate.execute(payload=event)
