from modules.long.generation.model import (
    DescriptionGenerationPayload,
    Schedule
)
from modules.long.generation.prompts.prompt_provider_abstraction import (
    PromptProviderAbstraction,
)
from typing import Optional, Dict, List


class OpenAiPromptAdapter(PromptProviderAbstraction):

    def summarize_schedule(self,schedules: List[Schedule]) -> str:
            # Initialize variables
            schedule_ranges = []
            current_range = []

            for i, schedule in enumerate(schedules):
                if schedule.isClosed:
                    continue

                # If it's the first schedule or if it continues the same day range
                if not current_range:
                    current_range.append(schedule)
                else:
                    # Check if we can merge consecutive days with the same hours
                    last_schedule = current_range[-1]
                    if (
                        last_schedule.closeDay == schedule.openDay
                        and last_schedule.openTime == schedule.openTime
                        and last_schedule.closeTime == schedule.closeTime
                    ):
                        current_range.append(schedule)
                    else:
                        # Otherwise, store the current range and start a new one
                        schedule_ranges.append(current_range)
                        current_range = [schedule]

            # Add the last range if it exists
            if current_range:
                schedule_ranges.append(current_range)

            # Construct the output string
            summary = []
            for range_group in schedule_ranges:
                # For each group of schedules, determine the range
                start_day = range_group[0].openDay
                end_day = range_group[-1].closeDay
                open_time = range_group[0].openTime
                close_time = range_group[-1].closeTime

                # If the schedule spans multiple days with the same time
                if start_day == end_day:
                    summary.append(
                        f"{start_day.capitalize()} from {open_time} to {close_time}"
                    )
                else:
                    # Check if there are consecutive days with the same schedule
                    consecutive_days = [start_day]
                    for j in range(1, len(range_group)):
                        if (
                            range_group[j].openTime == open_time
                            and range_group[j].closeTime == close_time
                        ):
                            consecutive_days.append(range_group[j].openDay)
                        else:
                            break

                    # If we have consecutive days with the same schedule, group them
                    if len(consecutive_days) > 1:
                        summary.append(
                            f"{consecutive_days[0].capitalize()} to {consecutive_days[-1].capitalize()} from {open_time} to {close_time}"
                        )
                    else:
                        summary.append(
                            f"{start_day.capitalize()} from {open_time} to {close_time}"
                        )

            # Join the different parts and return the complete string
            return ", ".join(summary) + "."

    def get_prompt(self, payload: DescriptionGenerationPayload):
        language = payload.language
        address = payload.address
        bricks = payload.bricks
        restaurant_name = payload.restaurantName
        restaurant_category = payload.restaurantCategory
        regular_hours = self.summarize_schedule(payload.regularHours)
        context = f"""
        You are an expert copywriter specializing in concise, informative, and honest restaurant descriptions for hospitality businesses. Follow this structure in the final description:



        1. Start with the restaurant name and its category/type (avoid using parentheses; use commas or natural phrasing).

        2. Add a clear indication of the location, but use only a hint (such as neighborhood, landmark, or general area), not the entire address.

        3. State the value proposition: what does this place uniquely offer (ambiance, cuisine, quality, services, etc.)?

        4. Highlight what makes the restaurant special, such as its story/history or special attributes.

        5. Integrate a summary of the opening hours naturally and smoothly into the flow, rather than as a separate statement. Partially omitting parts of the schedule for a quick overview of the regular hours is permitted.

        6. End with a subtle but clear call-to-action (CTA) inviting guests to visit or experience the restaurant.



        Instructions:

        - Write the description in the passed language, not exceeding 750 characters.

        - From the provided keywords, keep only those that reveal the restaurant’s specialties and services. Use the bare minimum so the description stays smooth and natural.

        - Mention differentiators (ambiance, cuisine, product quality, special events, services, accessibility, spoken languages), using rich, specific language matching the restaurant and category.

        - Avoid parentheses, promotional statements, claims of being "the best", discounts/promos, links, emojis, keyword stuffing, or offensive/vulgar content.

        - The whole description should be written in a professional, engaging, and inviting tone, suitable for hospitality. The CTA should match this style.

        - Output only the final description, without any reasoning or extra commentary.

        - The description must strictly follow the field order: name+category → location (hint only) → value proposition → uniqueness/history → opening hours (integrated smoothly) → CTA.



        Examples:

        Description: Trattoria Bella, an Italian restaurant, is located in the heart of the central market district. Enjoy authentic Italian recipes and a warm atmosphere. Founded by the Rossi family, this address carries on a gourmet tradition for three generations. Open 12pm–11pm Monday to Saturday. Reserve your table for a culinary journey.

        Description: Café Émeraude, a riverside café-bistro on the Loire, offers exceptional coffees and refined brunches. Awarded for its sustainability efforts, the venue features a unique, welcoming vibe you can enjoy every day of the week from 8 am to 10 pm. Stop by for a delightful break.



        Output:

        Return only the description.
        """
        prompt = f"""
        Restaurant Name: {restaurant_name}

        Restaurant Category: {restaurant_category}

        Address: {address}

        Opening Hours: {regular_hours}

        Keywords: {bricks}

        Language: {language}

        """
        return context, prompt
