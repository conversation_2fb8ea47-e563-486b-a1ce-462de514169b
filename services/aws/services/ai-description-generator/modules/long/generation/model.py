from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field, model_validator
from enum import Enum

AI_MAX_RETRIES = 3


class Day(str, Enum):
    MONDAY = "MONDAY"
    TUESDAY = "TUESDAY"
    WEDNESDAY = "WEDNESDAY"
    THURSDAY = "THURSDAY"
    FRIDAY = "FRIDAY"
    SATURDAY = "SATURDAY"
    SUNDAY = "SUNDAY"


class Schedule(BaseModel):
    openDay: Day
    closeDay: Day
    isClosed: bool
    openTime: str
    closeTime: str

class DescriptionGenerationPayload(BaseModel):
    bricks: List[str]
    restaurantName: str
    restaurantCategory: str
    address: Dict[str, str]
    regularHours: Optional[List[Schedule]] = None
    language: str


class DescriptionGeneration(BaseModel):
    text: str = Field(
        description="The generated description text for the restaurant.",
        min_length=250,
        max_length=750,
    )


class AiConfig(BaseModel):
    frequencyPenalty: Optional[float] = 0
    temperature: Optional[float] = 1.0
    maxTokens: Optional[float] = 4000
    timeout: Optional[float] = 55
    model: Optional[str] = "gpt-4.1-mini"
    topP: Optional[float] = 1.0


class AiResponseMetadata(BaseModel):
    relatedEntityCollection: str
    type: str
    message: List[Dict[str, str]]
    completionText: str
    completionTokenCount: int
    promptTokenCount: int
    completionTimeInMilliseconds: int
    modelConfig: Optional[AiConfig] = None
    numberOfRetries: Optional[int] = 0


class TextGeneration(BaseModel):
    aiResponse: DescriptionGeneration
    aiInteractionDetails: AiResponseMetadata
