from modules.long.generation.handler import TextGenerator
from modules.long.generation.model import (
    DescriptionGenerationPayload,
    TextGeneration,
)


class TextGeneratorController:

    def __init__(
        self,
        textGenerator: TextGenerator = TextGenerator(),
    ):
        self._generate = textGenerator

    def generate(self, event: DescriptionGenerationPayload) -> TextGeneration:
        return self._generate.execute(payload=event)
