from .model import (
    AiResponseMetadata,
    AiConfig,
    DescriptionGenerationPayload,
    DescriptionGeneration,
    TextGeneration,
    AI_MAX_RETRIES,
)

from core.models.event_model import RequestRelatedEntityCollectionEnum, RequestTypeEnum
from anthropic_provider.ai_provider_port import AiProvider
from modules.long.generation.prompts.prompt_provider_port import (
    PromptProvider,
)

from anthropic_provider.adapters.anthropic.anthropic_provider_adapter import (
    AnthropicAdapter,
)

from utils.utils import SimpleTextCleaner
from pydantic import BaseModel
from typing import Any, List


class TextGenerator:
    def __init__(
        self,
        ai_provider: AiProvider = AnthropicAdapter(),
        prompt_provider: PromptProvider = PromptProvider(),
    ) -> None:
        self.__ai_provider = ai_provider
        self.__prompt_provider = prompt_provider

    def execute(self, payload: DescriptionGenerationPayload) -> TextGeneration:
        context, prompt = self.__prompt_provider.get_prompt(payload=payload)
        configuration = AiConfig(
            model="claude-sonnet-4-20250514",
        )
        for retry in range(AI_MAX_RETRIES):
            try:
                ai_response_details = self.__ai_provider.callWithContext(
                    prompt=prompt,
                    context=context,
                    configuration=configuration,
                    response_model=DescriptionGeneration,
                )
                break
            except Exception as e:
                print(f"Error in generation: {e}")
                if retry == AI_MAX_RETRIES - 1:
                    raise
                continue

        return TextGeneration(
            aiResponse=DescriptionGeneration(
                text=SimpleTextCleaner.clean(ai_response_details.response.text)
            ),
            aiInteractionDetails=AiResponseMetadata(
                relatedEntityCollection=RequestRelatedEntityCollectionEnum.DESCRIPTION.value,
                type="long_description_generation",
                message=ai_response_details.metadata.message,
                completionText=str(ai_response_details.response),
                completionTokenCount=ai_response_details.metadata.completion_tokens,
                promptTokenCount=ai_response_details.metadata.prompt_tokens,
                completionTimeInMilliseconds=ai_response_details.metadata.request_response_time,
                modelConfig=configuration,
                numberOfRetries=retry,
            ),
        ).model_dump()
