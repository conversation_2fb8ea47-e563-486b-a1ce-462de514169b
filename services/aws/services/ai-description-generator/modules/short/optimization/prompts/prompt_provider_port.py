from modules.short.optimization.prompts.adapters.openai_prompt_adapter import (
    OpenAiPromptAdapter,
)
from modules.short.optimization.prompts.prompt_provider_abstraction import (
    PromptProviderAbstraction,
)
from modules.short.optimization.model import DescriptionOptimizationPayload


class PromptProvider(PromptProviderAbstraction):
    def __init__(
        self, prompt_impl: PromptProviderAbstraction = OpenAiPromptAdapter()
    ) -> None:
        self.__prompt_impl = prompt_impl

    def get_prompt(self, payload: DescriptionOptimizationPayload) -> str:
        return self.__prompt_impl.get_prompt(payload)
