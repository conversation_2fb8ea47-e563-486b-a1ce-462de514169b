from modules.short.optimization.model import (
    DescriptionOptimizationPayload,
    Schedule
)
from modules.short.optimization.prompts.prompt_provider_abstraction import (
    PromptProviderAbstraction,
)
from typing import Optional, Dict, List


class OpenAiPromptAdapter(PromptProviderAbstraction):

    def summarize_schedule(schedules: List[Schedule]) -> str:
            # Initialize variables
            schedule_ranges = []
            current_range = []

            for i, schedule in enumerate(schedules):
                if schedule.isClosed:
                    continue

                # If it's the first schedule or if it continues the same day range
                if not current_range:
                    current_range.append(schedule)
                else:
                    # Check if we can merge consecutive days with the same hours
                    last_schedule = current_range[-1]
                    if (
                        last_schedule.closeDay == schedule.openDay
                        and last_schedule.openTime == schedule.openTime
                        and last_schedule.closeTime == schedule.closeTime
                    ):
                        current_range.append(schedule)
                    else:
                        # Otherwise, store the current range and start a new one
                        schedule_ranges.append(current_range)
                        current_range = [schedule]

            # Add the last range if it exists
            if current_range:
                schedule_ranges.append(current_range)

            # Construct the output string
            summary = []
            for range_group in schedule_ranges:
                # For each group of schedules, determine the range
                start_day = range_group[0].openDay
                end_day = range_group[-1].closeDay
                open_time = range_group[0].openTime
                close_time = range_group[-1].closeTime

                # If the schedule spans multiple days with the same time
                if start_day == end_day:
                    summary.append(
                        f"{start_day.capitalize()} from {open_time} to {close_time}"
                    )
                else:
                    # Check if there are consecutive days with the same schedule
                    consecutive_days = [start_day]
                    for j in range(1, len(range_group)):
                        if (
                            range_group[j].openTime == open_time
                            and range_group[j].closeTime == close_time
                        ):
                            consecutive_days.append(range_group[j].openDay)
                        else:
                            break

                    # If we have consecutive days with the same schedule, group them
                    if len(consecutive_days) > 1:
                        summary.append(
                            f"{consecutive_days[0].capitalize()} to {consecutive_days[-1].capitalize()} from {open_time} to {close_time}"
                        )
                    else:
                        summary.append(
                            f"{start_day.capitalize()} from {open_time} to {close_time}"
                        )

            # Join the different parts and return the complete string
            return ", ".join(summary) + "."
    def get_prompt(self, payload: DescriptionOptimizationPayload):
        description = payload.description
        bricks = payload.bricks
        restaurant_name = payload.restaurantName
        restaurant_category = payload.restaurantCategory
        address = (
            payload.address.get("locality", "")
            + ", "
            + payload.address.get("postalCode", "")
        )


        context = f"""
          You are an expert restaurant copywriter.



Task:

Optimize a provided one-line restaurant description to meet all requirements below. Preserve the brand tone implied by inputs and keep it in the same language as provided.



Requirements (must follow all):

- Length: <100 characters. If >101, auto-trim to the last full word under 100.

- Include what + where: cuisine/style + neighborhood/city (derive from Address/local cues like neighborhood names if present).

- Add one differentiator: signature dish, amenity, or hook (e.g., "wood-fired pizza", "open late", "natural wines").

- Evergreen: no prices, promos, or time-bound offers.

- No links, phone numbers, hours, or hashtags.

- No calls to action.

- Use plain, specific keywords; avoid buzzwords and superlatives.

- Active voice; avoid filler (e.g., "authentic", "best in town").

- Prefer a single sentence with a clean separator (• or —) if needed.

- 0–1 emoji max only if clearly on-brand.

- Use local context over exact street address when possible (e.g., neighborhood names).

- Include dietary/accessibility flags if explicitly indicated (e.g., "GF options", "vegetarian-friendly").

- Use numerals to save space where natural (e.g., "24/7").

- Output ONLY the final description in the target language, nothing else.



Heuristics for inputs:

- Restaurant Category informs cuisine/style terms.

- From Address, prefer city or known neighborhood names; omit full street unless locally meaningful or helps brevity.

- From Local Keywords, pick 1–2 concrete, factual hooks; avoid repetition with Category.



Language: rewrite in the language of the provided description. Do not mix languages unless clearly brand-standard in inputs.



Output:

Return the optimized description only.
            """
        

        prompt = f"""
        **Original description** : {description}
        **Restaurant Name**: {restaurant_name}
        **Restaurant Category**: {restaurant_category}
        **Address**: {address}
        **List of Local Keywords**: {', '.join(bricks)}
        """

        return context, prompt
