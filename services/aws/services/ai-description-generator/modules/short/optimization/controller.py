from modules.short.optimization.handler import TextOptimizer
from modules.short.optimization.model import (
    DescriptionOptimizationPayload,
    TextOptimization,
)


class TextOptimizerController:

    def __init__(
        self,
        textGenerator: TextOptimizer = TextOptimizer(),
    ):
        self._generate = textGenerator

    def optimize(self, event: DescriptionOptimizationPayload) -> TextOptimization:
        return self._generate.execute(payload=event)
