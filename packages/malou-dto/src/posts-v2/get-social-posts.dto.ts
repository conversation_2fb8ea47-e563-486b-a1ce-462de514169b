import { z } from 'zod';

import {
    AspectRatio,
    DeviceType,
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    PostType,
    PublicationErrorCode,
    SocialPostsListFilter,
} from '@malou-io/package-utils';

import { restaurantIdParamsTransformValidator } from '../common';
import { MediaDimensionDto } from '../media';
import { PostHashtagsDto } from './post-hashtags.dto';

/** An item in the list of social posts */
export interface SocialPostItemDto {
    id: string;
    title?: string;
    text: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    isPublishing: boolean;
    postType: PostType;
    feedbackMessageCount: number;
    plannedPublicationDate: Date | null;

    /**
     * This is displayed as a small thumbnail actually, but we display media.thumbnailUrl
     * in priority (rather than media.url) so it should be fine if media.url is a large video.
     */
    media: SocialPostMediaDto | null;

    hashtags: PostHashtagsDto;
    socialLink?: string;
    socialCreatedAt?: Date;
    sortDate: Date;
    author?: PostAuthorDto;
    mostRecentPublicationErrorCode?: PublicationErrorCode;
    bindingId?: string;
    createdFromDeviceType?: DeviceType;
}

export interface TransformDto {
    aspectRatio: AspectRatio;
    rotationInDegrees: number;
    left: number;
    top: number;
    width: number;
    height: number;
}

export interface SocialPostMediaDto {
    url: string;
    dimensions?: MediaDimensionDto;
    type: MediaType;
    thumbnailUrl?: string; // some v1 media video don't have thumbnail
    thumbnailDimensions?: MediaDimensionDto;
    transformData?: TransformDto;
    duration?: number;
    aspectRatio?: number;
}

export interface PostAuthorDto {
    id: string;
    name?: string;
    lastname?: string;
    picture?: string | null;
}

export const getSocialPostsQueryValidator = z
    .object({
        cursor: z.string().datetime().nullish(),
        limit: z.coerce.number().int().positive().nullish(),
        filter: z.nativeEnum(SocialPostsListFilter).nullish(),
    })
    .transform((data) => ({
        cursor: data.cursor ? new Date(data.cursor) : null,
        limit: data.limit ?? null,
        filter: data.filter ?? SocialPostsListFilter.ALL,
    }));

export type GetSocialPostsQueryDto = z.infer<typeof getSocialPostsQueryValidator>;

export const getSocialPostsParamsValidator = restaurantIdParamsTransformValidator;
export type GetSocialPostsParamsDto = z.infer<typeof getSocialPostsParamsValidator>;
