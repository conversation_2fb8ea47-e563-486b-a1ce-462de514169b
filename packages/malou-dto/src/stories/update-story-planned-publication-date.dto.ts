import z from 'zod';

import { RecurrentStoryFrequency } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils';

export const updateStoryPlannedPublicationDateBodyValidator = z
    .object({
        plannedPublicationDate: z.string().datetime(),
        recurrentStoryFrequency: z.nativeEnum(RecurrentStoryFrequency),
    })
    .transform((data) => ({
        plannedPublicationDate: new Date(data.plannedPublicationDate),
        recurrentStoryFrequency: data.recurrentStoryFrequency,
    }));
export type UpdateStoryPlannedPublicationDateBodyDto = z.infer<typeof updateStoryPlannedPublicationDateBodyValidator>;

export const updateStoryPlannedPublicationDateParamsValidator = z
    .object({
        story_id: objectIdValidator,
    })
    .transform((data) => ({ storyId: data.story_id }));
export type UpdateStoryPlannedPublicationDateParamsDto = z.infer<typeof updateStoryPlannedPublicationDateParamsValidator>;
