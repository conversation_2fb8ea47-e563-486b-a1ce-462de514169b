import z from 'zod';

import { DeviceType, PlatformKey, PostPublicationStatus, PublicationErrorCode, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { postFeedbacksValidator, postMediaValidator, userTagsListValidator } from '../posts-v2';
import { objectIdValidator } from '../utils';

export const storyDtoValidator = z
    .object({
        id: objectIdValidator,
        platformKeys: z.array(z.nativeEnum(PlatformKey)),
        published: z.nativeEnum(PostPublicationStatus),
        isPublishing: z.boolean().optional(),
        feedbacks: postFeedbacksValidator.nullish(),
        plannedPublicationDate: z.string().datetime(),
        medias: z.array(z.object({ uploadedMedia: postMediaValidator, editedMedia: postMediaValidator.optional() })),
        userTagsList: userTagsListValidator,
        author: z
            .object({
                id: objectIdValidator,
                name: z.string().optional(),
                lastname: z.string().optional(),
                picture: z.string().nullish(),
            })
            .nullish(),
        bindingId: z.string().optional(),
        createdFromDeviceType: z.nativeEnum(DeviceType).nullish(),
        mostRecentPublicationErrorCode: z.nativeEnum(PublicationErrorCode).optional(),
        socialLink: z.string().optional(),
        socialCreatedAt: z.string().datetime().optional(),
        recurrentStoryFrequency: z.nativeEnum(RecurrentStoryFrequency).optional(),
    })
    .transform((data) => ({
        ...data,
        isPublishing: data.isPublishing ?? false,
    }));

export type StoryDto = z.infer<typeof storyDtoValidator>;
