import { z } from 'zod';

import { PlatformKey } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils/validators';

export const updateSubscriptionsProviderLocationParamsValidator = z
    .object({
        restaurant_id: objectIdValidator,
    })
    .transform((data) => ({
        restaurantId: data.restaurant_id,
    }));

export type UpdateSubscriptionsProviderLocationParamsDto = z.infer<typeof updateSubscriptionsProviderLocationParamsValidator>;

export const updateSubscriptionsProviderLocationBodyValidator = z.object({
    platformKey: z.nativeEnum(PlatformKey),
    newSubscriptionsProviderId: z.string(),
});

export type UpdateSubscriptionsProviderLocationBodyDto = z.infer<typeof updateSubscriptionsProviderLocationBodyValidator>;

export const updateSubscriptionsProviderLocationResponseValidator = z.object({
    success: z.boolean(),
    message: z.string().optional(),
});

export type UpdateSubscriptionsProviderLocationResponseDto = z.infer<typeof updateSubscriptionsProviderLocationResponseValidator>;
