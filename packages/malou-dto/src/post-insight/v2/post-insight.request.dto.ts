import { z } from 'zod';

import { PlatformKey } from '@malou-io/package-utils';

import { objectIdValidator } from '../../utils';

export const getRestaurantPostInsightsParamsValidator = z.object({
    restaurantId: objectIdValidator,
});
export type GetRestaurantPostInsightsParamsDto = z.infer<typeof getRestaurantPostInsightsParamsValidator>;

export const getRestaurantPostInsightsBodyValidator = z.object({
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
    platformKeys: z.array(z.enum([PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK])),
});

export type GetRestaurantPostInsightsBodyDto = z.infer<typeof getRestaurantPostInsightsBodyValidator>;

// ---------------------------------------------------------------------------------------------

export const getAggregatedTopPostInsightsBodyValidator = z.object({
    restaurantIds: z.array(objectIdValidator),
    platformKeys: z.array(z.enum([PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK])),
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
});

export type GetAggregatedTopPostInsightsBodyDto = z.infer<typeof getAggregatedTopPostInsightsBodyValidator>;

// ---------------------------------------------------------------------------------------------
export const getAggregatedSocialPostInsightsBodyValidator = z.object({
    restaurantIds: z.array(objectIdValidator),
    platformKeys: z.array(z.enum([PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK])),
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
    previousPeriod: z.boolean().optional().default(false),
});

export type GetAggregatedSocialPostInsightsBodyDto = z.infer<typeof getAggregatedSocialPostInsightsBodyValidator>;

// ---------------------------------------------------------------------------------------------
export const getRestaurantsCsvPostInsightsBodyValidator = z.object({
    restaurantIds: z.array(objectIdValidator),
    platformKeys: z.array(z.enum([PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM, PlatformKey.TIKTOK])),
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
});

export type GetRestaurantsCsvPostInsightsBodyDto = z.infer<typeof getRestaurantsCsvPostInsightsBodyValidator>;
// ---------------------------------------------------------------------------------------------
