import { z } from 'zod';

import { UserCaslRole } from '@malou-io/package-utils';

import { objectIdValidator } from '../utils/validators';

export const organizationValidator = z.object({
    _id: objectIdValidator.nullish(),
    name: z.string().nullish(),
    subscriptionsProviderId: z.string().nullish(),
    verifiedEmailsForCampaigns: z.array(z.string()).nullish(),
    limit: z.number().nullish(),
    createdAt: z.string().nullish(),
    updatedAt: z.string().nullish(),
});

export const organizationSettingsUpdateBodyValidator = z.object({
    caslRole: z.nativeEnum(UserCaslRole),
});

export type OrganizationSettingsUpdateBodyDto = z.infer<typeof organizationSettingsUpdateBodyValidator>;
