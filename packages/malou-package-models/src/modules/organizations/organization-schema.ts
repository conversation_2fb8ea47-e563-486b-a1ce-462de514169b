import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const organizationJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Organization',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        name: {
            type: 'string',
            minLength: 1,
        },
        // This field is related to the external provider which creates the organization
        // It should be Hyperline or Airtable for example
        // It cannot be null as organizations are necessarily created by a provider
        // Right now it is not included in the required fields as there are some organizations
        // that were created before this field was added
        subscriptionsProviderId: {
            type: 'string',
        },
        verifiedEmailsForCampaigns: {
            type: 'array',
            items: {
                type: 'string',
            },
            default: [],
        },
        reviewPublicBusinessIdCount: {
            type: 'number',
            default: 0,
            description: 'Counter for public business IDs used in reviews',
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    required: ['_id', 'name', 'createdAt', 'updatedAt', 'reviewPublicBusinessIdCount'],
    definitions: {},
} as const satisfies JSONSchemaExtraProps;
