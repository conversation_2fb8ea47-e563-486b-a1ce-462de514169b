import { FromSchema } from 'json-schema-to-ts';
import mongoose from 'mongoose';

import { BusinessCategory, PlatformKey } from '@malou-io/package-utils';

import { DESERIALIZE_OPTIONS } from ':core/mongoose-json-schema/definitions/constants';
import { createMongooseSchemaFromJSONSchema } from ':core/mongoose-json-schema/definitions/jsonschema-to-mongoose';
import { RemoveNullOrUndefined } from ':core/mongoose-json-schema/type-utils';
import { DbId } from ':helpers/index';
import { AutomationModel } from ':modules/automations/automation-model';
import { CampaignModel } from ':modules/campaigns/campaign-model';
import { ClientModel } from ':modules/clients/client-model';
import { CommentModel } from ':modules/comments/comment-model';
import { GiftDrawModel } from ':modules/gift-draws/gift-draw-model';
import { GiftStockModel } from ':modules/gift-stocks/gift-stock-model';
import { HashtagModel } from ':modules/hashtags/hashtag-model';
import { InformationUpdateModel } from ':modules/information-updates/information-update-model';
import { KeywordSearchImpressionsModel } from ':modules/keywords-search-impressions/keyword-search-impressions-model';
import { KeywordModel } from ':modules/keywords/keyword-model';
import { LabelModel } from ':modules/labels/label-model';
import { MediaModel } from ':modules/media/media-model';
import { NfcModel } from ':modules/nfcs/nfc-model';
import { NotificationModel } from ':modules/notifications/notification-model';
import { PlatformModel } from ':modules/platforms/platform-model';
import { PostModel } from ':modules/posts/post-model';
import { PrivateReviewModel } from ':modules/private-reviews/private-review-model';
import { RestaurantAiSettingsModel } from ':modules/restaurant-ai-settings/restaurant-ai-settings-model';
import { RestaurantAttributeModel } from ':modules/restaurant-attributes/restaurant-attribute-model';
import { ReviewModel } from ':modules/reviews/review-model';
import { TemplateModel } from ':modules/templates/template-model';
import { UserRestaurantModel } from ':modules/user-restaurants/user-restaurant-model';
import { UserModel } from ':modules/users/user-model';

import { restaurantJSONSchema } from './restaurant-schema';

const restaurantSchema = createMongooseSchemaFromJSONSchema(restaurantJSONSchema);

restaurantSchema.virtual('attributeList', {
    ref: 'RestaurantAttribute',
    localField: '_id',
    foreignField: 'restaurantId',
    justOne: false,
});

restaurantSchema.virtual('managers', {
    ref: 'UserRestaurant',
    localField: '_id',
    foreignField: 'restaurantId',
    justOne: false,
});

restaurantSchema.virtual('informationUpdate', {
    ref: 'InformationUpdates',
    localField: '_id',
    foreignField: 'restaurantId',
    justOne: false,
});

restaurantSchema.virtual('storeLocatorPages', {
    ref: 'StoreLocatorRestaurantPage',
    localField: '_id',
    foreignField: 'restaurantId',
    justOne: false,
});

restaurantSchema.virtual('organization', {
    ref: 'Organization',
    localField: 'organizationId',
    foreignField: '_id',
    justOne: true,
});

restaurantSchema.virtual('availableHoursTypes', {
    ref: 'HoursType',
    localField: 'availableHoursTypeIds',
    foreignField: '_id',
    justOne: false,
});

restaurantSchema.virtual('logoPopulated', {
    ref: 'Media',
    localField: 'logo',
    foreignField: '_id',
    justOne: true,
});

restaurantSchema.virtual('calendarEventsPopulated', {
    ref: 'CalendarEvent',
    localField: 'calendarEvents',
    foreignField: '_id',
    justOne: false,
});

restaurantSchema.virtual('coverPopulated', {
    ref: 'Media',
    localField: 'cover',
    foreignField: '_id',
    justOne: true,
});

restaurantSchema.index({ uniqueKey: 1 }, { unique: true });
restaurantSchema.index({ internalName: 1 }, { unique: true, sparse: true });
// restaurantSchema.index({ subscriptionsProviderId: 1 }, { unique: true, sparse: true });
restaurantSchema.index({ organizationId: 1, active: 1 });

restaurantSchema.index(
    { subscriptionsProviderId: 1 },
    {
        unique: true,
        partialFilterExpression: {
            subscriptionsProviderId: {
                $exists: true,
                $type: 'string',
            },
        },
    }
);

restaurantSchema.index({ name: 1 });
restaurantSchema.index({ 'address.locality': 1 });
restaurantSchema.index({ 'address.formattedAddress': 1 });

// Add this text index for better search capabilities
restaurantSchema.index(
    {
        name: 'text',
        internalName: 'text',
        'address.formattedAddress': 'text',
        'address.locality': 'text',
    },
    {
        weights: {
            name: 10, // Prioritize name matches
            internalName: 8, // Then internal name
            'address.locality': 5, // Then city/locality
            'address.formattedAddress': 3, // Then full address
        },
        name: 'restaurants_search_index',
    }
);

// Used in _getWeeklyGmapsSearchQueries
restaurantSchema.index({ active: 1 }, { partialFilterExpression: { active: true } });

// Used in SynchronizeRecentPostsUseCase
restaurantSchema.index({ active: 1, postsLastUpdate: 1 }, { partialFilterExpression: { active: true } });

restaurantSchema.pre('validate', function (next) {
    if (this.type === BusinessCategory.LOCAL_BUSINESS) {
        if (!this.placeId) {
            next(new Error('placeId required'));
        }
        if (!this.uniqueKey) {
            this.uniqueKey = `${PlatformKey.GMB}_${this.placeId};`;
        }
    } else if (this.type === BusinessCategory.BRAND) {
        if (!this.socialId) {
            next(new Error('socialId required'));
        }
        if (!this.uniqueKey) {
            this.uniqueKey = `${PlatformKey.FACEBOOK}_${this.socialId};`;
        }
    }
    next();
});

// HOOKS #################################
// Handle 11000 duplicate key
restaurantSchema.post('save', async (error: Error, _doc: unknown, next: (error?: Error) => void): Promise<void> => {
    if ('code' in error && error.code === 11000) {
        return next({ duplicateRecordError: true } as any); // FIXME
    }
    return next(error);
});

// MIDLLEWARES #################################
restaurantSchema.pre('deleteOne', async function (next) {
    // https://stackoverflow.com/a/59214455
    const restaurantId = this.getQuery()._id;
    try {
        await MediaModel.deleteMany({ restaurantId }).exec();
        await KeywordModel.deleteMany({ restaurantId }).exec();
        await PlatformModel.deleteMany({ restaurantId }).exec();
        await TemplateModel.deleteMany({ restaurantId }).exec();
        await ReviewModel.deleteMany({ restaurantId }).exec();
        await CommentModel.deleteMany({ restaurantId }).exec();
        await PostModel.deleteMany({ restaurantId }).exec();
        await RestaurantAttributeModel.deleteMany({ restaurantId }).exec();
        await LabelModel.deleteMany({ restaurantId }).exec();
        await HashtagModel.deleteMany({ restaurantId }).exec();
        await UserRestaurantModel.deleteMany({ restaurantId }).exec();
        await ClientModel.deleteMany({ restaurantId }).exec();
        await CampaignModel.deleteMany({ restaurantId }).exec();
        await PrivateReviewModel.deleteMany({ restaurantId }).exec();
        await InformationUpdateModel.deleteMany({ restaurantId }).exec();
        await GiftDrawModel.deleteMany({ restaurantId }).exec();
        await GiftStockModel.deleteMany({ restaurantId }).exec();
        await RestaurantAiSettingsModel.deleteMany({ restaurantId }).exec();
        await NfcModel.deleteMany({ restaurantId }).exec();
        await AutomationModel.deleteMany({ restaurantId }).exec();
        await KeywordSearchImpressionsModel.deleteMany({ restaurantId }).exec();
        await UserModel.updateMany({ lastVisitedRestaurantId: restaurantId }, { lastVisitedRestaurantId: null }).exec();
        await _handleDeleteNotificationsRestaurant(restaurantId);

        // await emptyS3Directory(AWS_BUCKET, `restaurants/${restaurantId}/media`)
    } catch (error: any) {
        console.warn('delete on cascade error', error);
        next(error);
    }
});

export const addressSchemaWithoutIndex = restaurantSchema.path('address').schema.clone().clearIndexes();
export const regularHourSchemaWithoutIndex = restaurantSchema.path('otherHours').schema.path('periods').schema.clone().clearIndexes();
export const otherHourSchemaWithoutIndex = restaurantSchema.path('otherHours').schema.clone().clearIndexes();
export const phoneSchemaWithoutIndex = restaurantSchema.path('phone').schema.clone().clearIndexes();
export const specialHourSchemaWithoutIndex = restaurantSchema.path('specialHours').schema.clone().clearIndexes();

export type IRestaurant = FromSchema<
    typeof restaurantJSONSchema,
    {
        keepDefaultedPropertiesOptional: true;
        deserialize: DESERIALIZE_OPTIONS;
    }
>;

export type IPlatformState = RemoveNullOrUndefined<
    RemoveNullOrUndefined<RemoveNullOrUndefined<RemoveNullOrUndefined<IRestaurant['currentState']>['messages']>['fetched']>['facebook']
>;

export const RestaurantModel = mongoose.model<IRestaurant>(restaurantJSONSchema.title, restaurantSchema);

const _handleDeleteNotificationsRestaurant = async (restaurantId: DbId[]) => {
    const notifications = await NotificationModel.find({ $or: [{ restaurants: restaurantId }, { restaurants: [] }] }).populate<{
        restaurants: IRestaurant[];
    }>({ path: 'restaurants' });
    if (!notifications) {
        return;
    }
    notifications.forEach(async (notif) => {
        if (notif.restaurants.length === 1 && notif.restaurants[0]._id.toString() === restaurantId.toString()) {
            await NotificationModel.findByIdAndUpdate(notif._id, { restaurants: [null] });
        }
    });
};
