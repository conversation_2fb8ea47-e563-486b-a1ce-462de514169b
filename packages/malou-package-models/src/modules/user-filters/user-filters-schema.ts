import { DateTime } from 'luxon';

import { DEFAULT_REVIEWS_RATINGS, MalouComparisonPeriod, MalouPeriod, MalouTimeScalePeriod, PlatformKey } from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const userFiltersJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'UserFilters',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        userId: {
            type: 'string',
            format: 'objectId',
        },
        aggregatedStatistics: {
            $ref: '#/definitions/AggregatedStatistics',
        },
        statisticsPerRestaurant: {
            type: 'array',
            items: {
                type: 'object',
                additionalProperties: false,
                properties: {
                    restaurantId: {
                        type: 'string',
                        format: 'objectId',
                    },
                    filters: {
                        $ref: '#/definitions/FiltersPerRestaurant',
                    },
                },
                required: ['filters', 'restaurantId'],
            },
        },
        aggregatedReviews: {
            $ref: '#/definitions/AggregatedReviews',
        },
        reviewsPerRestaurant: {
            type: 'array',
            items: {
                type: 'object',
                additionalProperties: false,
                properties: {
                    restaurantId: {
                        type: 'string',
                        format: 'objectId',
                    },
                    filters: {
                        $ref: '#/definitions/ReviewsFilters',
                    },
                },
                required: ['filters', 'restaurantId'],
            },
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
    },
    definitions: {
        AggregatedStatistics: {
            type: 'object',
            additionalProperties: false,
            properties: {
                dates: {
                    $ref: '#/definitions/AggregatedStatisticsDates',
                },
                platforms: {
                    $ref: '#/definitions/AggregatedStatsPlatforms',
                },
                restaurantIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                    default: [],
                },
                roiRestaurantIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                    default: [],
                },
                totemIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                    default: [],
                },
                timeScale: {
                    enum: Object.values(MalouTimeScalePeriod),
                    default: MalouTimeScalePeriod.LAST_THIRTY_DAYS,
                },
                comparisonPeriod: {
                    enum: Object.values(MalouComparisonPeriod),
                    default: MalouComparisonPeriod.PREVIOUS_PERIOD,
                },
                monthYearPeriod: {
                    $ref: '#/definitions/MonthYearPeriod',
                    default: {
                        startMonthYear: {
                            month: DateTime.now().minus({ months: 3 }).month,
                            year: DateTime.now().minus({ months: 3 }).year,
                        },
                        endMonthYear: {
                            month: DateTime.now().month,
                            year: DateTime.now().year,
                        },
                    },
                },
            },
            title: 'AggregatedStatistics',
            required: [
                'dates',
                'timeScale',
                'platforms',
                'restaurantIds',
                'roiRestaurantIds',
                'totemIds',
                'monthYearPeriod',
                'comparisonPeriod',
            ],
        },
        AggregatedStatisticsDates: {
            type: 'object',
            additionalProperties: false,
            properties: {
                startDate: {
                    type: 'string',
                    format: 'date-time',
                    default: null,
                },
                endDate: {
                    type: 'string',
                    format: 'date-time',
                    default: null,
                },
                period: {
                    enum: Object.values(MalouPeriod),
                    default: MalouPeriod.LAST_THIRTY_DAYS,
                },
            },
            title: 'AggregatedStatisticsDates',
            required: ['period'],
        },
        AggregatedStatsPlatforms: {
            type: 'object',
            additionalProperties: false,
            properties: {
                BOOSTERS: {
                    type: 'array',
                    items: {
                        enum: Object.values(PlatformKey),
                    },
                    default: [],
                },
                E_REPUTATION: {
                    type: 'array',
                    items: {
                        enum: Object.values(PlatformKey),
                    },
                    default: [],
                },
                SEO: {
                    type: 'array',
                    items: {
                        enum: Object.values(PlatformKey),
                    },
                    default: [],
                },
                SOCIAL_NETWORKS: {
                    type: 'array',
                    items: {
                        enum: Object.values(PlatformKey),
                    },
                    default: [],
                },
            },
            title: 'AggregatedStatsPlatforms',
            required: ['BOOSTERS', 'E_REPUTATION', 'SEO', 'SOCIAL_NETWORKS'],
        },
        Platforms: {
            type: 'object',
            additionalProperties: false,
            properties: {
                E_REPUTATION: {
                    type: 'array',
                    items: {
                        enum: Object.values(PlatformKey),
                    },
                    default: [],
                },
                SOCIAL_NETWORKS: {
                    type: 'array',
                    items: {
                        enum: Object.values(PlatformKey),
                    },
                    default: [],
                },
            },
            title: 'Platforms',
            required: ['E_REPUTATION', 'SOCIAL_NETWORKS'],
        },
        FiltersPerRestaurant: {
            type: 'object',
            additionalProperties: false,
            properties: {
                dates: {
                    $ref: '#/definitions/AggregatedStatisticsDates',
                },
                platforms: {
                    $ref: '#/definitions/Platforms',
                },
                totemIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                    default: [],
                },
                timeScale: {
                    enum: Object.values(MalouTimeScalePeriod),
                    default: MalouTimeScalePeriod.LAST_THIRTY_DAYS,
                },
                comparisonPeriod: {
                    enum: Object.values(MalouComparisonPeriod),
                    default: MalouComparisonPeriod.PREVIOUS_PERIOD,
                },
                monthYearPeriod: {
                    $ref: '#/definitions/MonthYearPeriod',
                    default: {
                        startMonthYear: {
                            month: DateTime.now().minus({ months: 3 }).month,
                            year: DateTime.now().minus({ months: 3 }).year,
                        },
                        endMonthYear: {
                            month: DateTime.now().month,
                            year: DateTime.now().year,
                        },
                    },
                },
            },
            title: 'FiltersPerRestaurant',
            required: ['timeScale', 'platforms', 'dates', 'totemIds', 'comparisonPeriod', 'monthYearPeriod'],
        },
        AggregatedReviews: {
            type: 'object',
            additionalProperties: false,
            properties: {
                filters: {
                    $ref: '#/definitions/ReviewsFilters',
                },
                restaurantIds: {
                    type: 'array',
                    items: {
                        type: 'string',
                        format: 'objectId',
                    },
                    default: [],
                },
            },
            title: 'AggregatedReviews',
            required: ['filters', 'restaurantIds'],
        },
        ReviewsFilters: {
            type: 'object',
            additionalProperties: false,
            properties: {
                period: {
                    enum: Object.values(MalouPeriod),
                    default: MalouPeriod.ALL,
                },
                platforms: {
                    type: 'array',
                    items: {
                        enum: Object.values(PlatformKey),
                    },
                    default: [],
                },
                text: {
                    type: 'string',
                    default: '',
                },
                ratings: {
                    type: 'array',
                    items: {
                        type: 'number',
                    },
                    default: DEFAULT_REVIEWS_RATINGS,
                },
                answered: {
                    type: 'boolean',
                    default: true,
                },
                notAnswered: {
                    type: 'boolean',
                    default: true,
                },
                pending: {
                    type: 'boolean',
                    default: true,
                },
                notAnswerable: {
                    type: 'boolean',
                    default: true,
                },
                showPrivate: {
                    type: 'boolean',
                    default: true,
                },
                withText: {
                    type: 'boolean',
                    default: true,
                },
                withoutText: {
                    type: 'boolean',
                    default: true,
                },
                archived: {
                    type: 'boolean',
                    default: false,
                },
                unarchived: {
                    type: 'boolean',
                    default: true,
                },
            },
            title: 'ReviewsFilters',
            required: [
                'period',
                'platforms',
                'ratings',
                'answered',
                'notAnswered',
                'pending',
                'notAnswerable',
                'showPrivate',
                'withText',
                'withoutText',
                'archived',
                'unarchived',
            ],
        },
        MonthYearPeriod: {
            title: 'MonthYearPeriod',
            type: 'object',
            additionalProperties: false,
            properties: {
                startMonthYear: {
                    type: 'object',
                    additionalProperties: false,
                    properties: { month: { type: 'number' }, year: { type: 'number' } },
                    required: ['month', 'year'],
                },
                endMonthYear: {
                    type: 'object',
                    additionalProperties: false,
                    properties: { month: { type: 'number' }, year: { type: 'number' } },
                    required: ['month', 'year'],
                },
            },
            required: ['startMonthYear', 'endMonthYear'],
        },
    },
    required: [
        '_id',
        'userId',
        'aggregatedStatistics',
        'statisticsPerRestaurant',
        'aggregatedReviews',
        'reviewsPerRestaurant',
        'createdAt',
        'updatedAt',
    ],
} as const satisfies JSONSchemaExtraProps;
