import type { BulkWriteResult } from 'mongodb';
import {
    Aggregate,
    AggregateOptions,
    AnyBulkWriteOperation,
    ClientSession,
    Document,
    FilterQuery,
    Model,
    PipelineStage,
    ProjectionFields,
    QueryOptions,
    RootFilterQuery,
    UpdateQuery,
    UpdateWithAggregationPipeline,
    UpdateWriteOpResult,
} from 'mongoose';
import type { Object, String } from 'ts-toolbelt';

import { VirtualsGetter } from ':modules/virtuals-getter';

import { AppPopulateOption } from './repository-types/app-populate-option';
import { DeepPaths } from './repository-types/deep-paths';
import { FindOneResult } from './repository-types/find-one-result';
import { SortOptions } from './repository-types/query-options';
import { RecursiveOverwriteValue } from './repository-types/recursive-overwrite-value';
import { ExecUpdateReturnType, isExecUpdateReturnType } from './repository-types/update-one';

export type { VirtualsGetter } from ':modules/virtuals-getter';
export type { AppPopulateOption } from './repository-types/app-populate-option';
export type { FindOneResult } from './repository-types/find-one-result';
export type { OverwriteOrAssign } from './repository-types/populate-result';

export enum ReadPreferenceMode {
    PRIMARY = 'primary',
    PRIMARY_PREFERRED = 'primaryPreferred',
    SECONDARY = 'secondary',
    SECONDARY_PREFERRED = 'secondaryPreferred',
    NEAREST = 'nearest',
}

export type MalouProjection<Entity> = ProjectionFields<Entity> | ProjectionFields<VirtualsGetter<Entity>>;

export class EntityRepository<Entity> {
    private readonly config: { returnDocumentAfterUpdate: boolean };
    readonly model: Model<Entity>;

    constructor(
        model: Model<Entity>,
        config: { returnDocumentAfterUpdate: boolean } = {
            returnDocumentAfterUpdate: true,
        }
    ) {
        this.model = model;
        this.config = config;
    }

    /**
     * Creates a new entity in the database.
     *
     * @throws {MongoServerError} If a unique index constraint is violated (e.g., duplicate email).
     */
    async create<
        Options extends {
            lean?: boolean;
        } = object,
    >({
        data,
        options,
    }: {
        data: Partial<Entity>;
        options?: Options;
    }): Promise<NonNullable<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>>> {
        const doc = await this.model.create(data);

        if (options?.lean) {
            return doc.toObject() as NonNullable<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>>;
        }

        return doc as any;
    }

    async createMany<
        Options extends {
            ordered?: boolean;
            lean?: boolean;
            session?: ClientSession;
        } = object,
    >({
        data,
        options,
    }: {
        data: Partial<Entity>[];
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>[]> {
        const docs = await this.model.insertMany(data, {
            ...(options?.ordered && { ordered: options?.ordered }),
            ...(options?.session && { session: options?.session }),
        });

        if (options?.lean) {
            return docs.map((doc) => doc.toObject()) as any;
        }

        return docs as any;
    }

    aggregate<AggregateResultType = any>(pipeline: PipelineStage[], options?: AggregateOptions): Aggregate<AggregateResultType[]> {
        return this.model.aggregate(pipeline, options);
    }

    // TODO: Change type d'operations en un type plus précis
    bulkOperations<T extends Document>({
        operations,
        options,
    }: {
        // eslint-disable-next-line @typescript-eslint/ban-types
        operations: AnyBulkWriteOperation<T extends Document<unknown, any, any> ? any : T extends {} ? T : any>[];
        options?: { ordered?: boolean };
    }): Promise<BulkWriteResult> {
        return this.model.bulkWrite(operations, options) as any;
    }

    findOne<
        Projection extends MalouProjection<Entity>,
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<Entity>>;
            sort?: SortOptions<Entity>;
            readPreference?: ReadPreferenceMode;
        } = object,
    >({
        filter,
        projection,
        options,
    }: {
        filter: FilterQuery<Entity>;
        projection?: Projection | null;
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, Projection, Options> | null> {
        return this.model.findOne(filter, projection, options as QueryOptions<Entity>).exec() as any;
    }

    async findOneOrFail<
        Projection extends MalouProjection<Entity>,
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<Entity>>;
            sort?: SortOptions<Entity>;
            readPreference?: ReadPreferenceMode;
        } = object,
    >({
        filter,
        projection,
        options,
    }: {
        filter: FilterQuery<Entity>;
        projection?: Projection | null;
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, Projection, Options>> {
        const result = (await this.model.findOne(filter, projection, options as QueryOptions<Entity>).exec()) as any;
        if (!result) {
            throw new Error(`${this.model.modelName} not found`);
        }
        return result;
    }

    find<
        Projection extends MalouProjection<Entity>,
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<Entity>>;
            sort?: SortOptions<Entity>;
            readPreference?: ReadPreferenceMode;
            comment?: string;
            limit?: number;
        } = object,
    >({
        filter,
        projection,
        options,
    }: {
        filter: FilterQuery<Entity>;
        projection?: Projection | null;
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, Projection, Options>[]> {
        return this.model.find(filter, projection, options as QueryOptions<Entity>).exec() as any;
    }

    findOneAndUpdate<
        Projection extends MalouProjection<Entity>,
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<Entity>>;
            new?: boolean;
            session?: ClientSession;
            arrayFilters?: { [key: string]: any }[];
        } = object,
    >({
        filter,
        update,
        projection,
        options,
    }: {
        filter: FilterQuery<Entity>;
        update: UpdateQuery<Entity> | UpdateWithAggregationPipeline | undefined;
        projection?: Projection | null;
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, Projection, Options> | null> {
        if ((options as QueryOptions<Entity>)?.upsert) {
            throw new Error('upsert option is banned, please use upsert method');
        }

        return this.model
            .findOneAndUpdate(filter, update, {
                new: options?.new ?? this.config.returnDocumentAfterUpdate,
                ...(projection && { projection }),
                ...options,
            } as QueryOptions<Entity>)
            .exec() as Promise<FindOneResult<Entity, VirtualsGetter<Entity>, Projection, Options> | null>;
    }

    findOneAndUpdateOrFail<
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<Entity>>;
            new?: boolean;
            session?: ClientSession;
        } = object,
    >({
        filter,
        update,
        options,
    }: {
        filter: FilterQuery<Entity>;
        update: UpdateQuery<Entity> | UpdateWithAggregationPipeline | undefined;
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>> {
        if ((options as any)?.upsert) {
            throw new Error('upsert option is banned, please use upsert method');
        }

        const result = this.model
            .findOneAndUpdate(filter, update, {
                new: options?.new ?? this.config.returnDocumentAfterUpdate,
                ...options,
            } as any)
            .exec() as any;

        if (!result) {
            throw new Error(`${this.model.modelName} not found for update`);
        }
        return result;
    }

    async upsert<
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<Entity>>;
            new?: boolean;
            session?: ClientSession;
        } = object,
    >({
        filter,
        update,
        options,
    }: {
        filter: FilterQuery<Entity>;
        update: Partial<Entity>;
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>> {
        if ((options as QueryOptions<Entity>)?.upsert) {
            throw new Error('upsert option is banned, do not try to use it!');
        }
        const target = (await this.model
            .findOneAndUpdate(filter, update, {
                new: options?.new ?? this.config.returnDocumentAfterUpdate,
                ...options,
            } as QueryOptions<Entity>)
            .exec()) as Options['lean'] extends true
            ? FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>
            : ExecUpdateReturnType<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>>;

        // If document is found and updated, return it
        if (isExecUpdateReturnType<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>>(target) && target.value) {
            return target.value;
        }

        if (target) {
            return target as FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>;
        }

        // Note that you **must**
        // pass an array as the first parameter to `create()` if you want to
        // specify options.
        // https://mongoosejs.com/docs/api/model.html#Model.create()
        if (options?.session) {
            const doc = await this.model.create(
                [
                    {
                        ...filter,
                        ...update,
                    },
                ],
                { session: options?.session }
            );
            return this.findOne({ filter: { _id: doc[0]._id }, options }) as any;
        }

        // Else, create it
        const doc = await this.model.create({
            ...filter,
            ...update,
        });
        return this.findOne({ filter: { _id: doc._id }, options }) as any;
    }

    /**
     * Atomic upsert operation that prevents race conditions and duplicate key errors.
     * Use this method when you need to ensure thread-safe upsert operations,
     * especially in high-concurrency scenarios like caching.
     */
    async atomicUpsert<
        Options extends {
            lean?: boolean;
            populate?: AppPopulateOption<VirtualsGetter<Entity>>;
            new?: boolean;
            session?: ClientSession;
        } = object,
    >({
        filter,
        update,
        options,
    }: {
        filter: FilterQuery<Entity>;
        update: Partial<Entity>;
        options?: Options;
    }): Promise<FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>> {
        const result = await this.model
            .findOneAndUpdate(filter, { $set: update }, {
                new: options?.new ?? this.config.returnDocumentAfterUpdate,
                upsert: true,
                ...options,
            } as QueryOptions<Entity>)
            .exec();

        return result as FindOneResult<Entity, VirtualsGetter<Entity>, never, Options>;
    }

    updateOne({
        filter,
        update,
        options,
    }: {
        filter: FilterQuery<Entity>;
        update: UpdateQuery<Entity> | UpdateWithAggregationPipeline;
        options?: Omit<QueryOptions<Entity>, 'upsert'> | null;
    }): Promise<UpdateWriteOpResult> {
        if ((options as QueryOptions<Entity>)?.upsert) {
            throw new Error('upsert option is banned, please use upsert method');
        }

        return this.model.updateOne(filter, update, options).exec();
    }

    updateMany({
        filter,
        update,
        options,
    }: {
        filter: FilterQuery<Entity>;
        update: UpdateQuery<Entity> | UpdateWithAggregationPipeline;
        options?: Omit<QueryOptions<Entity>, 'upsert'> | null;
    }): Promise<UpdateWriteOpResult> {
        if (options?.upsert) {
            throw new Error('upsert option is banned, please use upsert method');
        }

        return this.model.updateMany(filter, update, options).exec();
    }

    countDocuments({
        filter,
        options,
    }: {
        filter: RootFilterQuery<Entity>;
        options?: Omit<QueryOptions<Entity>, 'session'> & { session?: ClientSession };
    }): Promise<number> {
        return this.model.countDocuments(filter, options).exec();
    }

    distinct<Field extends DeepPaths<RecursiveOverwriteValue<Entity>>>(
        field: Field,
        filter?: FilterQuery<Entity>
    ): Promise<Object.Path<Entity, String.Split<Field, '.'>>[]> {
        return this.model.distinct(field, filter).exec() as any;
    }

    deleteOne({
        filter,
        options,
    }: {
        filter: FilterQuery<Entity>;
        options?: { session?: ClientSession };
    }): Promise<{ acknowledged: boolean; deletedCount: number }> {
        return this.model.deleteOne(filter, options).exec();
    }

    deleteMany({
        filter,
        options,
    }: {
        filter: FilterQuery<Entity>;
        options?: { session?: ClientSession };
    }): Promise<{ acknowledged: boolean; deletedCount: number }> {
        return this.model.deleteMany(filter, options).exec();
    }
}
