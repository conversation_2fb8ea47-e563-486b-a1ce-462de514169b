import { DateTime, Interval } from 'luxon';

import { MonthYearPeriod } from '../../date';
import { MalouComparisonPeriod } from './interface';

interface BasicFilters {
    restaurantStartDate?: Date;
    comparisonPeriod: MalouComparisonPeriod;
}
interface GetDateRangeFromMalouComparisonPeriodInput extends BasicFilters {
    dateFilters: {
        startDate: Date;
        endDate: Date;
    };
}

interface GetMonthYearFromMalouComparisonPeriod extends BasicFilters {
    dateFilters: MonthYearPeriod;
}

interface GetDateRangeFromMalouComparisonPeriodOutput {
    startDate: Date | null;
    endDate: Date | null;
}

const ELIGIBLE_COMPARISON_PERIODS_WITHOUT_RESTAURANT_START_DATE = [
    MalouComparisonPeriod.PREVIOUS_PERIOD,
    MalouComparisonPeriod.SAME_PERIOD_LAST_YEAR,
];

export const getDateRangeFromMalouComparisonPeriod = ({
    restaurantStartDate,
    dateFilters,
    comparisonPeriod,
}: GetDateRangeFromMalouComparisonPeriodInput): GetDateRangeFromMalouComparisonPeriodOutput => {
    const { offset } = DateTime.local();
    const startDateLuxon = DateTime.fromJSDate(dateFilters.startDate).plus({ minutes: offset }).setZone('utc');
    const endDateLuxon = DateTime.fromJSDate(dateFilters.endDate).plus({ minutes: offset }).setZone('utc');
    const duration = endDateLuxon.diff(startDateLuxon).as('milliseconds');

    // If no restaurant start date, we can only compare to previous period or last year
    if (!restaurantStartDate || ELIGIBLE_COMPARISON_PERIODS_WITHOUT_RESTAURANT_START_DATE.includes(comparisonPeriod)) {
        switch (comparisonPeriod) {
            case MalouComparisonPeriod.PREVIOUS_PERIOD: {
                const startDateForPreviousPeriod = startDateLuxon.minus({ days: 1 });
                return {
                    startDate: startDateForPreviousPeriod.minus(duration).toJSDate(),
                    endDate: startDateForPreviousPeriod.toJSDate(),
                };
            }
            case MalouComparisonPeriod.SAME_PERIOD_LAST_YEAR: {
                const lastYearStartDate = DateTime.fromObject({
                    year: startDateLuxon.year - 1,
                    month: startDateLuxon.month,
                    day: startDateLuxon.day,
                    hour: 0,
                    minute: 0,
                    second: 0,
                });

                const lastYearEndDate = DateTime.fromObject({
                    year: endDateLuxon.year - 1,
                    month: endDateLuxon.month,
                    day: endDateLuxon.day,
                    hour: 0,
                    minute: 0,
                    second: 0,
                });

                return {
                    startDate: lastYearStartDate.toJSDate(),
                    endDate: lastYearEndDate.toJSDate(),
                };
            }
            default:
                throw new Error('Invalid comparison period without restaurant start date');
        }
    }

    const restaurantStartDateLuxon = DateTime.fromJSDate(restaurantStartDate).setZone('utc');
    switch (comparisonPeriod) {
        case MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR: {
            const restaurantStartMonth = restaurantStartDateLuxon.month;
            let comparisonYear = restaurantStartDateLuxon.year;
            if (startDateLuxon.month < restaurantStartMonth) {
                comparisonYear += 1;
            }
            const firstYearStartDate = DateTime.fromObject({
                year: comparisonYear,
                month: startDateLuxon.month,
                day: startDateLuxon.day,
                hour: 0,
                minute: 0,
                second: 0,
            });

            if (firstYearStartDate.valueOf() < restaurantStartDateLuxon.valueOf()) {
                return {
                    startDate: null,
                    endDate: null,
                };
            }
            const firstYearEndDate = firstYearStartDate.plus(duration);

            return {
                startDate: firstYearStartDate.toJSDate(),
                endDate: firstYearEndDate.toJSDate(),
            };
        }
        case MalouComparisonPeriod.SINCE_START: {
            return {
                startDate: restaurantStartDateLuxon.toJSDate(),
                endDate: restaurantStartDateLuxon.plus(duration).toJSDate(),
            };
        }
        default:
            throw new Error('Invalid comparison period');
    }
};

export const getMonthYearFromMalouComparisonPeriod = ({
    comparisonPeriod,
    dateFilters,
    restaurantStartDate,
}: GetMonthYearFromMalouComparisonPeriod): MonthYearPeriod | null => {
    const { endMonthYear, startMonthYear } = dateFilters;
    const monthsCount = Interval.fromDateTimes(
        DateTime.fromObject({ year: startMonthYear.year, month: startMonthYear.month }),
        DateTime.fromObject({ year: endMonthYear.year, month: endMonthYear.month })
    ).splitBy({ months: 1 }).length;

    if (!restaurantStartDate || comparisonPeriod === MalouComparisonPeriod.PREVIOUS_PERIOD) {
        const previousStart = DateTime.fromObject({ year: startMonthYear.year, month: startMonthYear.month }).minus({
            months: monthsCount + 1,
        });
        const previousEnd = DateTime.fromObject({ year: startMonthYear.year, month: startMonthYear.month }).minus({ months: 1 });
        return {
            startMonthYear: { month: previousStart.month, year: previousStart.year },
            endMonthYear: { month: previousEnd.month, year: previousEnd.year },
        };
    }

    const restaurantStartDateLuxon = DateTime.fromJSDate(restaurantStartDate).setZone('utc');

    switch (comparisonPeriod) {
        case MalouComparisonPeriod.SAME_PERIOD_FIRST_YEAR: {
            const restaurantStartMonth = restaurantStartDateLuxon.month;
            let comparisonYear = restaurantStartDateLuxon.year;
            if (startMonthYear.month < restaurantStartMonth) {
                comparisonYear += 1;
            }
            const firstYearStartDate = DateTime.fromObject({
                year: comparisonYear,
                month: startMonthYear.month,
            });

            if (firstYearStartDate.valueOf() < restaurantStartDateLuxon.valueOf()) {
                return null;
            }
            const firstYearEndDate = firstYearStartDate.plus({ months: monthsCount });

            return {
                startMonthYear: { month: firstYearStartDate.month, year: firstYearStartDate.year },
                endMonthYear: { month: firstYearEndDate.month, year: firstYearEndDate.year },
            };
        }
        case MalouComparisonPeriod.SAME_PERIOD_LAST_YEAR: {
            const lastYearStartDate = DateTime.fromObject({
                year: startMonthYear.year - 1,
                month: startMonthYear.month,
            });

            const lastYearEndDate = DateTime.fromObject({
                year: endMonthYear.year - 1,
                month: endMonthYear.month,
            });

            if (lastYearStartDate.valueOf() < restaurantStartDateLuxon.valueOf()) {
                return null;
            }

            return {
                startMonthYear: { month: lastYearStartDate.month, year: lastYearStartDate.year },
                endMonthYear: { month: lastYearEndDate.month, year: lastYearEndDate.year },
            };
        }
        case MalouComparisonPeriod.SINCE_START: {
            const sinceStartEndDate = restaurantStartDateLuxon.plus({ months: monthsCount });
            return {
                startMonthYear: { month: restaurantStartDateLuxon.month, year: restaurantStartDateLuxon.year },
                endMonthYear: { month: sinceStartEndDate.month, year: sinceStartEndDate.year },
            };
        }
        default:
            throw new Error('Invalid comparison period');
    }
};
