{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "2dd7eeff-07b3-4860-a733-a16f1cc3eb0d", "cache": {"enabled": true, "environment": "all", "path": ".angular/cache"}, "packageManager": "pnpm"}, "newProjectRoot": "projects", "projects": {"malou": {"architect": {"build": {"builder": "@angular-devkit/build-angular:application", "configurations": {"development": {"budgets": [{"maximumWarning": "6kb", "type": "anyComponentStyle"}], "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "namedChunks": false, "optimization": true, "outputHashing": "bundles", "sourceMap": {"scripts": true, "styles": true}, "statsJson": false}, "free": {"budgets": [{"maximumWarning": "6kb", "type": "anyComponentStyle"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.free.ts"}]}, "local": {"budgets": [], "extractLicenses": false, "namedChunks": true, "optimization": false, "outputHashing": "all", "sourceMap": true}, "production": {"budgets": [{"maximumWarning": "6kb", "type": "anyComponentStyle"}], "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "namedChunks": false, "optimization": true, "outputHashing": "bundles", "sourceMap": {"hidden": true, "scripts": true, "styles": true}, "statsJson": false}, "staging": {"budgets": [{"maximumWarning": "6kb", "type": "anyComponentStyle"}], "extractLicenses": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}], "namedChunks": false, "optimization": true, "outputHashing": "bundles", "sourceMap": {"scripts": true, "styles": true}, "statsJson": false}}, "options": {"allowedCommonJsDependencies": ["lodash", "pusher-js", "uuid", "luxon", "j<PERSON><PERSON>", "file-saver", "file-type/core"], "assets": ["src/assets", "src/assets/favicon.ico", "src/manifest.webmanifest"], "browser": "src/main.ts", "extractLicenses": false, "index": "src/index.html", "namedChunks": true, "optimization": false, "outputPath": {"base": "dist", "browser": ""}, "polyfills": ["src/polyfills.ts"], "scripts": ["./node_modules/jquery/dist/jquery.min.js"], "serviceWorker": "ngsw-config.json", "sourceMap": true, "stylePreprocessorOptions": {"includePaths": ["src/styles/malou"]}, "styles": ["src/styles/malou-material-theme.scss", "node_modules/@ctrl/ngx-emoji-mart/picker.css", "src/styles/styles.scss", "node_modules/@pqina/pintura/pintura.css"], "tsConfig": "src/tsconfig.app.json"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "malou:build"}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"exclude": ["**/node_modules/**"], "tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"]}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"local": {"buildTarget": "malou:build:local"}, "production": {"buildTarget": "malou:build:production"}, "staging": {"buildTarget": "malou:build:staging"}}, "options": {"buildTarget": "malou:build", "disableHostCheck": true, "port": 4200}}, "test": {"builder": "@angular/build:unit-test", "options": {"buildTarget": "::development", "runner": "vitest", "tsConfig": "tsconfig.json"}}}, "projectType": "application", "root": "", "sourceRoot": "src"}}, "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "scss", "type": "component"}, "@schematics/angular:directive": {"prefix": "app", "type": "directive"}, "@schematics/angular:guard": {"typeSeparator": "."}, "@schematics/angular:interceptor": {"typeSeparator": "."}, "@schematics/angular:module": {"typeSeparator": "."}, "@schematics/angular:pipe": {"typeSeparator": "."}, "@schematics/angular:resolver": {"typeSeparator": "."}, "@schematics/angular:service": {"type": "service"}}, "version": 1}