import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import {
    AggregatedSocialPostInsightDto,
    AggregatedTopPostInsightDto,
    GetAggregatedSocialPostInsightsBodyDto,
    GetRestaurantsCsvPostInsightsBodyDto,
    PlatformPostInsightResponseDto,
    RestaurantCsvPostInsights,
} from '@malou-io/package-dto';
import { ApiResultV2, PlatformKey } from '@malou-io/package-utils';

import { environment } from ':environments/environment';

@Injectable({
    providedIn: 'root',
})
export class PostInsightsV2Service {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/post-insights/v2`;

    constructor(private readonly _http: HttpClient) {}

    getRestaurantPostInsights$({
        restaurantId,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Observable<PlatformPostInsightResponseDto[]> {
        return this._http
            .post<ApiResultV2<PlatformPostInsightResponseDto[]>>(`${this.API_BASE_URL}/restaurants/${restaurantId}`, {
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
                platformKeys,
            })
            .pipe(map((res) => res.data));
    }

    getAggregatedTopPostInsights$({
        restaurantIds,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantIds: string[];
        startDate: string;
        endDate: string;
        platformKeys: PlatformKey[];
    }): Observable<AggregatedTopPostInsightDto[]> {
        return this._http
            .post<ApiResultV2<AggregatedTopPostInsightDto[]>>(`${this.API_BASE_URL}/aggregated-top-post-insights`, {
                restaurantIds,
                startDate: startDate,
                endDate: endDate,
                platformKeys,
            })
            .pipe(map((res) => res.data));
    }

    getAggregatedSocialPostInsights$({
        restaurantIds,
        startDate,
        endDate,
        platformKeys,
        previousPeriod,
    }: GetAggregatedSocialPostInsightsBodyDto): Observable<AggregatedSocialPostInsightDto> {
        return this._http
            .post<ApiResultV2<AggregatedSocialPostInsightDto>>(`${this.API_BASE_URL}/aggregated-social-post-insights`, {
                restaurantIds,
                startDate: startDate,
                endDate: endDate,
                platformKeys,
                previousPeriod,
            })
            .pipe(map((res) => res.data));
    }

    getRestaurantsCsvPostInsights$(body: GetRestaurantsCsvPostInsightsBodyDto): Observable<RestaurantCsvPostInsights[]> {
        return this._http
            .post<ApiResultV2<RestaurantCsvPostInsights[]>>(`${this.API_BASE_URL}/restaurants-csv-post-insights`, body)
            .pipe(map((res) => res.data));
    }
}
