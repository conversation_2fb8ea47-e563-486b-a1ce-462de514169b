@switch (selectedPreviewPlatform()?.platformKey) {
    @case (PlatformKey.INSTAGRAM) {
        <app-instagram-stories-previews
            [stories]="instagramStories()"
            [accountName]="selectedPreviewPlatform()?.username"
            [profilePictureUrl]="selectedPreviewPlatform()?.profilePictureUrl"
            [showStatus]="showStatus()"
            [isReadonly]="isReadonly()"
            [connectedSocialPlatforms]="connectedSocialPlatforms()"
            [userTagsList]="userTagsList()"
            [userTagsHistory]="userTagsHistory()"
            [toggleMediaScroll$]="toggleMediaScroll$()"
            (addUserTag)="onAddUserTag($event)"
            (removeUserTag)="onRemoveUserTag($event)"></app-instagram-stories-previews>
    }
    @case (PlatformKey.FACEBOOK) {
        <app-facebook-stories-previews
            [stories]="facebookStories()"
            [accountName]="selectedPreviewPlatform()?.username"
            [profilePictureUrl]="selectedPreviewPlatform()?.profilePictureUrl"
            [showStatus]="showStatus()"
            [isReadonly]="isReadonly()"
            [toggleMediaScroll$]="toggleMediaScroll$()"></app-facebook-stories-previews>
    }
}
