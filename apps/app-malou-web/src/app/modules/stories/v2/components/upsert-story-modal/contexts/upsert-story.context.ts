import { moveItemInArray } from '@angular/cdk/drag-drop';
import { computed, DestroyRef, inject, Injectable, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { patchState, signalState } from '@ngrx/signals';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { DateTime } from 'luxon';
import { BehaviorSubject, catchError, combineLatest, EMPTY, forkJoin, map, Observable, of, switchMap, takeUntil, tap } from 'rxjs';

import { GetMediaForEditionResponseDto, UpdateStoryDto } from '@malou-io/package-dto';
import {
    HeapEventName,
    PlatformKey,
    PostFeedbacks,
    PostPublicationStatus,
    PublicationType,
    RecurrentStoryFrequency,
} from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { PlatformsService } from ':core/services/platforms.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { ToastService } from ':core/services/toast.service';
import { MediaService } from ':modules/media/media.service';
import { UpsertSocialPostService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/upsert-social-post.service';
import { UpsertStoryState } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.interface';
import { UpsertStoryService } from ':modules/stories/v2/components/upsert-story.service';
import { IUpsertStory, UpsertStory } from ':modules/stories/v2/models/upsert-story';
import { selectUserInfos } from ':modules/user/store/user.selectors';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { Platform, UserTag } from ':shared/models';

@Injectable()
export class UpsertStoryContext {
    private readonly _mediaService = inject(MediaService);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _platformsService = inject(PlatformsService);
    private readonly _upsertStoryService = inject(UpsertStoryService);
    private readonly _upsertSocialPostService = inject(UpsertSocialPostService);
    private readonly _store = inject(Store);
    private readonly _heapService = inject(HeapService);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _destroyRef = inject(DestroyRef);

    readonly upsertStoryState = signalState<UpsertStoryState>(this._getInitialState(null));
    readonly initialStory: WritableSignal<UpsertStory | null> = signal(null);

    readonly postErrors = computed(() =>
        this._upsertStoryService.getStoriesErrors(this.upsertStoryState.upsertStory(), this.submitPublicationStatus())
    );

    readonly hasErrorOccurredDuringInit = signal(false);

    readonly closeModal$ = new BehaviorSubject<boolean>(false);

    readonly submitPublicationStatus = signal<SubmitPublicationStatus>(SubmitPublicationStatus.SCHEDULE);

    private readonly _disconnectedPlatforms$: BehaviorSubject<Platform[]> = new BehaviorSubject([]);
    private readonly _resetSubscription$ = new BehaviorSubject<void>(undefined);

    readonly profilePictureUrls = signal<Record<PlatformKey, string | undefined>>({} as Record<PlatformKey, string | undefined>);

    init(storyId: string | undefined, disconnectedPlatforms$: BehaviorSubject<Platform[]>, options?: { date?: Date }): void {
        this.initialStory.set(null);
        patchState(this.upsertStoryState, this._getInitialState(null));

        this._setIsLoadingStory(true);
        this.hasErrorOccurredDuringInit.set(false);

        const currentRestaurantId = this._restaurantsService.currentRestaurant._id;

        this._resetSubscription$.next();
        this._disconnectedPlatforms$.next(disconnectedPlatforms$.getValue());
        disconnectedPlatforms$.pipe(takeUntil(this._resetSubscription$)).subscribe((disconnectedPlatforms) => {
            this._disconnectedPlatforms$.next(disconnectedPlatforms);
        });

        (storyId
            ? this._upsertStoryService.getStory$(storyId)
            : this._upsertStoryService.createStory$(currentRestaurantId, options)
        ).subscribe({
            next: (upsertStory) => {
                if (
                    upsertStory?.published === PostPublicationStatus.PUBLISHED ||
                    (upsertStory?.published === PostPublicationStatus.PENDING && upsertStory?.isPublishing)
                ) {
                    return this.closeModal$.next(true);
                }
                if (upsertStory && storyId) {
                    this.initialStory.set(upsertStory);
                } else {
                    this.initialStory.set(null);
                }

                patchState(this.upsertStoryState, this._getInitialState(upsertStory));

                this._initConnectedStoriesPlatforms({
                    isNewStory: !storyId,
                });
                this._initUserTagsHistory(currentRestaurantId);

                this._setIsLoadingStory(false);
            },
            error: (error) => {
                console.error(error);
                this.hasErrorOccurredDuringInit.set(true);
                this._setIsLoadingStory(false);
            },
        });
    }

    autosave(story: IUpsertStory): void {
        if (this.upsertStoryState.isSubmitting()) {
            return;
        }
        this._setIsAutoSaving(true);
        this._updatePost$(story, SubmitPublicationStatus.DRAFT)
            .pipe(catchError((error) => this._handleError(error)))
            .subscribe(() => {
                this._setIsAutoSaving(false);
            });
    }

    deleteStory$(): Observable<null> {
        const storyId = this.upsertStoryState.upsertStory.id();
        this._setIsSubmitting(true);
        return this._upsertStoryService.deleteStory$(storyId).pipe(
            map(() => null),
            catchError(() => {
                this._toastService.openErrorToast(this._translateService.instant('social_posts.error_delete_post'));
                this._setIsSubmitting(false);
                return of(null);
            })
        );
    }

    saveStory$(story: IUpsertStory, submitPublicationStatus: SubmitPublicationStatus): Observable<UpsertStory | null> {
        this._setIsSubmitting(true);
        const currentUser = this._store.selectSignal(selectUserInfos);
        this._heapService.track(HeapEventName.STORIES_UPDATE, {
            userId: currentUser()?.id,
            email: currentUser()?.email,
            restaurantId: this._restaurantsService.currentRestaurant._id,
            storyId: story.id,
            submitPublicationStatus,
        });
        return this._updatePost$(story, submitPublicationStatus).pipe(
            catchError((error) => {
                this._setIsSubmitting(false);
                this._handleError(error);
                return of(null);
            })
        );
    }

    selectPlatforms(platformKeys: PlatformKey[]): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, upsertStory: { ...state.upsertStory, platformKeys } }));
    }

    setDuplicateToOtherRestaurants(value: boolean): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, duplicateToOtherRestaurants: value }));
    }

    updatePlannedPublicationDate(date: Date | null): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, upsertStory: { ...state.upsertStory, plannedPublicationDate: date } }));
    }

    updateRecurrentStoryFrequency(value: RecurrentStoryFrequency): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, upsertStory: { ...state.upsertStory, recurrentStoryFrequency: value } }));
    }

    updateFeedbacks(feedbacks: null | PostFeedbacks): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, upsertStory: { ...state.upsertStory, feedbacks } }));
    }

    reorderMedias(previousIndex: number, currentIndex: number): void {
        const medias = this.upsertStoryState.upsertStory.medias();
        const reorderedMedias = [...medias];
        moveItemInArray(reorderedMedias, previousIndex, currentIndex);
        this.updateMedias(reorderedMedias);
    }

    addMedia(media: { uploadedMedia: GetMediaForEditionResponseDto; editedMedia?: GetMediaForEditionResponseDto }, index?: number): void {
        const medias = this.upsertStoryState.upsertStory.medias();
        const newMedias = index === undefined ? [...medias, media] : [...medias.slice(0, index + 1), media, ...medias.slice(index + 1)];
        this.updateMedias(newMedias);
    }

    attachEditedMediaToUploadedMedia(uploadedMediaId: string, editedMedia: GetMediaForEditionResponseDto): void {
        const medias = this.upsertStoryState.upsertStory.medias();
        const newMedias = medias.map((media) => {
            if (media.uploadedMedia.id === uploadedMediaId) {
                return { ...media, editedMedia };
            }
            return media;
        });
        this.updateMedias(newMedias);
    }

    duplicateMedia(mediaId: string, index: number): void {
        const publicationType = PublicationType.STORY;
        const restaurantId = this._restaurantsService.currentRestaurant._id;

        this._mediaService
            .duplicateMediaForPublication({ mediaId }, { restaurantIds: [restaurantId], publicationType })
            .pipe(
                switchMap(({ data }) => {
                    const newMediaId = data[0].duplicatedMediaId;
                    return this._mediaService.getMediaForEdition({ mediaId: newMediaId }, { publicationType });
                })
            )
            .subscribe({
                next: (newMedia) => {
                    this.addMedia({ uploadedMedia: newMedia.data }, index);
                },
                error: (error) => {
                    console.error(error);
                    this._toastService.openErrorToast(this._translateService.instant('stories.error_duplicate_media'));
                },
            });
    }

    removeMedia(uploadedMediaId: string): void {
        const newMedias = this.upsertStoryState.upsertStory.medias().filter((m) => m.uploadedMedia.id !== uploadedMediaId);
        this.updateMedias(newMedias);
    }

    updateMedias(medias: { uploadedMedia: GetMediaForEditionResponseDto; editedMedia?: GetMediaForEditionResponseDto }[]): void {
        const userTagsListReset: IUpsertStory['userTagsList'] = medias.map(() => []);

        const currentUserTagsLists = this.upsertStoryState.upsertStory.userTagsList();
        const currentMedias = this.upsertStoryState.upsertStory.medias();
        if (currentUserTagsLists.length === currentMedias.length) {
            for (const [index, currentMedia] of currentMedias.entries()) {
                const newIndex = medias.findIndex((media) => media.uploadedMedia.id === currentMedia.uploadedMedia.id);
                if (newIndex !== -1) {
                    userTagsListReset[newIndex] = currentUserTagsLists[index];
                }
            }
        }

        patchState(this.upsertStoryState, (state) => ({
            ...state,
            upsertStory: { ...state.upsertStory, medias, userTagsList: userTagsListReset },
        }));
    }

    addUserTag(mediaIdx: number, userTag: UserTag): void {
        const userTagsList = [...this.upsertStoryState.upsertStory.userTagsList()];
        if (userTagsList[mediaIdx]) {
            userTagsList[mediaIdx]!.push(userTag);
        } else {
            userTagsList[mediaIdx] = [userTag];
        }
        patchState(this.upsertStoryState, (state) => ({ ...state, upsertStory: { ...state.upsertStory, userTagsList } }));
    }

    removeUserTag(mediaIdx: number, username: string): void {
        const userTagsList = [...this.upsertStoryState.upsertStory.userTagsList()];
        if (userTagsList[mediaIdx]) {
            userTagsList[mediaIdx] = userTagsList[mediaIdx]!.filter((userTag) => userTag.username !== username);
            patchState(this.upsertStoryState, (state) => ({ ...state, upsertStory: { ...state.upsertStory, userTagsList } }));
        }
    }

    private _getInitialState(initialStory: UpsertStory | null): UpsertStoryState {
        return {
            upsertStory: initialStory ? initialStory.toInterface() : UpsertStory.create().toInterface(),
            isAutoSaving: false,
            isSubmitting: false,
            isLoadingStories: false,
            duplicateToOtherRestaurants: false,
            connectedSocialPlatforms: [],
            userTagsHistory: [],
        };
    }

    private _initConnectedStoriesPlatforms(options: { isNewStory: boolean }): void {
        const connectedPlatforms$ = this._upsertStoryService.getConnectedStoriesPlatforms$();

        combineLatest([this._disconnectedPlatforms$, connectedPlatforms$])
            .pipe(
                map(([disconnectedPlatforms, connectePlatforms]) =>
                    connectePlatforms.filter((platform) => !disconnectedPlatforms.some((p) => p.key === platform.key))
                ),
                tap((platforms) => {
                    const connectedPlatformKeys = platforms.map((platform) => platform.key);
                    patchState(this.upsertStoryState, (state) => ({
                        ...state,
                        connectedSocialPlatforms: platforms,
                    }));
                    if (options.isNewStory) {
                        this.selectPlatforms(connectedPlatformKeys);
                    }
                }),
                switchMap((platforms) =>
                    forkJoin(
                        platforms.map((platform) =>
                            forkJoin([
                                this._platformsService.getProfilePictureUrl(this._restaurantsService.currentRestaurant.id, platform.key),
                                of(platform.key),
                            ])
                        )
                    )
                ),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe((results) => {
                this.profilePictureUrls.set(
                    results.reduce(
                        (acc, result) => ({ ...acc, [result[1]]: result[0].data.profilePictureUrl }),
                        {} as Record<PlatformKey, string | undefined>
                    )
                );
            });
    }

    private _initUserTagsHistory(restaurantId: string): void {
        this._upsertSocialPostService.getUserTagsHistory$(restaurantId).subscribe((result) => {
            patchState(this.upsertStoryState, (state) => ({ ...state, userTagsHistory: result.data }));
        });
    }

    private _updatePost$(story: IUpsertStory, submitPublicationStatus: SubmitPublicationStatus): Observable<UpsertStory> {
        const storyToUpdate = this._getStoryToUpdate(story, submitPublicationStatus);
        return this._upsertStoryService.updateStory$(storyToUpdate);
    }

    private _setIsLoadingStory(isLoadingStories: boolean): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, isLoadingStories }));
    }

    private _setIsSubmitting(isSubmitting: boolean): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, isSubmitting }));
    }

    private _setIsAutoSaving(isAutoSaving: boolean): void {
        patchState(this.upsertStoryState, (state) => ({ ...state, isAutoSaving }));
    }

    private _handleError(_error: unknown): Observable<never> {
        this._toastService.openErrorToast(this._translateService.instant('social_posts.update_post.error'));
        return EMPTY;
    }

    private _getStoryToUpdate(story: IUpsertStory, submitPublicationStatus: SubmitPublicationStatus): UpdateStoryDto {
        const updateStoryDto = new UpsertStory(story).toUpdateStoryDto();
        switch (submitPublicationStatus) {
            case SubmitPublicationStatus.DRAFT:
                return { ...updateStoryDto, published: PostPublicationStatus.DRAFT };
            case SubmitPublicationStatus.SCHEDULE:
                return { ...updateStoryDto, published: PostPublicationStatus.PENDING };
            case SubmitPublicationStatus.NOW:
                return {
                    ...updateStoryDto,
                    published: PostPublicationStatus.PENDING,
                    plannedPublicationDate: DateTime.now().toJSDate().toISOString(), // We used to add +1 minute to the date, but it has been removed to avoid confusion
                    isPublishing: true, // We anticipate the isPublishing state for UX reasons
                };
        }
    }
}
