<div class="group relative flex aspect-9/16 h-full w-full justify-center bg-malou-color-background-dark" #previewContainer>
    <div class="absolute left-2 top-2 z-10 flex w-[96%] gap-x-2">
        @for (story of storiesForPreview(); track story.id + story.medias[0].url; let i = $index) {
            <div
                class="h-[3px] grow rounded-[4px]"
                [ngClass]="{ 'bg-white': i < currentStoryIdx(), 'bg-white/[.35]': i >= currentStoryIdx() }">
                @if (i === currentStoryIdx()) {
                    <div class="h-full rounded-full bg-white" [style]="{ width: progressPercentage() }"></div>
                }
            </div>
        }
    </div>

    <div class="absolute top-[20px] z-10 flex w-full justify-between px-4">
        <div class="flex items-center gap-x-2 text-white">
            @if (accountName(); as accountName) {
                <img class="h-8 w-8 rounded-full" [src]="profilePictureUrl() ?? ('default_logo' | imagePathResolver)" />
                <span class="malou-text-14--semibold">{{ accountName }}</span>
            }
            @if (currentStory(); as currentStory) {
                @if (currentStory | applySelfPure: 'isActive') {
                    @let remainingHours = currentStory | applySelfPure: 'getRemainingHours';
                    <span class="malou-text-14--regular">
                        {{ remainingHours }} {{ 'common.hours' | pluralTranslate: remainingHours | lowercase }}
                    </span>
                }
            }
        </div>

        <div class="flex items-center gap-x-1">
            @if (currentStory(); as currentStory) {
                @if (([PostPublicationStatus.DRAFT, PostPublicationStatus.PENDING] | includes: currentStory.published) && showStatus()) {
                    <ng-container
                        [ngTemplateOutlet]="storyStatusTemplate"
                        [ngTemplateOutletContext]="{
                            date: currentStory | applySelfPure: 'getPostDate',
                            published: currentStory.published,
                            isActive: currentStory | applySelfPure: 'isActive',
                            isPublishing: currentStory.isPublishing,
                        }"></ng-container>
                }
            }

            <mat-icon class="!h-4 !w-4 !fill-white" color="white" [svgIcon]="SvgIcon.ELLIPSIS"></mat-icon>
        </div>
    </div>

    @if (!storiesForPreview().length) {
        <ng-container
            [ngTemplateOutlet]="imageMediaTemplate"
            [ngTemplateOutletContext]="{ url: 'default_post' | imagePathResolver, objectCover: false }"></ng-container>
    } @else {
        @let currentStoryMediaValue = currentStoryMedia();
        @if (currentStoryMediaValue) {
            @switch (currentStoryMediaValue.type) {
                @case (MediaType.PHOTO) {
                    <ng-container
                        [ngTemplateOutlet]="imageMediaTemplate"
                        [ngTemplateOutletContext]="{ url: currentStoryMediaValue.url, objectCover: true }"></ng-container>
                }
                @case (MediaType.VIDEO) {
                    <ng-container [ngTemplateOutlet]="videoMediaTemplate"></ng-container>
                }
            }
        } @else {
            <ng-container
                [ngTemplateOutlet]="imageMediaTemplate"
                [ngTemplateOutletContext]="{ url: 'default_post' | imagePathResolver, objectCover: false }"></ng-container>
        }
    }

    @if (storiesForPreview().length > 1) {
        <div class="absolute left-0 top-[45%] z-10 flex w-full translate-y-[50%] transform px-3">
            <mat-icon
                class="!h-6 !w-6 cursor-pointer !text-malou-color-text-white"
                [svgIcon]="SvgIcon.CHEVRON_LEFT"
                (click)="previousMedia(); $event.stopPropagation()"></mat-icon>
            <mat-icon
                class="!ml-auto !h-6 !w-6 cursor-pointer !text-malou-color-text-white"
                [svgIcon]="SvgIcon.CHEVRON_RIGHT"
                (click)="nextMedia(); $event.stopPropagation()"></mat-icon>
        </div>
    }

    @if (addTagAccount() && addAccountAvailable()) {
        <app-tag-account-v2
            class="absolute"
            [ngStyle]="addTagAccountPosition()"
            [tagControl]="tagControl"
            [searching]="searching()"
            [foundAccount]="foundAccount()"
            [tagPosition]="tagPosition()"
            [showArrowAbove]="showArrowAbove()"
            [userTagsHistory]="userTagsHistory()"
            (onAccountSelected)="addAccount($event)"
            (onClose)="playMediaScroll(); closeAddTagAccount()"></app-tag-account-v2>
    }

    @if (canAddTagAccount()) {
        <div class="absolute bottom-3 left-3 flex gap-x-2">
            @if (!isReadonly()) {
                <div
                    class="flex cursor-pointer items-center gap-x-1 rounded-[5px] bg-malou-color-background-text-1 p-2 text-white"
                    (click)="pauseMediaScroll(); openAddTagAccount($event); showTaggedAccounts.set(false)">
                    <mat-icon class="!h-[13px] !w-[13px] fill-white" [svgIcon]="SvgIcon.PROFILE"></mat-icon>
                    <div class="malou-text-12--regular">
                        {{ 'social_posts.upsert_social_post_modal.previews_feed_notes.tabs.previews.instagram.tag_an_account' | translate }}
                    </div>
                </div>
            }

            @if (userTags(); as userTags) {
                @if (userTags.length === 1) {
                    <div class="flex items-center gap-x-1 rounded-[5px] bg-malou-color-background-text-1 p-2 text-white">
                        <div class="malou-text-12--regular">{{ '@' + userTags[0].username }}</div>
                        @if (!isReadonly()) {
                            <mat-icon
                                class="!h-3 !w-3 cursor-pointer fill-white"
                                [svgIcon]="SvgIcon.CROSS"
                                (click)="removeAccount(userTags[0].username)"></mat-icon>
                        }
                    </div>
                } @else if (userTags.length > 1) {
                    <div
                        class="relative flex cursor-pointer items-center rounded-[5px] bg-malou-color-background-text-1 p-2 text-white"
                        (click)="onShowTaggedAccounts()"
                        #taggedAccountsContainer>
                        <div class="malou-text-12--regular">
                            {{ 'stories.previews.tagged_accounts' | translate: { count: userTags.length } }}
                        </div>

                        @if (showTaggedAccounts()) {
                            <ng-container
                                [ngTemplateOutlet]="taggedAccountsTemplate"
                                [ngTemplateOutletContext]="{ userTags }"></ng-container>
                        }
                    </div>
                }
            }
        </div>
    }
</div>

<ng-template let-url="url" let-objectCover="objectCover" #imageMediaTemplate>
    <img
        class="aspect-9/16 min-w-full"
        [ngClass]="{ 'object-cover': objectCover, 'object-contain': !objectCover }"
        [src]="url"
        (error)="onImgError($event)" />
</ng-template>

<ng-template #videoMediaTemplate>
    <video class="aspect-9/16 min-w-full object-cover" autoplay [muted]="true" (ended)="onVideoEnd()" (click)="onVideoClick()" #videoMedia>
        <source type="video/mp4" [src]="currentStoryMedia()?.url" />
    </video>
</ng-template>

<ng-template let-date="date" let-published="published" let-isActive="isActive" let-isPublishing="isPublishing" #storyStatusTemplate>
    <div
        class="malou-text-10--semibold flex h-6 items-center gap-x-[2px] rounded-[3px] px-2 text-white"
        [ngClass]="{
            'bg-malou-color-background-warning-darker/60': published === PostPublicationStatus.PENDING && !isPublishing,
            'bg-malou-color-background-lavender-light': published === PostPublicationStatus.PENDING && isPublishing,
            'bg-malou-color-primary/60': published === PostPublicationStatus.DRAFT,
        }">
        <span>
            @if (published === PostPublicationStatus.PENDING && isPublishing) {
                <app-malou-spinner size="xs" color="#AC32B7"></app-malou-spinner>
                <div>{{ 'social_post.is_publishing' | translate }}</div>
            } @else {
                {{ published | enumTranslate: 'publication_status' }}
            }
        </span>

        <span>{{ date | date: 'shortDate' }} - {{ date | date: 'shortTime' }}</span>
    </div>
</ng-template>

<ng-template let-userTags="userTags" #taggedAccountsTemplate>
    <div class="absolute -left-20 bottom-12 z-10 w-[250px] cursor-default rounded-[10px] bg-white px-4.5 py-3.5">
        <div class="flex justify-between">
            <div class="malou-text-12--semibold text-malou-color-text-1">{{ 'stories.previews.tagged_accounts_title' | translate }}</div>
            <div class="ml-2">
                <mat-icon
                    class="!h-4 !w-4 cursor-pointer text-malou-color-primary"
                    [svgIcon]="SvgIcon.CROSS"
                    (click)="hideTaggedAccounts(); $event.stopPropagation()"></mat-icon>
            </div>
        </div>

        <div class="flex flex-col gap-1">
            @for (userTag of userTags; track userTag.username) {
                <div class="flex items-center justify-between rounded-[5px] border border-malou-color-border-primary p-2">
                    <div class="flex items-center gap-x-3">
                        <img
                            class="h-7.5 w-7.5 rounded-full"
                            [defaultImage]="'default-picture-grey' | illustrationPathResolver"
                            [lazyLoad]="userTag.profilePicture ?? ''"
                            [errorImage]="'default-picture-grey' | illustrationPathResolver" />
                        <span
                            class="malou-text-10--semibold text-malou-color-text-2"
                            [matTooltip]="userTag.username.length > 16 ? (userTag.username | shortText: 200) : ''">
                            {{ '@' }}{{ userTag.username | shortText: 16 }}
                        </span>
                    </div>

                    @if (!isReadonly()) {
                        <div
                            class="flex h-6 w-6 cursor-pointer items-center justify-center rounded-[5px] hover:bg-malou-color-chart-pink--light"
                            (click)="removeAccount(userTag.username)">
                            <mat-icon class="!h-3 !w-3 text-malou-color-state-error" [svgIcon]="SvgIcon.TRASH"></mat-icon>
                        </div>
                    }
                </div>
            }
        </div>

        <div class="absolute -bottom-2 left-28 rotate-180" id="triangle"></div>
    </div>
</ng-template>
