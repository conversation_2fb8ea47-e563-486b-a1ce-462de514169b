import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { Observable } from 'rxjs';

import { MetaStoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/meta-stories-previews/meta-stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';

@Component({
    selector: 'app-facebook-stories-previews',
    templateUrl: './facebook-stories-previews.component.html',
    styleUrls: ['./facebook-stories-previews.component.scss'],
    imports: [MetaStoriesPreviewsComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FacebookStoriesPreviewsComponent {
    readonly stories = input.required<StoryItem[]>();
    readonly accountName = input<string>();
    readonly profilePictureUrl = input<string>();
    readonly showStatus = input<boolean>(true);
    readonly isReadonly = input.required<boolean>();
    readonly toggleMediaScroll$ = input<Observable<0 | 1>>();
}
