import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { Observable } from 'rxjs';

import { IGAccount, PostUserTag } from '@malou-io/package-utils';

import { MetaStoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/meta-stories-previews/meta-stories-previews.component';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { Platform } from ':shared/models/platform';

@Component({
    selector: 'app-instagram-stories-previews',
    templateUrl: './instagram-stories-previews.component.html',
    styleUrls: ['./instagram-stories-previews.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [MetaStoriesPreviewsComponent],
})
export class InstagramStoriesPreviewsComponent {
    readonly stories = input.required<StoryItem[]>();
    readonly accountName = input<string>();
    readonly profilePictureUrl = input<string>();
    readonly showStatus = input<boolean>(true);
    readonly isReadonly = input.required<boolean>();
    readonly connectedSocialPlatforms = input.required<Platform[]>();
    readonly userTagsList = input<(PostUserTag[] | null)[]>([]);
    readonly userTagsHistory = input<{ username: string; count: number; igAccount: IGAccount }[]>([]);
    readonly toggleMediaScroll$ = input<Observable<0 | 1>>();

    readonly addUserTag = output<{ index: number; userTag: PostUserTag }>();
    readonly removeUserTag = output<{ index: number; username: string }>();

    onAddUserTag(event: { index: number; userTag: PostUserTag }): void {
        this.addUserTag.emit(event);
    }

    onRemoveUserTag(event: { index: number; username: string }): void {
        this.removeUserTag.emit(event);
    }
}
