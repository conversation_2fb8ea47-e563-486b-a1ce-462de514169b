import { ChangeDetectionStrategy, Component, computed, effect, inject, model, output, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { CaslAction, CaslSubject, StoriesListFilter } from '@malou-io/package-utils';

import { StoriesContext } from ':modules/stories/v2/stories.context';
import { StoriesService } from ':modules/stories/v2/stories.service';
import { ButtonComponent } from ':shared/components/button/button.component';
import { PostsListHeaderComponent } from ':shared/components/posts-v2/posts-list-header/posts-list-header.component';
import { DuplicationDestination } from ':shared/enums/duplication-destination.enum';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CaslAblePipe } from ':shared/pipes/casl-able.pipe';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';

@Component({
    selector: 'app-stories-list-header',
    templateUrl: './stories-list-header.component.html',
    styleUrls: ['./stories-list-header.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CaslAblePipe, MatButtonModule, MatIconModule, MatMenuModule, TranslateModule, ButtonComponent, PostsListHeaderComponent],
    providers: [EnumTranslatePipe],
})
export class StoriesListHeaderComponent {
    readonly selectedFilter = model.required<StoriesListFilter>();
    readonly createStory = output<void>();
    readonly duplicateToOtherRestaurants = output<{ postRefs: { id: string }[] }>();

    private readonly _storiesService = inject(StoriesService);
    private readonly _storiesContext = inject(StoriesContext);
    private readonly _translateService = inject(TranslateService);
    private readonly _enumTranslatePipe = inject(EnumTranslatePipe);

    readonly CaslAction = CaslAction;
    readonly CaslSubject = CaslSubject;
    readonly SvgIcon = SvgIcon;
    readonly StoriesListFilter = StoriesListFilter;
    readonly DuplicationDestination = DuplicationDestination;

    readonly filterOptionsAndCount = signal<{ filterOption: StoriesListFilter; count: number | null }[]>([]);

    readonly atLeastOnePostInList = computed(() => this._storiesContext.sortedStories().length > 0);
    readonly isSelecting = this._storiesContext.isSelecting;
    readonly selectedStories = this._storiesContext.storySelection.getSelectionAsSignal();

    readonly totalPostsCount = computed(() => {
        const filterOption = this.selectedFilter();
        return this.filterOptionsAndCount().find((filterOptionCount) => filterOptionCount.filterOption === filterOption)?.count ?? 0;
    });
    readonly allPostsSelected = this._storiesContext.allStoriesSelected;

    readonly duplicateStoriesTooltip = computed(() =>
        this.selectedStories().length
            ? this.selectedStories().length > 10
                ? this._translateService.instant('social_posts.header.max_10_posts_to_duplicate')
                : ''
            : this._translateService.instant('social_posts.header.select_post_to_duplicate_bulk')
    );

    readonly deleteStoriesTooltip = computed(() => {
        const selectedStories = this.selectedStories();

        if (selectedStories.length === 0) {
            return this._translateService.instant('social_posts.header.select_post_to_delete_bulk');
        }

        const deletablePosts = selectedStories.filter((post) => post.canDelete());

        return deletablePosts.length === selectedStories.length
            ? ''
            : this._translateService.instant('stories.header.delete_bulk_not_allowed');
    });

    constructor() {
        effect(() => {
            const restaurant = this._storiesContext.restaurant();
            if (restaurant) {
                this._setFilterOptionsCount(restaurant._id);
            }
        });

        this._storiesContext.shouldFetchFilterOptionsCount$.subscribe(() => {
            const restaurant = this._storiesContext.restaurant();
            if (restaurant) {
                this._setFilterOptionsCount(restaurant._id);
            }
        });
    }

    selectFilter(filterOption: StoriesListFilter): void {
        this.selectedFilter.set(filterOption);
    }

    onCreateStory(): void {
        this.createStory.emit();
    }

    setIsSelecting(isSelecting: boolean): void {
        this._storiesContext.setIsSelecting(isSelecting);
        if (!isSelecting) {
            this._storiesContext.unselectAllStories();
        }
    }

    onDuplicateSelection(destination: DuplicationDestination): void {
        if (destination === DuplicationDestination.HERE) {
            const selectedStories = this.selectedStories();

            const restaurantId = this._storiesContext.restaurant().id;
            this._storiesContext.duplicateStories({
                postRefs: selectedStories.map((post) => ({ id: post.id })),
                restaurantIds: [restaurantId],
            });
        } else {
            this.duplicateToOtherRestaurants.emit({
                postRefs: this.selectedStories().map((post) => ({ id: post.id })),
            });
        }
    }

    onDeleteSelection(): void {
        const selectedStories = this.selectedStories();
        const deletableStories = selectedStories.filter((story) => story.canDelete());

        if (deletableStories.length === 0) {
            return;
        }

        const storyIds = deletableStories.map((story) => story.id);
        this._storiesContext.deleteStories(storyIds);
    }

    toggleSelectAll(value: boolean): void {
        if (value) {
            this._storiesContext.selectAllStories();
        } else {
            this._storiesContext.unselectAllStories();
        }
    }

    filterOptionsIdFn = (filterOption: StoriesListFilter): string => `tracking_stories_list_filter_${filterOption}_v2`;

    filterOptionDisplayFn = (filterOption: StoriesListFilter): string =>
        this._enumTranslatePipe.transform(filterOption, 'stories_list_filter');

    private _setFilterOptionsCount(restaurantId: string): void {
        this._storiesService.getStoriesCountByFilterOptions$(restaurantId).subscribe((filterOptionsCount) => {
            const filterOptionsAndCountWithoutZero = filterOptionsCount.filter((filterOptionCount) => filterOptionCount.count !== 0);
            this.filterOptionsAndCount.set(filterOptionsAndCountWithoutZero);
        });
    }
}
