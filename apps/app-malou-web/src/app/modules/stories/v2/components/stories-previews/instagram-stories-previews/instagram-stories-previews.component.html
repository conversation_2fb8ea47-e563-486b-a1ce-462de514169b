<app-meta-stories-previews
    [connectedSocialPlatforms]="connectedSocialPlatforms()"
    [stories]="stories()"
    [accountName]="accountName()"
    [profilePictureUrl]="profilePictureUrl()"
    [showStatus]="showStatus()"
    [isReadonly]="isReadonly()"
    [canAddTagAccount]="true"
    [userTagsList]="userTagsList()"
    [userTagsHistory]="userTagsHistory()"
    [toggleMediaScroll$]="toggleMediaScroll$()"
    (addUserTag)="onAddUserTag($event)"
    (removeUserTag)="onRemoveUserTag($event)"></app-meta-stories-previews>
