<div class="flex h-full flex-col gap-4 overflow-y-auto px-6 py-5 lg:px-10 lg:py-8">
    <div class="flex flex-col gap-4" cdkDropList (cdkDropListDropped)="drop($event)">
        @for (media of medias(); let index = $index; track media.uploadedMedia.id) {
            <div class="flex items-center gap-x-4 rounded-md border border-malou-color-border-primary bg-white p-3" cdkDrag>
                <mat-icon class="!h-5 !w-5 cursor-grab" color="primary" cdkDragHandle [svgIcon]="SvgIcon.DRAGGABLE"></mat-icon>
                <app-story-media
                    class="grow"
                    [isReadonly]="isReadonly()"
                    [media]="media.editedMedia ?? media.uploadedMedia"
                    (editMedia)="onEditMedia(media.uploadedMedia.id)"
                    (duplicateMedia)="onDuplicateMedia(media.uploadedMedia.id, index)"
                    (deleteMedia)="onRemoveMedia(media.uploadedMedia.id)"></app-story-media>
            </div>
        }
        @for (count of uploadingMediaCount() | createArray; track $index) {
            <app-skeleton skeletonClass="h-[90px] w-full secondary-bg"></app-skeleton>
        }
    </div>
    <div class="rounded-md border border-malou-color-border-primary">
        @if (isDragging()) {
            <div class="h-[75px] rounded-md border border-dotted border-malou-color-primary p-4">
                <div class="flex h-full items-center gap-x-5">
                    <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
                    <div class="malou-text-11--semibold text-malou-color-text-1">
                        {{ 'social_post_medias.drag_and_drop_here' | translate }}
                    </div>
                </div>
            </div>
        } @else {
            <app-add-media
                [isReadonly]="isReadonly()"
                [onlyVideoText]="false"
                (importMediaFromComputer)="fileInput.click()"
                (importMediaFromGallery)="onImportMediaFromGallery()"></app-add-media>
        }
    </div>
</div>

<input
    type="file"
    hidden
    [multiple]="true"
    [accept]="acceptAttribute"
    [disabled]="isReadonly()"
    (change)="onImportFromFile($event)"
    #fileInput />

<ng-template #dragOverTemplate>
    <div class="px-6 py-5 lg:px-10 lg:py-8">
        <div class="rounded-[5px] border border-dotted border-malou-color-primary bg-malou-color-background-light p-3">
            <div class="flex h-[75px] items-center gap-x-5 rounded-md bg-white p-4">
                <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
                <div class="malou-text-11--semibold text-malou-color-text-1">
                    {{ 'social_post_medias.drag_and_drop_here' | translate }}
                </div>
            </div>
        </div>
    </div>
</ng-template>

@if (showImageEditor()) {
    <!-- This component will take all the viewport -->
    <pintura-editor-modal
        [options]="editorOptions"
        [src]="imageEditorSrc()"
        [imageCropAspectRatio]="9 / 16"
        (hide)="onHideImageEditor()"
        (process)="onImageEditionDone($event)"></pintura-editor-modal>
}
