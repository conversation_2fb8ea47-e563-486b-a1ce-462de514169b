import { ChangeDetectionStrategy, Component, computed, effect, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatTabsModule } from '@angular/material/tabs';
import { TranslateService } from '@ngx-translate/core';
import { map } from 'rxjs';

import {
    HeapEventName,
    PlatformKey,
    PostFeedbacks,
    PostPublicationStatus,
    PostUserTag,
    RecurrentStoryFrequency,
} from '@malou-io/package-utils';

import { HeapService } from ':core/services/heap.service';
import { RestaurantsService } from ':core/services/restaurants.service';
import { NotesComponent } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/previews-feed-notes/components/notes/notes.component';
import { SocialPostMedia } from ':modules/posts-v2/social-posts/models/social-post-media';
import { StoriesPreviewsComponent } from ':modules/stories/v2/components/stories-previews/stories-previews.component';
import { UpsertStoryContext } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.context';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { User } from ':modules/user/user';

interface Tab {
    label: string;
    key: TabKeys;
}

enum TabKeys {
    PREVIEWS = 'previews',
    NOTES = 'notes',
}

@Component({
    selector: 'app-stories-previews-feed-notes',
    templateUrl: './stories-previews-feed-notes.component.html',
    styleUrls: ['./stories-previews-feed-notes.component.scss'],
    imports: [MatTabsModule, NotesComponent, StoriesPreviewsComponent],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StoriesPreviewsFeedNotesComponent {
    readonly isReadonly = input.required<boolean>();
    readonly shouldOpenFeedbacks = input.required<boolean>();

    private readonly _heapService = inject(HeapService);
    private readonly _upsertStoryContext = inject(UpsertStoryContext);
    private readonly _restaurantsService = inject(RestaurantsService);
    private readonly _translateService = inject(TranslateService);

    private readonly _PREVIEWS_TAB: Tab = {
        key: TabKeys.PREVIEWS,
        label: this._translateService.instant('social_posts.upsert_social_post_modal.previews_feed_notes.tabs.previews.title'),
    };
    private readonly _NOTES_TAB: Tab = {
        key: TabKeys.NOTES,
        label: this._translateService.instant('social_posts.upsert_social_post_modal.previews_feed_notes.tabs.notes.title'),
    };
    readonly tabs = signal([this._PREVIEWS_TAB, this._NOTES_TAB]);
    readonly selectedTabIndex = signal<number>(0);
    readonly selectedTab = computed<Tab>(() => this.tabs()[Math.min(this.tabs().length - 1, this.selectedTabIndex())]);

    // Feedbacks
    private readonly _restaurantManagers$ = this._restaurantsService.restaurantSelected$.pipe(
        map((restaurant) => restaurant?.managers.map((restaurantUser) => new User(restaurantUser.user)) ?? []),
        takeUntilDestroyed()
    );
    readonly restaurantManagers = toSignal<User[]>(this._restaurantManagers$);

    readonly feedbacks = this._upsertStoryContext.upsertStoryState.upsertStory.feedbacks;
    readonly storyId = this._upsertStoryContext.upsertStoryState.upsertStory.id;
    readonly isStoryPublished = computed(
        (): boolean => this._upsertStoryContext.upsertStoryState.upsertStory.published() === PostPublicationStatus.PUBLISHED
    );

    readonly connectedSocialPlatforms = this._upsertStoryContext.upsertStoryState.connectedSocialPlatforms;
    readonly userTagsList = this._upsertStoryContext.upsertStoryState.upsertStory.userTagsList;
    readonly userTagsHistory = this._upsertStoryContext.upsertStoryState.userTagsHistory;

    readonly selectedPlatformKeys = this._upsertStoryContext.upsertStoryState.upsertStory.platformKeys;

    readonly platformKeyForPreview = computed(() => {
        const platformKeys = this.selectedPlatformKeys();
        if (platformKeys.includes(PlatformKey.INSTAGRAM)) {
            return PlatformKey.INSTAGRAM;
        }
        if (platformKeys.includes(PlatformKey.FACEBOOK)) {
            return PlatformKey.FACEBOOK;
        }
        return PlatformKey.INSTAGRAM;
    });
    readonly platformOptions = computed(() => {
        const connectedSocialPlatforms = this._upsertStoryContext.upsertStoryState.connectedSocialPlatforms();
        const profilePictureUrls = this._upsertStoryContext.profilePictureUrls();
        return connectedSocialPlatforms.map((platform) => ({
            platformKey: platform.key,
            username: platform.name,
            profilePictureUrl: profilePictureUrls[platform.key],
        }));
    });
    readonly selectedPreviewPlatform = computed(() => {
        const platformOptions = this.platformOptions();
        const platformKey = this.platformKeyForPreview();
        const foundPlatformOption = platformOptions.find((option) => option.platformKey === platformKey);
        if (foundPlatformOption) {
            return foundPlatformOption;
        }
        return {
            platformKey,
            username: '',
            profilePictureUrl: '',
        };
    });

    readonly TabKeys = TabKeys;

    readonly stories = computed(() => {
        const medias = this._upsertStoryContext.upsertStoryState.upsertStory.medias();
        const selectedPlatformKeys = this.selectedPlatformKeys();
        return [
            new StoryItem({
                id: '',
                platformKeys: selectedPlatformKeys,
                published: PostPublicationStatus.DRAFT,
                isPublishing: false,
                feedbackMessageCount: 0,
                plannedPublicationDate: null,
                medias: medias.map((media) => SocialPostMedia.fromEditionMedia(media.editedMedia ?? media.uploadedMedia)),
                socialLink: '',
                socialCreatedAt: undefined,
                sortDate: undefined,
                author: undefined,
                mostRecentPublicationErrorCode: undefined,
                bindingId: undefined,
                recurrentStoryFrequency: RecurrentStoryFrequency.NONE,
            }),
        ];
    });

    constructor() {
        effect(() => {
            if (this.shouldOpenFeedbacks()) {
                const notesTabIndex = this.tabs().findIndex((tab) => tab.key === TabKeys.NOTES);
                this.selectedTabIndex.set(Math.max(notesTabIndex, 0));
            }
        });
    }

    handleTabChange(index: number): void {
        this.selectedTabIndex.set(index);
        setTimeout(() => {
            this._heapService.track(HeapEventName.STORIES_UPSERT_MODAL_SELECT_TAB, { tab: this.selectedTab().key });
        });
    }

    onFeedbacksChange(feedbacks: null | PostFeedbacks): void {
        this._upsertStoryContext.updateFeedbacks(feedbacks);
    }

    onAddUserTag(event: { index: number; userTag: PostUserTag }): void {
        this._upsertStoryContext.addUserTag(event.index, event.userTag);
    }

    onRemoveUserTag(event: { index: number; username: string }): void {
        this._upsertStoryContext.removeUserTag(event.index, event.username);
    }
}
