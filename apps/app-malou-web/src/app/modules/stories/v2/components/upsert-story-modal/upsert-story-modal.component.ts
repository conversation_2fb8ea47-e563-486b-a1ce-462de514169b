import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, Inject, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { isEqual } from 'lodash';
import { debounceTime, distinctUntilChanged, filter, Observable, of } from 'rxjs';

import { CaslAction, CaslSubject } from '@malou-io/package-utils';

import { AbilitiesContext } from ':core/context/abilities.context';
import { ToastService } from ':core/services/toast.service';
import { DuplicateAndSelectStoriesPlatformsComponent } from ':modules/stories/v2/components/upsert-story-modal/components/duplicate-and-select-stories-platforms/duplicate-and-select-stories-platforms.component';
import { StoriesContentFormComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/stories-content-form.component';
import { StoriesPreviewsFeedNotesComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-previews-feed-notes/stories-previews-feed-notes.component';
import { UpsertStoryModalFooterComponent } from ':modules/stories/v2/components/upsert-story-modal/components/upsert-story-modal-footer/upsert-story-modal-footer.component';
import { UpsertStoryContext } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.context';
import { CREATE_STORY_MODAL_HTML_ID } from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.constants';
import {
    UpsertStoryModalProps,
    UpsertStoryModalResult,
} from ':modules/stories/v2/components/upsert-story-modal/upsert-story-modal.interface';
import { StoryItem } from ':modules/stories/v2/models/story-item';
import { IUpsertStory, UpsertStory } from ':modules/stories/v2/models/upsert-story';
import { SubmitPublicationStatus } from ':shared/components/posts-v2/posts-v2.interface';
import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-upsert-story-modal',
    templateUrl: './upsert-story-modal.component.html',
    styleUrls: ['./upsert-story-modal.component.scss'],
    imports: [
        NgTemplateOutlet,
        MatButtonModule,
        MatIconModule,
        TranslateModule,
        DuplicateAndSelectStoriesPlatformsComponent,
        StoriesContentFormComponent,
        StoriesPreviewsFeedNotesComponent,
        UpsertStoryModalFooterComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [UpsertStoryContext],
})
export class UpsertStoryModalComponent {
    private readonly _abilitiesContext = inject(AbilitiesContext);
    private readonly _toastService = inject(ToastService);
    private readonly _translateService = inject(TranslateService);
    private readonly _upsertStoryContext = inject(UpsertStoryContext);

    readonly SvgIcon = SvgIcon;
    readonly CREATE_STORY_MODAL_HTML_ID = CREATE_STORY_MODAL_HTML_ID;

    readonly shouldOpenFeedbacks = signal(false);
    readonly storyId = signal<string | null>(null);

    readonly isSubmitting = this._upsertStoryContext.upsertStoryState.isSubmitting;
    readonly isLoadingPost = this._upsertStoryContext.upsertStoryState.isLoadingStories;
    readonly isReadonly = computed(() => this.isSubmitting() || this.isLoadingPost());
    readonly medias = this._upsertStoryContext.upsertStoryState.upsertStory.medias;

    private readonly _shouldAutoSave = signal(false);
    private _isFirstAutoSave = true;
    private readonly _upsertStoryForAutoSave = toSignal(
        toObservable(this._upsertStoryContext.upsertStoryState.upsertStory).pipe(
            filter((upsertStory) => !!upsertStory.id),
            distinctUntilChanged((a, b) => isEqual(a, b)),
            debounceTime(500)
        )
    );

    private readonly _hasErrorOccurredDuringInit = this._upsertStoryContext.hasErrorOccurredDuringInit;

    constructor(
        private readonly _dialogRef: MatDialogRef<UpsertStoryModalComponent, UpsertStoryModalResult>,
        @Inject(MAT_DIALOG_DATA)
        data: UpsertStoryModalProps
    ) {
        this._upsertStoryContext.closeModal$
            .pipe(filter(Boolean), takeUntilDestroyed())
            .subscribe(() => this._confirmClose({ story: null, scrollToStoryId: data.storyId }));
        this._upsertStoryContext.init(data.storyId, data.disconnectedPlatforms$, { date: data.date });

        this.storyId.set(data.storyId ?? null);

        if (data.shouldOpenFeedbacks) {
            this.shouldOpenFeedbacks.set(true);
        }

        this.storyId.set(data.storyId ?? null);

        if (!this._abilitiesContext.userRestaurantAbilities.can(CaslAction.MANAGE, CaslSubject.SOCIAL_POST)) {
            this._toastService.openErrorToast(this._translateService.instant('casl.wrong_role'));
            this._confirmClose({ story: null });
            return;
        }

        effect(() => {
            if (this._hasErrorOccurredDuringInit()) {
                this._toastService.openErrorToast(this._translateService.instant('stories.upsert_stories_modal.init_error'));
                this.close();
                return;
            }
        });

        effect(() => {
            const shouldAutoSave = this._shouldAutoSave();
            if (shouldAutoSave) {
                const story = this._upsertStoryForAutoSave();
                if (story) {
                    if (this._isFirstAutoSave) {
                        this._isFirstAutoSave = false;
                        return;
                    }
                    this._upsertStoryContext.autosave(story);
                }
            }
        });
    }

    onSaveStories(submitPublicationStatus: SubmitPublicationStatus): void {
        const upsertStory = this._upsertStoryContext.upsertStoryState.upsertStory();
        if (!upsertStory) {
            return;
        }
        this._upsertStoryContext.saveStory$(upsertStory, submitPublicationStatus).subscribe((res) => {
            if (!res) {
                return;
            }

            this._confirmClose({
                story: res.toInterface(),
                isManualSave: true,
                scrollToStoryId: res.id,
                submitPublicationStatus,
            });
        });
    }

    close(): void {
        const storyState = this._upsertStoryContext.upsertStoryState.upsertStory();

        // If the story is empty, and it was a creation, close the modal and delete the story
        const initialStory = this._upsertStoryContext.initialStory();

        const isEmptyAndCreation = !initialStory && new UpsertStory(storyState).isEmpty();
        if (isEmptyAndCreation) {
            this._upsertStoryContext.deleteStory$().subscribe(() => {
                this._confirmClose({ story: null });
            });
        } else {
            // Send data to the parent component only if autosave is enabled
            const story = this._shouldAutoSave() ? (storyState ?? null) : null;
            this._confirmClose({ story, scrollToStoryId: story?.id });
        }
    }

    onCancel(): void {
        const initialStory = this._upsertStoryContext.initialStory();

        let actionBeforeClose$: Observable<UpsertStory | null>;

        if (!initialStory) {
            actionBeforeClose$ = this._upsertStoryContext.deleteStory$();
        } else if (this._shouldAutoSave()) {
            actionBeforeClose$ = this._upsertStoryContext.saveStory$(initialStory, SubmitPublicationStatus.DRAFT);
        } else {
            actionBeforeClose$ = of(null);
        }

        actionBeforeClose$.subscribe((res) => {
            this._confirmClose({ story: res ?? null, scrollToStoryId: res?.id });
        });
    }

    private _confirmClose(data: {
        story: IUpsertStory | null;
        isManualSave?: boolean;
        scrollToStoryId?: string;
        submitPublicationStatus?: SubmitPublicationStatus;
    }): void {
        const story = data.story ? StoryItem.fromIUpsertStory(data.story) : null;
        const duplicateToOtherRestaurants = !!data.isManualSave && this._upsertStoryContext.upsertStoryState.duplicateToOtherRestaurants();
        this._dialogRef.close({
            story,
            scrollToStoryId: data.scrollToStoryId,
            duplicateToOtherRestaurants,
            submitPublicationStatus: data.submitPublicationStatus,
        });
    }
}
