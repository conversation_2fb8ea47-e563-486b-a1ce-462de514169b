<div
    class="relative h-full overflow-hidden border-l border-malou-color-border-primary bg-malou-color-background-light px-6 pb-4 pt-2 lg:border-t lg:px-10">
    <div class="flex h-full w-full flex-col items-center justify-center gap-5">
        <mat-tab-group
            class="h-full w-full"
            animationDuration="5ms"
            mat-align-tabs="start"
            [preserveContent]="true"
            [selectedIndex]="selectedTabIndex()"
            (selectedIndexChange)="handleTabChange($event)">
            @for (tab of tabs(); track tab.key) {
                <mat-tab bodyClass="h-full" [labelClass]="'tracking_edit_custom_ai_settings_modal_' + tab.key + '_tab'">
                    <ng-template mat-tab-label>
                        <div class="malou-text-14--regular flex items-center text-malou-color-text-2">
                            <span class="tab-title" [class]="{ 'malou-text-14--semibold': selectedTab().key === tab.key }">
                                {{ tab.label }}
                            </span>
                        </div>
                    </ng-template>
                    @if (selectedTab().key === tab.key) {
                        <div class="flex h-full justify-center pt-4">
                            @switch (selectedTab().key) {
                                @case (TabKeys.PREVIEWS) {
                                    <div class="aspect-9/16 w-full max-w-[400px]">
                                        <div class="overflow-hidden rounded-[10px]">
                                            <app-stories-previews
                                                class="h-full w-full"
                                                [selectedPreviewPlatform]="selectedPreviewPlatform()"
                                                [stories]="stories()"
                                                [showStatus]="false"
                                                [isReadonly]="isReadonly()"
                                                [connectedSocialPlatforms]="connectedSocialPlatforms()"
                                                [userTagsList]="userTagsList()"
                                                [userTagsHistory]="userTagsHistory()"
                                                (addUserTag)="onAddUserTag($event)"
                                                (removeUserTag)="onRemoveUserTag($event)"></app-stories-previews>
                                        </div>
                                    </div>
                                }
                                @case (TabKeys.NOTES) {
                                    <app-notes
                                        class="h-full w-full"
                                        trackingId="tracking_upsert_stories_modal_send_feedback"
                                        [feedbacks]="feedbacks()"
                                        [restaurantManagers]="restaurantManagers() ?? []"
                                        [postId]="storyId()"
                                        [isPostPublished]="isStoryPublished()"
                                        [isReadonly]="isReadonly()"
                                        (feedbacksChange)="onFeedbacksChange($event)"></app-notes>
                                }
                            }
                        </div>
                    }
                </mat-tab>
            }
        </mat-tab-group>
    </div>
</div>
