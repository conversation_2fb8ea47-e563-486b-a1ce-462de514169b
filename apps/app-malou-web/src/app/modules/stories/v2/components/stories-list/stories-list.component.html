@if (restaurantHasNoStory()) {
    <app-no-story
        [isInstagramConnected]="isIgConnected()"
        [isFacebookConnected]="isFbConnected()"
        [restaurantId]="restaurantId()"
        (createPost)="onCreateStory()"></app-no-story>
} @else {
    <div class="flex">
        <div class="min-w-0 grow">
            <app-stories-list-header
                [(selectedFilter)]="selectedFilter"
                (createStory)="onCreateStory()"
                (duplicateToOtherRestaurants)="duplicateToOtherRestaurants.emit($event)" />

            <div
                class="flex h-full flex-col gap-3 px-6 py-3 pb-10"
                infinite-scroll
                infiniteScrollContainer="#scrollable-content"
                [infiniteScrollDistance]="2"
                [fromRoot]="true"
                (scrolled)="onScrollDown()">
                @if (isFetchingStories()) {
                    <ng-container [ngTemplateOutlet]="loadingTemplate"></ng-container>
                } @else {
                    @for (story of stories(); track story.id) {
                        <div class="flex items-center gap-x-3">
                            @if (isSelecting()) {
                                <mat-checkbox
                                    class="checkbox"
                                    color="primary"
                                    [checked]="isSelected | applyPure: story : selectedStoriesCount()"
                                    (change)="onSelect(story)"></mat-checkbox>
                            }
                            <app-story-item
                                class="grow"
                                [ngClass]="{ 'cursor-pointer': isSelecting() }"
                                [id]="'story-' + story.id"
                                [story]="story"
                                [isHighlighted]="isHighlighted | applyPure: story : highlightedStoryIds()"
                                [isReadonly]="isSelecting()"
                                (updateStory)="onUpdateStory($event)"
                                (deleteStory)="onDeleteStory($event)"
                                (duplicateStory)="onDuplicateStory($event)"
                                (storyDateChange)="onStoryDateChange($event)"
                                (click)="isSelecting() && onSelect(story)"></app-story-item>
                        </div>
                    }
                    @if (isFetchingMoreStories()) {
                        <ng-container [ngTemplateOutlet]="storySkeletonTemplate"></ng-container>
                    }
                }
            </div>
        </div>

        <div class="sticky top-0 h-screen w-[30%] min-w-[450px] shrink-0 self-start lg:hidden" #previewPanel>
            <app-stories-previews-header
                [(selectedPreviewPlatform)]="selectedPreviewPlatform"
                [previewPlatformOptions]="previewPlatformOptions()"></app-stories-previews-header>
            <div class="h-full border-l border-malou-color-background-dark">
                <app-stories-previews
                    [selectedPreviewPlatform]="selectedPreviewPlatform()"
                    [stories]="futureStories()"
                    [isReadonly]="true"
                    [toggleMediaScroll$]="toggleMediaScroll$()"></app-stories-previews>
            </div>
        </div>
    </div>
}

<ng-template #loadingTemplate>
    @for (count of [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; track count) {
        <ng-container [ngTemplateOutlet]="storySkeletonTemplate"></ng-container>
    }
</ng-template>

<ng-template #storySkeletonTemplate>
    <app-skeleton skeletonClass="secondary-bg h-[106px] w-full"></app-skeleton>
</ng-template>
