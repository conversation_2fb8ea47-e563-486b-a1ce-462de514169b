import { CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList } from '@angular/cdk/drag-drop';
import { ChangeDetectionStrategy, Component, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { AngularPinturaModule } from '@pqina/angular-pintura';
import { getEditorDefaults, PinturaDefaultImageWriterResult, PinturaEditorOptions } from '@pqina/pintura';
import { filter, lastValueFrom } from 'rxjs';

import { GetMediaForEditionResponseDto } from '@malou-io/package-dto';
import { ProcessingMediaStatus, PublicationType } from '@malou-io/package-utils';

import { ProcessingMediasService } from ':core/services/processing-medias.service';
import { UploadClientError } from ':modules/media/media.service';
import { MediaUploaderService } from ':modules/posts-v2/social-posts/components/upsert-social-post-modal/components/social-post-content-form/social-post-medias/media-uploader.service';
import { BaseStoryMediaImportComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/base-story-media-import/base-story-media-import.component';
import { StoryMediaComponent } from ':modules/stories/v2/components/upsert-story-modal/components/stories-content-form/story-media/story-media.component';
import { UpsertStoryContext } from ':modules/stories/v2/components/upsert-story-modal/contexts/upsert-story.context';
import { AddMediaComponent } from ':shared/components/posts-v2/medias/add-media/add-media.component';
import { IMAGE_MIME_TYPES, VIDEO_MIME_TYPES } from ':shared/components/posts-v2/medias/posts-v2-media.interface';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { CreateArrayPipe } from ':shared/pipes/create-array.pipe';
import { BodyDragAndDropEventsService } from ':shared/services/body-drag-and-drop-events.service';

@Component({
    selector: 'app-stories-content-form',
    templateUrl: './stories-content-form.component.html',
    styleUrls: ['./stories-content-form.component.scss'],
    imports: [
        CdkDropList,
        CdkDrag,
        CdkDragHandle,
        MatIconModule,
        AddMediaComponent,
        StoryMediaComponent,
        TranslateModule,
        CreateArrayPipe,
        SkeletonComponent,
        AngularPinturaModule,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [MediaUploaderService],
})
export class StoriesContentFormComponent extends BaseStoryMediaImportComponent {
    readonly isReadonly = input.required<boolean>();

    private readonly _upsertStoryContext = inject(UpsertStoryContext);
    private readonly _bodyDragAndDropEventsService = inject(BodyDragAndDropEventsService);
    private readonly _processingMediasService = inject(ProcessingMediasService);

    readonly acceptAttribute = [...IMAGE_MIME_TYPES, ...VIDEO_MIME_TYPES];

    readonly isDragging = signal(false);

    readonly SvgIcon = SvgIcon;

    readonly editorOptions = getEditorDefaults() as PinturaEditorOptions;
    readonly showImageEditor = signal(false);
    readonly imageEditorSrc = signal<any | undefined>(undefined);
    readonly uploadedMediaIdInEdition = signal<string | undefined>(undefined);

    constructor() {
        super();
        this._handleBodyDragEvents();
    }

    async onShowImageEditor(imageUrl: string): Promise<void> {
        this.imageEditorSrc.set(imageUrl);
        this.showImageEditor.set(true);
    }

    onHideImageEditor(): void {
        this.showImageEditor.set(false);
        this.imageEditorSrc.set(undefined);
    }

    onEditMedia(mediaId: string): void {
        const medium = this.medias().find((m) => m.uploadedMedia.id === mediaId);
        if (!medium) {
            return;
        }
        this.uploadedMediaIdInEdition.set(mediaId);
        this.onShowImageEditor(medium.editedMedia?.thumbnail1024OutsideUrl ?? medium.uploadedMedia.thumbnail1024OutsideUrl);
    }

    async onImageEditionDone(event: PinturaDefaultImageWriterResult): Promise<void> {
        const result = await this._mediaService.uploadV2({
            file: event.dest,
            onProgress: () => {},
            queryParams: {
                restaurantId: this._restaurantsService.currentRestaurant._id,
                originalMediaId: this.uploadedMediaIdInEdition(),
            },
        });
        if (!result.success) {
            if (result.errorCode === UploadClientError.NETWORK_ERROR) {
                this._toastService.openErrorToast(this._translateService.instant('upload_errors.network_error'));
            }
            return;
        }
        const processingMedia = await this._processingMediasService.waitUntilEnded(result.result.processingMediaId);
        if (processingMedia.status === ProcessingMediaStatus.ERROR) {
            this._toastService.openErrorToast(this._translateService.instant('upload_errors.invalid_file'));
        } else if (processingMedia.status === ProcessingMediaStatus.SUCCESS && processingMedia.mediaId) {
            const editedMedia = await lastValueFrom(
                this._mediaService.getMediaForEdition({ mediaId: processingMedia.mediaId }, { publicationType: PublicationType.STORY })
            );
            const uploadedMediaId = this.uploadedMediaIdInEdition();
            if (!uploadedMediaId) {
                // silent fail, the uploaded media has been deleted by the user
                return;
            }
            this._upsertStoryContext.attachEditedMediaToUploadedMedia(uploadedMediaId, editedMedia.data);
        } else {
            this._toastService.openErrorToast(this._translateService.instant('common.unknow_error'));
        }
    }

    onDuplicateMedia(mediaId: string, index: number): void {
        this._upsertStoryContext.duplicateMedia(mediaId, index);
    }

    onRemoveMedia(mediaId: string): void {
        this._upsertStoryContext.removeMedia(mediaId);
    }

    drop(event: CdkDragDrop<string[]>): void {
        this._upsertStoryContext.reorderMedias(event.previousIndex, event.currentIndex);
    }

    // Override BaseStoryMediaImportComponent method
    protected addMedia(media: GetMediaForEditionResponseDto): void {
        this._upsertStoryContext.addMedia({ uploadedMedia: media });
    }

    // ------- Events handlers : drag and drop ------- //

    private _handleBodyDragEvents(): void {
        this._bodyDragAndDropEventsService.dragEnter.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragEnter);
        this._bodyDragAndDropEventsService.dragOver.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragOver);
        this._bodyDragAndDropEventsService.dragLeave.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDragLeave);
        this._bodyDragAndDropEventsService.drop.pipe(filter(this._hasFile), takeUntilDestroyed()).subscribe(this._onDrop);
    }

    private _hasFile(event: DragEvent): boolean {
        return (event.dataTransfer?.types ?? []).includes('Files');
    }

    private _onDragEnter = (): void => {
        this.isDragging.set(true);
    };

    private _onDragOver = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
    };

    private _onDragLeave = (event: DragEvent): void => {
        event.preventDefault();
        this.isDragging.set(false);
    };

    private _onDrop = (event: DragEvent): void => {
        // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/File_drag_and_drop#prevent_the_browsers_default_drag_behavior
        // Prevent default behavior (Prevent file from being opened)
        event.preventDefault();
        this.isDragging.set(false);
        for (const file of Array.from(event.dataTransfer?.files ?? [])) {
            this._importMediaFromFile(file);
        }
    };
}
