import { Async<PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, Signal, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TranslateModule } from '@ngx-translate/core';
import { catchError, combineLatest, debounceTime, EMPTY, filter, map, Observable, of, switchMap, tap } from 'rxjs';

import {
    FAKE_NFC_ID_FOR_WHEEL_OF_FORTUNE_SCANS,
    InsightsChart,
    isNotNil,
    MalouComparisonPeriod,
    MalouPeriod,
} from '@malou-io/package-utils';

import { NfcService } from ':core/services/nfc.service';
import { AGGREGATED_STATISTICS_RESTAURANTS_COUNT_UI_LIMIT } from ':modules/aggregated-statistics/aggregated-statistics.constant';
import { AggregatedBoostersScanCountComponent } from ':modules/aggregated-statistics/boosters/aggregated-boosters-scan-count/aggregated-boosters-scan-count.component';
import { AggregatedTotemsEstimatedReviewCountComponent } from ':modules/aggregated-statistics/boosters/aggregated-totems-estimated-review-count/aggregated-totems-estimated-review-count.component';
import { AggregatedWheelOfFortuneEstimatedReviewCountComponent } from ':modules/aggregated-statistics/boosters/aggregated-wheel-of-fortune-estimated-review-count/aggregated-wheel-of-fortune-estimated-review-count.component';
import { AggregatedWheelOfFortuneGiftsDistributionComponent } from ':modules/aggregated-statistics/boosters/aggregated-wheel-of-fortune-gifts-distribution/aggregated-wheel-of-fortune-gifts-distribution.component';
import { AggregatedWheelOfFortuneGiftsKpisComponent } from ':modules/aggregated-statistics/boosters/aggregated-wheel-of-fortune-gifts-kpis/aggregated-wheel-of-fortune-gifts-kpis.component';
import {
    AggregatedBoostersStatisticsData,
    AggregatedBoostersStatisticsDataV2,
    AggregatedWheelOfFortuneGiftsStatisticsData,
} from ':modules/aggregated-statistics/boosters/booster.interface';
import { BoostersAggregatedDataFetchingServiceV2 } from ':modules/aggregated-statistics/boosters/services/get-boosters-aggregated-data-v2.service';
import { BoostersAggregatedDataFetchingService } from ':modules/aggregated-statistics/boosters/services/get-boosters-aggregated-data.service';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import { ChartOptions } from ':shared/components/download-insights-modal/download-insights.interface';
import { isDateSetOrGenericPeriod } from ':shared/helpers';
import { parseInsightsRouteParams } from ':shared/helpers/extract-statistics-route-data';
import { DatesAndPeriod, LightNfc, Restaurant } from ':shared/models';
import { FromToDateFormatterPipe } from ':shared/pipes/from-to-date-formatter.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';
import { StatisticsPdfRestaurantsFormatterPipe } from ':shared/pipes/statistics-pdf-restaurants-formatter.pipe';

@Component({
    selector: 'app-boosters-pdf',
    templateUrl: './boosters-pdf.component.html',
    imports: [
        TranslateModule,
        FromToDateFormatterPipe,
        NgTemplateOutlet,
        AsyncPipe,
        IncludesPipe,
        AggregatedBoostersScanCountComponent,
        AggregatedTotemsEstimatedReviewCountComponent,
        AggregatedWheelOfFortuneEstimatedReviewCountComponent,
        AggregatedWheelOfFortuneGiftsDistributionComponent,
        AggregatedWheelOfFortuneGiftsKpisComponent,
        StatisticsPdfRestaurantsFormatterPipe,
    ],
    styleUrls: ['./boosters-pdf.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BoostersPdfComponent {
    startDate: Date;
    endDate: Date;
    displayedCharts: InsightsChart[];
    chartOptions: ChartOptions;
    selectedRestaurantsTitle$: Observable<string>;
    isRestaurantsCountUiLimitExceeded$: Observable<boolean>;
    nfcIds: string[] = [];
    restaurantIds: string[] = [];

    readonly InsightsChart = InsightsChart;
    totems$: Observable<LightNfc[]>;
    restaurants$: Observable<Restaurant[]>;
    restaurantsWithBoosterPackActivated$: Observable<Restaurant[]>;

    readonly restaurants = signal<Restaurant[]>([]);

    atLeastOneBoosterPackActivated: WritableSignal<Observable<Boolean>> = signal(of(false));

    readonly isLoadingBoosters: WritableSignal<boolean> = signal(true);
    readonly isErrorBoosters: WritableSignal<boolean> = signal(false);
    readonly isLoadingGifts: WritableSignal<boolean> = signal(true);
    readonly isErrorGifts: WritableSignal<boolean> = signal(false);
    readonly isLoadingTotemReviews: WritableSignal<boolean> = signal(false);
    readonly isErrorTotemReviews: WritableSignal<boolean> = signal(false);

    boostersData$: Observable<AggregatedBoostersStatisticsData>;
    wheelOfFortuneData$: Observable<AggregatedBoostersStatisticsData>;
    giftsData$: Observable<AggregatedWheelOfFortuneGiftsStatisticsData>;

    boostersAggregatedScanCountHasData = true;
    wheelOfFortuneAggregatedEstimatedReviewCountHasData = true;
    wheelOfFortuneAggregatedGiftsDistributionHasData = true;
    wheelOfFortuneAggregatedGiftsKpisHasData = true;

    readonly boostersData: WritableSignal<AggregatedBoostersStatisticsDataV2 | null> = signal(null);
    readonly totemReviewsData: Signal<AggregatedBoostersStatisticsDataV2['totemReviewsPerRestaurant'] | null> = computed(() => {
        const boosterData = this.boostersData();
        if (boosterData) {
            return boosterData.totemReviewsPerRestaurant;
        }
        return null;
    });

    constructor(
        private readonly _aggregatedFiltersContext: AggregatedStatisticsFiltersContext,
        private readonly _destroyRef: DestroyRef,
        private readonly _nfcsService: NfcService,
        private readonly _getBoostersAggregatedDataService: BoostersAggregatedDataFetchingService,
        private readonly _getBoostersAggregatedDataServiceV2: BoostersAggregatedDataFetchingServiceV2
    ) {
        this._getRouteParams();

        this._init();

        this._getRestaurantsTitle();

        this._boostersAggregatedData();

        this._initBoostersData();

        this._getChartsData();
    }

    private _getRouteParams(): void {
        const parsedQueryParams = parseInsightsRouteParams();
        const { dates, displayedCharts, chartOptions, nfcIds, restaurantIds } = parsedQueryParams;

        this.displayedCharts = displayedCharts;
        this.chartOptions = chartOptions ?? {};
        this.startDate = dates.startDate;
        this.endDate = dates.endDate;

        this.nfcIds = nfcIds ?? [];
        this.restaurantIds = restaurantIds ?? [];
    }

    private _init(): void {
        this.restaurants$ = this._aggregatedFiltersContext.selectedRestaurants$;

        this.restaurantsWithBoosterPackActivated$ = this.restaurants$.pipe(
            map((restaurants) =>
                restaurants.filter((restaurant) => restaurant.boosterPack?.activated && this.restaurantIds.includes(restaurant._id))
            )
        );
        this.atLeastOneBoosterPackActivated = signal(
            this.restaurantsWithBoosterPackActivated$.pipe(
                map((restaurants) => restaurants.length > 0),
                takeUntilDestroyed(this._destroyRef)
            )
        );
        this.totems$ = this._nfcsService.getLightNfcByRestaurantIds(this.restaurantIds);

        this.restaurants$.pipe(takeUntilDestroyed(this._destroyRef)).subscribe((restaurants) => this.restaurants.set(restaurants));
    }

    private _getRestaurantsTitle(): void {
        this.selectedRestaurantsTitle$ = this.restaurants$.pipe(
            map((restaurants) => restaurants.map((restaurant) => restaurant.internalName ?? restaurant.name).join(', '))
        );
        this.isRestaurantsCountUiLimitExceeded$ = this.restaurants$.pipe(
            map((restaurants) => restaurants.length > AGGREGATED_STATISTICS_RESTAURANTS_COUNT_UI_LIMIT)
        );
    }

    private _boostersAggregatedData(): void {
        this.boostersData$ = combineLatest([
            this.restaurants$,
            of({ startDate: this.startDate, endDate: this.endDate, period: MalouPeriod.CUSTOM }),
            this.totems$,
        ]).pipe(
            filter(([restaurants, dates]) => restaurants.length > 0 && isDateSetOrGenericPeriod(dates)),
            tap(() => {
                this.isErrorBoosters.set(false);
                this.isLoadingBoosters.set(true);
            }),
            debounceTime(500),
            filter(([_, dates]) => isNotNil(dates.startDate) && isNotNil(dates.endDate)),
            switchMap(([restaurants, dates, nfcs]: [Restaurant[], DatesAndPeriod, LightNfc[]]) => {
                const data = this._getBoostersAggregatedDataService.getChartsData(
                    this._getFilteredNfcs(nfcs, this.nfcIds),
                    dates,
                    restaurants
                );
                this.isLoadingBoosters.set(false);
                return data;
            }),
            catchError(() => {
                this.isErrorBoosters.set(true);
                this.isLoadingBoosters.set(false);
                return EMPTY;
            }),
            takeUntilDestroyed(this._destroyRef)
        );
    }

    private _initBoostersData(): void {
        combineLatest([
            this.restaurants$,
            of({ startDate: this.startDate, endDate: this.endDate, period: MalouPeriod.CUSTOM }),
            this.totems$,
        ])
            .pipe(
                filter(([restaurants, dates, totems]) => restaurants.length > 0 && isDateSetOrGenericPeriod(dates) && totems.length > 0),
                tap(() => {
                    this.isErrorBoosters.set(false);
                    this.isLoadingBoosters.set(true);
                    this.isErrorTotemReviews.set(false);
                    this.isLoadingTotemReviews.set(true);
                }),
                debounceTime(500),
                filter(([_, dates]) => isNotNil(dates.startDate) && isNotNil(dates.endDate)),
                switchMap(([restaurants, dates, nfcs]: [Restaurant[], DatesAndPeriod, LightNfc[]]) => {
                    // TODO: check this file, why we use v2 only for totem reviews ? [@hamza]
                    const data = this._getBoostersAggregatedDataServiceV2.getChartsData({
                        nfcs,
                        dates,
                        restaurantIds: restaurants.map((r) => r._id.toString()),
                        comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                    });
                    return data;
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (data) => {
                    this.boostersData.set(data);
                    this.isLoadingBoosters.set(false);
                    this.isLoadingTotemReviews.set(false);
                },
                error: (error) => {
                    console.error('error >>', error);
                    this.isErrorBoosters.set(true);
                    this.isLoadingBoosters.set(false);
                    this.isErrorTotemReviews.set(true);
                    this.isLoadingTotemReviews.set(false);
                },
            });
    }

    private _getChartsData(): void {
        this.giftsData$ = combineLatest([
            this.restaurantsWithBoosterPackActivated$,
            of({ startDate: this.startDate, endDate: this.endDate, period: MalouPeriod.CUSTOM }),
        ]).pipe(
            filter(([restaurants, dates]) => restaurants.length > 0 && isDateSetOrGenericPeriod(dates)),
            tap(() => {
                this.isErrorGifts.set(false);
                this.isLoadingGifts.set(true);
            }),
            debounceTime(500),
            filter(([_, dates]) => isNotNil(dates.startDate) && isNotNil(dates.endDate)),
            switchMap(([restaurants, dates]: [Restaurant[], DatesAndPeriod]) => {
                const data = this._getBoostersAggregatedDataService.getGiftsData(dates, restaurants);
                this.isLoadingGifts.set(false);
                return data;
            }),
            catchError(() => {
                this.isErrorGifts.set(true);
                this.isLoadingGifts.set(false);
                return EMPTY;
            }),
            takeUntilDestroyed(this._destroyRef)
        );

        this.wheelOfFortuneData$ = this.boostersData$.pipe(
            map((data) => ({
                ...data,
                scans: data.scans.filter((scan) => scan.isWheelOfFortuneRelated()),
                previousScans: data.previousScans.filter((scan) => scan.isWheelOfFortuneRelated()),
            }))
        );
    }

    private _getFilteredNfcs(allNfcs: LightNfc[], nfcs: string[] = []): LightNfc[] {
        const nfcsWithWheelOfFortune = nfcs.concat([FAKE_NFC_ID_FOR_WHEEL_OF_FORTUNE_SCANS]);
        if (this.displayedCharts.includes(InsightsChart.AGGREGATED_BOOSTERS_SCAN_COUNT)) {
            return allNfcs.filter((nfc: LightNfc) => nfcsWithWheelOfFortune.includes(nfc.id));
        }
        return allNfcs;
    }
}
