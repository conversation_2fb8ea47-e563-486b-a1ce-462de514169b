import { SegmentAnalysisInsightsForRestaurantDto } from '@malou-io/package-dto';
import { MalouComparisonPeriod, MonthYearPeriod, PlatformFilterPage, PlatformKey } from '@malou-io/package-utils';

import {
    AggregatedBoostersStatisticsData,
    AggregatedBoostersStatisticsDataV2,
} from ':modules/aggregated-statistics/boosters/booster.interface';
import { ReviewsRatingsAverageData } from ':modules/aggregated-statistics/e-reputation/reviews/reviews-ratings-average/reviews-ratings-average.component';
import { AggregatedKeywordRankingData } from ':modules/aggregated-statistics/seo/models/keyword-table-data-row';
import { TopKeywordSearchImpressions } from ':modules/aggregated-statistics/seo/models/top-keyword-search-impressions.interface';
import { ChartReviewsStats, DatesAndPeriod, InsightsByPlatformByRestaurant, MalouTimeScalePeriod } from ':shared/models';

export interface AggregatedStatisticsState {
    filters: {
        dates: DatesAndPeriod;
        platforms: Record<PlatformFilterPage, PlatformKey[]>;
        restaurantIds: string[];
        roiRestaurantIds: string[];
        totemIds: string[];
        timeScale: MalouTimeScalePeriod;
        comparisonPeriod: MalouComparisonPeriod;
        monthYearPeriod: MonthYearPeriod;
    };
    data: {
        platformsRatingsByRestaurant: InsightsByPlatformByRestaurant;
        reviewCounts: ChartReviewsStats[];
        averageReviewsRatings: ReviewsRatingsAverageData[];
        boosterStatsData: AggregatedBoostersStatisticsData | undefined;
        boosterStatsDataV2: AggregatedBoostersStatisticsDataV2 | undefined;
        topKeywordSearchImpressions: TopKeywordSearchImpressions;
        keywordRankings: AggregatedKeywordRankingData[];
        semanticAnalysisData: SegmentAnalysisInsightsForRestaurantDto[] | undefined;
    };
    page: PlatformFilterPage;
    loaded: boolean;
}
