import { Ng<PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    DestroyRef,
    inject,
    input,
    OnInit,
    output,
    signal,
    ViewChild,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatButtonToggleModule } from '@angular/material/button-toggle';
import { MatIconModule } from '@angular/material/icon';
import { MatSort, MatSortModule, Sort } from '@angular/material/sort';
import { MatTableDataSource, MatTableModule } from '@angular/material/table';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { partition } from 'lodash';
import { DateTime } from 'luxon';
import { combineLatest, forkJoin, map, Observable, switchMap, tap } from 'rxjs';

import { AggregationType, GeoSamplePlatform, MalouComparisonPeriod, MonthYearPeriod, PlatformKey } from '@malou-io/package-utils';

import { KeywordsService } from ':core/services/keywords.service';
import { AggregatedStatisticsHttpErrorPipe } from ':modules/aggregated-statistics/aggregated-statistics-http-error.pipe';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import {
    AggregatedKeywordRankings,
    KeywordSearchImpressionsTotal,
} from ':modules/aggregated-statistics/seo/models/keyword-search-impressions.interface';
import { KeywordTableDataRowV2 } from ':modules/aggregated-statistics/seo/models/keyword-table-data-row';
import * as AggregatedStatisticsActions from ':modules/aggregated-statistics/store/aggregated-statistics.actions';
import { AggregatedStatisticsState } from ':modules/aggregated-statistics/store/aggregated-statistics.interface';
import * as AggregatedStatisticsSelector from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { KeywordSearchImpressionsService } from ':modules/statistics/seo/keyword-search-impressions/keyword-search-impressions.service';
import { NumberEvolutionComponent } from ':shared/components/number-evolution/number-evolution.component';
import { PlatformLogoComponent } from ':shared/components/platform-logo/platform-logo.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { FilterOption, SortByFiltersComponent } from ':shared/components/sort-by-filters/sort-by-filters.component';
import { TypeSafeMatCellDefDirective } from ':shared/directives/type-safe-mat-cell-def.directive';
import { TypeSafeMatRowDefDirective } from ':shared/directives/type-safe-mat-row-def.directive';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { ApplySelfPurePipe } from ':shared/pipes/apply-fn.pipe';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';

enum KeywordTableColumns {
    RESTAURANT_NAME = 'restaurantName',
    CURRENT_TOP_20 = 'currentTop20',
    CURRENT_TOP_3 = 'currentTop3',
    BRANDING_KEYWORD_IMPRESSIONS = 'brandingKeywordImpressions',
    DISCOVERY_KEYWORD_IMPRESSIONS = 'discoveryKeywordImpressions',
}

enum KeywordTableGroupedColumns {
    RESTAURANT_NAME_GROUP = 'restaurantNameGroup',
    GOOGLE_POSITIONS = 'googlePositions',
    SEARCH_KEYWORDS = 'searchKeywords',
}

@Component({
    selector: 'app-keywords-v5',
    imports: [
        NgTemplateOutlet,
        MatTooltipModule,
        MatIconModule,
        MatButtonToggleModule,
        SortByFiltersComponent,
        MatTableModule,
        MatSortModule,
        SkeletonComponent,
        IllustrationPathResolverPipe,
        TranslateModule,
        ApplySelfPurePipe,
        TypeSafeMatCellDefDirective,
        TypeSafeMatRowDefDirective,
        ShortNumberPipe,
        NumberEvolutionComponent,
        NgClass,
        PlatformLogoComponent,
    ],
    templateUrl: './keywords-v5.component.html',
    styleUrl: './keywords-v5.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [AggregatedStatisticsHttpErrorPipe],
})
export class KeywordsV5Component implements OnInit {
    readonly tableSortOptions = input<Sort | undefined>(undefined);
    readonly shouldSetMaxHeight = input<boolean>(true);
    readonly isLoadingEvent = output<boolean>();
    readonly tableSortOptionsChange = output<Sort>();
    readonly hasDataChange = output<boolean>();

    private readonly _store = inject(Store);
    private readonly _aggregatedStatisticsFiltersContext = inject(AggregatedStatisticsFiltersContext);
    private readonly _translate = inject(TranslateService);
    private readonly _keywordsService = inject(KeywordsService);
    private readonly _keywordSearchImpressionsService = inject(KeywordSearchImpressionsService);
    private readonly _router = inject(Router);
    private readonly _aggregatedStatisticsHttpErrorPipe = inject(AggregatedStatisticsHttpErrorPipe);
    private readonly _destroyRef = inject(DestroyRef);

    dataSource: MatTableDataSource<any>;
    readonly DISPLAYED_GROUPED_COLUMNS = [
        KeywordTableGroupedColumns.RESTAURANT_NAME_GROUP,
        KeywordTableGroupedColumns.GOOGLE_POSITIONS,
        KeywordTableGroupedColumns.SEARCH_KEYWORDS,
    ];
    readonly DISPLAYED_COLUMNS = [
        KeywordTableColumns.RESTAURANT_NAME,
        KeywordTableColumns.CURRENT_TOP_20,
        KeywordTableColumns.CURRENT_TOP_3,
        KeywordTableColumns.BRANDING_KEYWORD_IMPRESSIONS,
        KeywordTableColumns.DISCOVERY_KEYWORD_IMPRESSIONS,
    ];
    readonly SORT_OPTIONS: FilterOption[] = [
        { key: 'restaurantName', label: this._translate.instant('aggregated_statistics.seo.keywords.restaurant') },
        { key: 'top20', label: this._translate.instant('aggregated_statistics.seo.keywords.top20') },
        { key: 'top3', label: this._translate.instant('aggregated_statistics.seo.keywords.top3') },
        { key: 'brandingKeywordImpressions', label: this._translate.instant('aggregated_statistics.seo.keywords.branding') },
        { key: 'discoveryKeywordImpressions', label: this._translate.instant('aggregated_statistics.seo.keywords.discovery') },
    ];
    readonly defaultSort: WritableSignal<Sort> = signal({ active: 'currentTop20', direction: ChartSortBy.DESC });

    @ViewChild(MatSort, { static: false }) set matSort(sort: MatSort) {
        if (this.dataSource) {
            this.dataSource.sortingDataAccessor = (item, property): number | string => {
                const direction = this.dataSource.sort?.direction === 'desc' ? 1 : -1;
                if (item.restaurantId === this.ALL_RESTAURANTS_ROW) {
                    if (property === 'restaurantName') {
                        return this.dataSource.sort?.direction === 'desc' ? 'ÿ' : '!';
                    }
                    return Number.MAX_SAFE_INTEGER * direction;
                }
                return item[property] ?? 0;
            };
            this.dataSource.sort = sort;
        }
    }

    readonly SvgIcon = SvgIcon;
    readonly KeywordTableColumns = KeywordTableColumns;
    readonly KeywordTableGroupedColumns = KeywordTableGroupedColumns;
    readonly Illustration = Illustration;
    readonly PlatformKey = PlatformKey;
    readonly ALL_RESTAURANTS_ROW = 'all';

    readonly aggStatsFilters$: Observable<AggregatedStatisticsState['filters']> = this._store.select(
        AggregatedStatisticsSelector.selectFilters
    );
    readonly selectedRestaurants$: Observable<Restaurant[]> = this._aggregatedStatisticsFiltersContext.selectedRestaurants$;

    readonly warningTooltip: WritableSignal<string | undefined> = signal(undefined);
    readonly isLoading: WritableSignal<boolean> = signal(false);
    readonly hasData: WritableSignal<boolean> = signal(true);
    readonly errorMessage: WritableSignal<string | undefined> = signal(undefined);

    ngOnInit(): void {
        combineLatest([this.aggStatsFilters$, this.selectedRestaurants$])
            .pipe(
                tap(() => this._reset()),
                map(([filters, restaurants]: [AggregatedStatisticsState['filters'], Restaurant[]]) => {
                    const [businessRestaurants, nonBusinessRestaurants] = partition(restaurants, (r) => r.isBrandBusiness());
                    if (businessRestaurants.length) {
                        this.warningTooltip.set(this._computeWarningTooltip(businessRestaurants));
                    }
                    return [filters, nonBusinessRestaurants];
                }),
                map(([filters, restaurants]: [AggregatedStatisticsState['filters'], Restaurant[]]) => {
                    const { monthYearPeriod, comparisonPeriod } = filters;
                    return [restaurants, monthYearPeriod, comparisonPeriod];
                }),
                switchMap(([restaurants, monthYearPeriod, comparisonPeriod]: [Restaurant[], MonthYearPeriod, MalouComparisonPeriod]) => {
                    const restaurantIds = restaurants.filter((r) => !r.isBrandBusiness()).map((r) => r._id);
                    const startDate = DateTime.fromObject(monthYearPeriod.startMonthYear).startOf('month').toJSDate();
                    const endDate = DateTime.fromObject(monthYearPeriod.endMonthYear).endOf('month').toJSDate();

                    return forkJoin([
                        this._keywordsService.getAggregatedKeywordRankings({
                            restaurantIds,
                            startDate,
                            endDate,
                            platformKey: GeoSamplePlatform.GMAPS,
                        }),
                        this._keywordsService.getAggregatedKeywordRankings({
                            restaurantIds,
                            startDate,
                            endDate,
                            platformKey: GeoSamplePlatform.GMAPS,
                            comparisonPeriod,
                        }),
                        this._keywordSearchImpressionsService.getKeywordSearchImpressionsAggregatedInsights({
                            monthYearPeriod,
                            restaurantIds,
                            aggregationType: AggregationType.TOTAL,
                        }),
                        this._keywordSearchImpressionsService.getKeywordSearchImpressionsAggregatedInsights({
                            monthYearPeriod,
                            restaurantIds,
                            aggregationType: AggregationType.TOTAL,
                            comparisonPeriod,
                        }),
                    ]);
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: ([currentAggregatedRankings, previousAggregatedRankings, currentSearchImpressions, previousSearchImpressions]: [
                    AggregatedKeywordRankings,
                    AggregatedKeywordRankings,
                    KeywordSearchImpressionsTotal,
                    KeywordSearchImpressionsTotal,
                ]) => {
                    this.isLoading.set(false);
                    this.hasData.set(!!currentAggregatedRankings?.restaurants?.length);
                    this.errorMessage.set(undefined);
                    this.isLoadingEvent.emit(false);

                    if (this.hasData()) {
                        this._setTableDataSource({
                            currentAggregatedRankings,
                            previousAggregatedRankings,
                            currentSearchImpressions,
                            previousSearchImpressions,
                        });
                    }
                },
                error: (error) => {
                    this.errorMessage.set(this._aggregatedStatisticsHttpErrorPipe.transform(error));
                    this.isLoading.set(false);
                    this.hasData.set(false);
                    this.isLoadingEvent.emit(false);
                },
            });

        const tableSortOptions = this.tableSortOptions();
        if (tableSortOptions) {
            this.defaultSort.set(tableSortOptions);
        }
    }

    onSortByChange(sortBy: string): void {
        this.dataSource.sort?.sort({ id: sortBy, start: this.dataSource.sort.direction || ChartSortBy.ASC, disableClear: true });
    }

    onSortOrderChange(): void {
        const direction = this.dataSource.sort?.direction === ChartSortBy.ASC ? ChartSortBy.DESC : ChartSortBy.ASC;
        this.dataSource.sort?.sort({ id: this.dataSource.sort.active, start: direction, disableClear: true });
    }

    onSortChange(sort: Sort): void {
        this.tableSortOptionsChange.emit(sort);
    }

    goToRestaurantKeywordsStats(keywordTableDataRow: KeywordTableDataRowV2): void {
        if (keywordTableDataRow.restaurantId === this.ALL_RESTAURANTS_ROW) {
            return;
        }
        void this._router.navigate([`/restaurants/${keywordTableDataRow.restaurantId}/statistics/seo`]);
    }

    private _setTableDataSource({
        currentAggregatedRankings,
        previousAggregatedRankings,
        currentSearchImpressions,
        previousSearchImpressions,
    }: {
        currentAggregatedRankings: AggregatedKeywordRankings;
        previousAggregatedRankings: AggregatedKeywordRankings;
        currentSearchImpressions: KeywordSearchImpressionsTotal;
        previousSearchImpressions: KeywordSearchImpressionsTotal;
    }): void {
        const keywordTableDataRow: KeywordTableDataRowV2[] = currentAggregatedRankings.restaurants.map((restaurant) => {
            const { id } = restaurant;
            const previousData = previousAggregatedRankings.restaurants.find((r) => r.id === id);

            return new KeywordTableDataRowV2({
                restaurantId: id,
                restaurantName: restaurant.internalName ?? restaurant.name,
                keywords: [],
                keywordsTop20: [],
                keywordsTop3: [],
                currentTop20: restaurant.rankings.top20,
                currentTop3: restaurant.rankings.top3,
                previousTop20: previousData?.rankings.top20,
                previousTop3: previousData?.rankings.top3,
                brandingKeywordImpressions: currentSearchImpressions[id].total?.branding,
                discoveryKeywordImpressions: currentSearchImpressions[id].total?.discovery,
                previousBrandingKeywordImpressions: previousSearchImpressions[id].total?.branding,
                previousDiscoveryKeywordImpressions: previousSearchImpressions[id].total?.discovery,
            });
        });

        this._setAggregatedKeywordRankingData(keywordTableDataRow);
        this.dataSource = new MatTableDataSource([this._getAllRestaurantsRow(keywordTableDataRow), ...keywordTableDataRow]);
        this.dataSource.sort?.sort({ id: this.defaultSort().active, start: this.defaultSort().direction, disableClear: true });
        if (!keywordTableDataRow.length) {
            this.hasDataChange.emit(false);
        }
    }

    private _reset(): void {
        this.isLoading.set(true);
        this.isLoadingEvent.emit(true);
        this.hasData.set(false);
        this.errorMessage.set(undefined);
    }

    private _computeWarningTooltip(restaurants: Restaurant[] | undefined): string | undefined {
        if (!restaurants?.length) {
            return;
        }
        const restaurantsLabel = restaurants.map((e) => e.name).join(', ');
        return this._translate.instant('aggregated_statistics.errors.gmb_data_does_not_exist_for_business_restaurants', {
            restaurants: restaurantsLabel,
        });
    }

    private _getAllRestaurantsRow(rows: KeywordTableDataRowV2[]): KeywordTableDataRowV2 {
        const _sumRowsProp = (_rows: KeywordTableDataRowV2[], prop: string): number =>
            _rows.reduce((acc, next) => acc + (next[prop] ?? 0), 0);

        return new KeywordTableDataRowV2({
            restaurantId: this.ALL_RESTAURANTS_ROW,
            restaurantName: this._translate.instant('aggregated_statistics.seo.keywords.all_businesses'),
            currentTop20: _sumRowsProp(rows, 'currentTop20'),
            currentTop3: _sumRowsProp(rows, 'currentTop3'),
            previousTop20: _sumRowsProp(rows, 'previousTop20'),
            previousTop3: _sumRowsProp(rows, 'previousTop3'),
            brandingKeywordImpressions: _sumRowsProp(rows, 'brandingKeywordImpressions'),
            discoveryKeywordImpressions: _sumRowsProp(rows, 'discoveryKeywordImpressions'),
            previousBrandingKeywordImpressions: _sumRowsProp(rows, 'previousBrandingKeywordImpressions'),
            previousDiscoveryKeywordImpressions: _sumRowsProp(rows, 'previousDiscoveryKeywordImpressions'),
        });
    }

    private _setAggregatedKeywordRankingData(keywordTableDataRows: KeywordTableDataRowV2[]): void {
        this._store.dispatch(
            AggregatedStatisticsActions.editKeywordRankings({ data: keywordTableDataRows.map((r) => r.getAggregatedKeywordRakingData()) })
        );
    }
}
