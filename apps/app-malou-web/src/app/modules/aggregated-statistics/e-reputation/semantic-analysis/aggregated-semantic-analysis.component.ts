import { Async<PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import {
    ChangeDetectionStrategy,
    Component,
    computed,
    DestroyRef,
    effect,
    inject,
    input,
    OnInit,
    output,
    signal,
    WritableSignal,
} from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { environment } from 'environments/environment';
import { difference, isEqual, isEqualWith, partition } from 'lodash';
import { catchError, combineLatest, distinctUntilChanged, EMPTY, filter, map, Observable, of, switchMap, tap } from 'rxjs';

import { SegmentAnalysisInsightsForRestaurantDto } from '@malou-io/package-dto';
import {
    InsightsChart,
    isSameDay,
    MalouComparisonPeriod,
    PlatformFilterPage,
    PlatformKey,
    ReviewAnalysisChartDataTag,
} from '@malou-io/package-utils';

import { SegmentAnalysesService } from ':core/services/segment-analyses.service';
import { LocalStorage } from ':core/storage/local-storage';
import {
    RestaurantSemanticAnalysisInsights,
    SemanticAnalysisInsightsMapper,
} from ':modules/aggregated-statistics/e-reputation/semantic-analysis/aggregated-semantic-analysis-table/aggregated-semantic-analysis-insights.mapper';
import { AggregatedSemanticAnalysisTableComponent } from ':modules/aggregated-statistics/e-reputation/semantic-analysis/aggregated-semantic-analysis-table/aggregated-semantic-analysis-table.component';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import * as AggregatedStatisticsActions from ':modules/aggregated-statistics/store/aggregated-statistics.actions';
import * as AggregatedStatisticsSelectors from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { ReviewAnalysesChartDataByRestaurantId } from ':shared/components/review-analyses-v2/review-analyses-chart-data-by-restaurant-id/review-analyses-chart-data-by-restaurant-id';
import { ReviewAnalysesChartFilter } from ':shared/components/review-analyses-v2/review-analyses.interface';
import { TagsBarChartComponent } from ':shared/components/review-analyses-v2/tags-bar-chart/tags-bar-chart.component';
import { TagsDoughnutChartComponent } from ':shared/components/review-analyses-v2/tags-doughnut-chart/tags-doughnut-chart.component';
import { TagsEvolutionComponent } from ':shared/components/review-analyses-v2/tags-evolution/tags-evolution.component';
import { SkeletonComponent } from ':shared/components/skeleton/skeleton.component';
import { ChartSortBy } from ':shared/enums/sort.enum';
import { Restaurant } from ':shared/models';
import { SvgIcon } from ':shared/modules/svg-icon.enum';
import { Illustration, IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { IncludesPipe } from ':shared/pipes/includes.pipe';

@Component({
    selector: 'app-aggregated-semantic-analysis',
    imports: [
        MatIconModule,
        MatTooltipModule,
        NgTemplateOutlet,
        TranslateModule,
        AggregatedSemanticAnalysisTableComponent,
        SkeletonComponent,
        TagsBarChartComponent,
        TagsDoughnutChartComponent,
        TagsEvolutionComponent,
        IllustrationPathResolverPipe,
        AsyncPipe,
        IncludesPipe,
    ],
    templateUrl: './aggregated-semantic-analysis.component.html',
    styleUrl: './aggregated-semantic-analysis.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AggregatedSemanticAnalysisComponent implements OnInit {
    readonly isPdfDownload = input<boolean>(false);
    readonly displayedCharts = input<InsightsChart[]>([]);
    readonly tagsEvolutionSortBy = input<ChartSortBy | undefined>(undefined);
    readonly isLoadingEvent = output<boolean>();
    readonly tagsEvolutionSortByChange = output<ChartSortBy>();

    private readonly _store = inject(Store);
    private readonly _translate = inject(TranslateService);
    private readonly _segmentAnalysesService = inject(SegmentAnalysesService);
    private readonly _destroyRef = inject(DestroyRef);
    private readonly _aggregatedStatisticsFiltersContext = inject(AggregatedStatisticsFiltersContext);

    readonly Illustration = Illustration;
    readonly SvgIcon = SvgIcon;
    readonly InsightsChart = InsightsChart;

    readonly currentLang = signal(LocalStorage.getLang());
    readonly detailHref = environment.BASE_URL + '/groups/statistics/e-reputation';
    readonly isLoadingChart: WritableSignal<boolean> = signal(false);
    readonly isLoadingTable: WritableSignal<boolean> = signal(false);
    readonly isLoading = computed(() => this.isLoadingChart() || this.isLoadingTable());
    readonly warningTooltip = signal<string | undefined>(undefined);
    readonly reviewAnalysesChartData = signal<ReviewAnalysesChartDataByRestaurantId>(ReviewAnalysesChartDataByRestaurantId.fromDto({}));
    readonly semanticAnalysisTableData = signal<RestaurantSemanticAnalysisInsights[]>([]);
    readonly currentFilter = signal<ReviewAnalysesChartFilter | null>(null);
    readonly hasTableData = signal(false);
    readonly hasChartData = computed(() => this.reviewAnalysesChartData().hasData(ReviewAnalysisChartDataTag.TOTAL));

    readonly dates$: Observable<{ startDate: Date | null; endDate: Date | null }> = this._store
        .select(AggregatedStatisticsSelectors.selectDatesFilter)
        .pipe(
            distinctUntilChanged((prev, curr) => {
                const isSameStartDate = !!prev.startDate && !!curr.startDate && isSameDay(prev.startDate, curr.startDate);
                const isSameEndDate = !!prev.endDate && !!curr.endDate && isSameDay(prev.endDate, curr.endDate);
                return isSameStartDate && isSameEndDate;
            })
        );

    readonly platformKeys$: Observable<PlatformKey[]> = this._store
        .select(AggregatedStatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.E_REPUTATION }))
        .pipe(
            distinctUntilChanged((prev, curr) => prev.length === curr.length && isEqual(prev, curr)),
            map((platforms) => {
                if (platforms?.length) {
                    return platforms;
                }
                return Object.values(PlatformKey);
            })
        );

    readonly selectedRestaurants$: Observable<Restaurant[]> = this._aggregatedStatisticsFiltersContext.selectedRestaurants$.pipe(
        distinctUntilChanged((prev, curr) => prev.length === curr.length && isEqualWith(prev, curr, (c, d) => c.id === d.id))
    );

    readonly comparisonPeriod$: Observable<MalouComparisonPeriod> = this._store
        .select(AggregatedStatisticsSelectors.selectComparisonPeriod)
        .pipe(distinctUntilChanged());

    readonly selectedRestaurants = toSignal(this.selectedRestaurants$, { initialValue: [] });

    readonly reviewAnalysesFilter = signal<ReviewAnalysesChartFilter | null>(null);

    constructor() {
        effect(() => {
            this.isLoadingEvent.emit(this.isLoading());
        });
    }

    onTableLoadingChange(isLoading: boolean): void {
        this.isLoadingTable.set(isLoading);
    }

    ngOnInit(): void {
        this._buildSemanticAnalysisDataForTable();
        this._buildSemanticAnalysisDataForCharts();
    }

    private _buildSemanticAnalysisDataForCharts(): void {
        combineLatest([
            this.dates$.pipe(tap(() => this._resetChartData())),
            this.platformKeys$.pipe(tap(() => this._resetChartData())),
            this.selectedRestaurants$,
        ])
            .pipe(
                filter(
                    ([dates, platforms, restaurants]) =>
                        !!dates.startDate && !!dates.endDate && platforms.length > 0 && !!restaurants.length
                ),
                tap(() => {
                    this._reset();
                }),
                map(([dates, platforms, restaurants]) => {
                    const [businessRestaurant, nonBusinessRestaurants] = partition(restaurants, (r) => r.isBrandBusiness());
                    this.warningTooltip.set(this._computeWarningTooltip(businessRestaurant));
                    const restaurantIdsWithDataAlreadyFetched = [...this.reviewAnalysesChartData().keys()];
                    const restaurantIdsWeNeedToFetch = difference(
                        nonBusinessRestaurants.map((r) => r.id),
                        restaurantIdsWithDataAlreadyFetched
                    );
                    if (!dates.startDate || !dates.endDate) {
                        return EMPTY;
                    }
                    this._removeRestaurantIdsFromChartData(nonBusinessRestaurants, restaurantIdsWithDataAlreadyFetched);
                    this.reviewAnalysesFilter.set({
                        startDate: dates.startDate,
                        endDate: dates.endDate,
                        keys: platforms,
                        restaurantIds: nonBusinessRestaurants.map((r) => r.id),
                    });
                    return [dates, platforms, restaurantIdsWeNeedToFetch];
                }),
                filter(
                    ([_dates, _platforms, restaurantIds]: [{ startDate: Date | null; endDate: Date | null }, PlatformKey[], string[]]) => {
                        if (restaurantIds.length === 0) {
                            this.isLoadingChart.set(false);
                            return false;
                        }
                        return true;
                    }
                ),
                switchMap(([dates, platforms, restaurantIdsWeNeedToFetch]) => {
                    const { startDate, endDate } = dates;
                    if (!startDate || !endDate) {
                        return EMPTY;
                    }
                    const emptyData = this._buildEmptyChartData(restaurantIdsWeNeedToFetch);
                    return this._segmentAnalysesService
                        .getSegmentAnalysesChartData({
                            startDate,
                            endDate,
                            keys: platforms,
                            restaurantIds: restaurantIdsWeNeedToFetch,
                        })
                        .pipe(map((res) => emptyData.merge(ReviewAnalysesChartDataByRestaurantId.fromDto(res.data))));
                }),
                catchError((error) => {
                    this.isLoadingChart.set(false);
                    console.error('Error while fetching review analyses chart data', error);
                    return of(ReviewAnalysesChartDataByRestaurantId.fromDto({}));
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (newReviewAnalysesChartData: ReviewAnalysesChartDataByRestaurantId) => {
                    this.reviewAnalysesChartData.update((reviewAnalysesChartData) =>
                        reviewAnalysesChartData.merge(newReviewAnalysesChartData)
                    );
                    this.isLoadingChart.set(false);
                },
                error: () => {
                    this.isLoadingChart.set(false);
                },
            });
    }

    private _computeWarningTooltip(restaurants: Restaurant[]): string | undefined {
        if (!restaurants.length) {
            return;
        }
        const restaurantsLabel = restaurants.map((e) => e.internalName ?? e.name).join(', ');
        return this._translate.instant('aggregated_statistics.errors.gmb_data_does_not_exist_for_business_restaurants', {
            restaurants: restaurantsLabel,
        });
    }

    private _reset(): void {
        this.isLoadingChart.set(true);
        this.warningTooltip.set(undefined);
    }

    private _resetChartData(): void {
        this.reviewAnalysesChartData.set(ReviewAnalysesChartDataByRestaurantId.fromDto({}));
    }

    /**
     * Return empty data for all restaurants
     * Useful because if we don't retrieve result for a restaurant,
     * we will always do a call for this restaurant when changing the date or platformKeys
     */
    private _buildEmptyChartData(restaurantIds: string[]): ReviewAnalysesChartDataByRestaurantId {
        return ReviewAnalysesChartDataByRestaurantId.buildEmptyChartData(restaurantIds);
    }

    private _removeRestaurantIdsFromChartData(nonBusinessRestaurants: Restaurant[], restaurantIdsWithDataAlreadyFetched: string[]): void {
        const restaurantIdsToDelete = difference(
            restaurantIdsWithDataAlreadyFetched,
            nonBusinessRestaurants.map((r) => r.id)
        );
        if (restaurantIdsToDelete.length) {
            this.reviewAnalysesChartData.update((reviewAnalysesChartData) => reviewAnalysesChartData.deleteMany(restaurantIdsToDelete));
            this.reviewAnalysesFilter.update((filters) =>
                filters
                    ? {
                          ...filters,
                          restaurantIds: difference(filters.restaurantIds, restaurantIdsToDelete),
                      }
                    : null
            );
        }
    }

    private _buildSemanticAnalysisDataForTable(): void {
        let selectedRestaurants: Restaurant[] = [];
        let startDate: Date | null;
        let endDate: Date | null;
        combineLatest([this.dates$, this.comparisonPeriod$, this.platformKeys$, this.selectedRestaurants$])
            .pipe(
                filter(
                    ([dates, comparisonPeriod, platforms, restaurants]) =>
                        !!restaurants.length && !!dates.startDate && !!dates.endDate && !!comparisonPeriod && platforms.length > 0
                ),
                tap(() => {
                    this.isLoadingTable.set(true);
                    this.hasTableData.set(false);
                }),
                switchMap(([dates, comparisonPeriod, platforms, restaurants]) => {
                    startDate = dates.startDate;
                    endDate = dates.endDate;
                    if (!startDate || !endDate) {
                        return EMPTY;
                    }
                    selectedRestaurants = restaurants;
                    const restaurantIds = restaurants
                        .filter((restaurant) => !restaurant.isBrandBusiness())
                        .map((restaurant) => restaurant._id);
                    this.currentFilter.set({
                        startDate,
                        endDate,
                        keys: platforms,
                        restaurantIds,
                    });
                    return this._segmentAnalysesService
                        .getAggregatedSegmentAnalysisForRestaurants({
                            startDate,
                            endDate,
                            platformKeys: platforms,
                            restaurantIds,
                            comparisonPeriod,
                        })
                        .pipe(
                            map((res) => res.data),
                            catchError((error) => {
                                console.error('Error while fetching aggregated segment analyses data', error);
                                this.isLoadingTable.set(false);
                                this.hasTableData.set(false);
                                return of([]);
                            })
                        );
                }),
                takeUntilDestroyed(this._destroyRef)
            )
            .subscribe({
                next: (aggregatedSegmentAnalysisForRestaurant: SegmentAnalysisInsightsForRestaurantDto[]) => {
                    const mappedData = SemanticAnalysisInsightsMapper.createSegmentAnalysisInsightsFromDtoArray(
                        aggregatedSegmentAnalysisForRestaurant,
                        selectedRestaurants,
                        this.currentLang()
                    );
                    this.semanticAnalysisTableData.set(mappedData);
                    this._store.dispatch(
                        AggregatedStatisticsActions.editSemanticAnalysisData({
                            data: aggregatedSegmentAnalysisForRestaurant,
                        })
                    );
                    this.isLoadingTable.set(false);
                    this.hasTableData.set(true);
                },
                error: () => {
                    this.isLoadingTable.set(false);
                    this.hasTableData.set(false);
                },
            });
    }
}
