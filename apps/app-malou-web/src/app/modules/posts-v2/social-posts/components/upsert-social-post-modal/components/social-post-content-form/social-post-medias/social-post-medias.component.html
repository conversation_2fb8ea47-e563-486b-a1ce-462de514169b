@if (isDragging()) {
    <ng-container [ngTemplateOutlet]="dragOverTemplate"></ng-container>
} @else if (medias().length === 0 && uploadingMediaCount() === 0) {
    <ng-container [ngTemplateOutlet]="addFirstMediaTemplate"></ng-container>
} @else {
    @switch (publicationType()) {
        @case (PublicationType.POST) {
            <div class="rounded-[5px] border border-malou-color-background-dark bg-malou-color-background-light p-3">
                <app-post-media-list
                    [(medias)]="medias"
                    [uploadingMediaCount]="uploadingMediaCount()"
                    [showEditMediaButton]="options().post?.showEditMediaButton ?? true"
                    [isReadonly]="isReadonly()"
                    (openFilePicker)="fileInput.click()"
                    (importFromGallery)="onImportMediaFromGallery()"
                    (mediaClicked)="postMediaClicked.emit($event)"></app-post-media-list>
            </div>
        }
        @case (PublicationType.REEL) {
            <app-reel-media
                [mediaName]="reelMedia()?.name"
                [isLoading]="uploadingMediaCount() > 0"
                [isReadonly]="isReadonly()"
                (deleteMedia)="onDeleteReelMedia()"></app-reel-media>
        }
    }
}

<ng-template #addFirstMediaTemplate>
    <div class="rounded-[5px] border border-malou-color-background-dark bg-malou-color-background-light p-3">
        <app-add-media
            [isReadonly]="isReadonly()"
            [onlyVideoText]="shouldHandleVideoOnly()"
            (importMediaFromComputer)="fileInput.click()"
            (importMediaFromGallery)="onImportMediaFromGallery()"></app-add-media>
    </div>
</ng-template>

<ng-template #dragOverTemplate>
    <div class="rounded-[5px] border border-dotted border-malou-color-primary bg-malou-color-background-light p-3">
        <div class="flex h-[75px] items-center gap-x-5 rounded-md bg-white p-4">
            <mat-icon class="!h-14 !w-14 !fill-malou-color-background-dark" [svgIcon]="SvgIcon.IMAGES"></mat-icon>
            <div class="malou-text-11--semibold text-malou-color-text-1">
                {{ 'social_post_medias.drag_and_drop_here' | translate }}
            </div>
        </div>
    </div>
</ng-template>

<input
    type="file"
    hidden
    [multiple]="multipleMediaSelection()"
    [accept]="acceptAttribute()"
    [disabled]="isReadonly()"
    (change)="onImportFromFile($event)"
    #fileInput />
