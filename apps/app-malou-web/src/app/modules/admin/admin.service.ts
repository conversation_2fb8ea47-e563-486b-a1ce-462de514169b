import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { ImpersonateUserResponseDto } from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { environment } from ':environments/environment';

import { User } from '../user/user';

@Injectable({ providedIn: 'root' })
export class AdminService {
    readonly API_BASE_URL = `${environment.APP_MALOU_API_URL}/api/v1/`;

    constructor(private readonly _http: HttpClient) {}

    createAccount(user: Partial<User>): Observable<any> {
        return this._http.post(this.API_BASE_URL + 'users/new_account', { user });
    }

    updateAccount(userId: string, params: any): Observable<any> {
        return this._http.put(this.API_BASE_URL + 'users/admin/' + userId, params);
    }

    impersonateUser(targetUserId: string): Observable<ApiResultV2<ImpersonateUserResponseDto>> {
        return this._http.post<ApiResultV2<ImpersonateUserResponseDto>>(`${this.API_BASE_URL}users/admin/impersonate/`, {
            userId: targetUserId,
        });
    }

    sendConfirmationEmail(userId: string): Observable<ApiResultV2<void>> {
        return this._http.post<ApiResultV2<void>>(`${this.API_BASE_URL}users/admin/resend-confirmation-email/`, {
            userId,
        });
    }
}
