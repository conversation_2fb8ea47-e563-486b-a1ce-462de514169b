<ng-container *ngTemplateOutlet="hasFetchedUsers() ? page : loading"></ng-container>

<ng-template #loading>
    <div class="flex flex-col gap-4 px-7.5 py-4">
        <app-skeleton skeletonClass="secondary-bg h-[50px] w-full"></app-skeleton>

        <app-skeleton
            flexDirection="flex-row"
            skeletonClass="secondary-bg h-[15px] w-[7vw]"
            justifyContent="justify-around"
            [count]="4"></app-skeleton>

        <app-skeleton skeletonClass="secondary-bg h-[50px] w-full" gapClass="gap-[5px]" [count]="10"></app-skeleton>
    </div>
</ng-template>

<ng-template #page>
    <div class="flex h-full flex-col py-4">
        <ng-container [ngTemplateOutlet]="header"></ng-container>

        <div class="flex min-h-0 grow flex-col px-7.5">
            <div class="hidden md:mb-2 md:block">
                <app-sort-by-filters
                    [sortOptions]="SORT_OPTIONS"
                    [sortBy]="TABLE_COLUMNS.NAME"
                    [sortOrder]="1"
                    (changeSortBy)="onSortByChange($event)"
                    (toggleSortOrder)="onSortOrderChange()">
                </app-sort-by-filters>
            </div>

            @if (dataSource.filteredData.length) {
                <mat-table class="malou-mat-table pb-6" matSort [dataSource]="dataSource" #table="matTable">
                    <ng-container [matColumnDef]="RoleManagerTableColumns.AVATAR">
                        <mat-header-cell *matHeaderCellDef></mat-header-cell>
                        <mat-cell *matCellDef="let element; table: table" mat-cell>
                            <img
                                class="malou-avatar--medium my-2.5 ml-1.5 !rounded-[5px]"
                                [lazyLoad]="element.avatar || ('Karl' | illustrationPathResolver)" />
                        </mat-cell>
                    </ng-container>
                    <ng-container [matColumnDef]="RoleManagerTableColumns.LASTNAME">
                        <mat-header-cell *matHeaderCellDef mat-sort-header>
                            <div>
                                {{ 'roles.manager.lastname' | translate }}
                            </div>
                        </mat-header-cell>
                        <mat-cell *matCellDef="let element; table: table" class="malou-color-text-1 malou-text-weight-semibold" mat-cell>
                            {{ element.lastname }}
                        </mat-cell>
                    </ng-container>
                    <ng-container [matColumnDef]="RoleManagerTableColumns.NAME">
                        <mat-header-cell *matHeaderCellDef mat-sort-header>
                            <div>
                                {{ 'roles.manager.name_header' | translate }}
                            </div>
                        </mat-header-cell>
                        <mat-cell *matCellDef="let element; table: table" mat-cell>
                            {{ element.name }}
                        </mat-cell>
                    </ng-container>
                    <ng-container [matColumnDef]="RoleManagerTableColumns.EMAIL">
                        <mat-header-cell *matHeaderCellDef mat-sort-header>
                            <div>
                                {{ 'roles.manager.email_header' | translate }}
                            </div>
                        </mat-header-cell>
                        <mat-cell *matCellDef="let element; table: table" mat-cell>
                            <span class="malou-color-text-1 malou-text-weight-semibold mr-1.5 hidden md:inline">
                                {{ 'roles.manager.email_header' | translate }} :
                            </span>
                            <span data-testid="roles-manager-email">
                                {{ element.email }}
                            </span>
                        </mat-cell>
                    </ng-container>
                    <ng-container [matColumnDef]="RoleManagerTableColumns.UR_ROLE">
                        <mat-header-cell *matHeaderCellDef mat-sort-header>
                            <div>
                                {{ 'roles.manager.casl_role' | translate }}
                            </div>
                        </mat-header-cell>
                        <mat-cell *matCellDef="let element; table: table" mat-cell>
                            <mat-form-field class="custom-malou-filled-form-field !mb-[-10px] h-[36px] w-9/12" subscriptSizing="dynamic">
                                <mat-select
                                    panelClass="malou-select-panel"
                                    [(value)]="element.urRole"
                                    [disabled]="!(CaslAction.UPDATE | caslAble: CaslSubject.USER_RESTAURANT)"
                                    [hideSingleSelectionIndicator]="true"
                                    (openedChange)="selectRoleOpen(element.urRole, $event)"
                                    (selectionChange)="changeRole($event, element)">
                                    @for (role of CASL_ROLES; track role) {
                                        <mat-option [attr.data-testid]="'casl-role-option-' + role.key" [value]="role.key">
                                            {{ role.text }}
                                        </mat-option>
                                    }
                                </mat-select>
                            </mat-form-field>
                        </mat-cell>
                    </ng-container>
                    <ng-container [matColumnDef]="RoleManagerTableColumns.VERIFIED_STATUS">
                        <mat-header-cell *matHeaderCellDef mat-sort-header>
                            <div>
                                {{ 'roles.manager.verified_status' | translate }}
                            </div>
                        </mat-header-cell>
                        <mat-cell *matCellDef="let userRestaurant; table: table" mat-cell>
                            <div
                                class="malou-text-10--medium flex h-6 items-center gap-x-2 rounded-[3px] px-2"
                                [ngClass]="{
                                    'bg-malou-color-background-success text-malou-color-text-green': !!userRestaurant.user.verified,
                                    'bg-malou-color-background-warning text-malou-color-state-warn': !userRestaurant.user.verified,
                                }">
                                @if (!!userRestaurant.user.verified) {
                                    <mat-icon class="!h-4 !w-4" [svgIcon]="SvgIcon.CHECK"></mat-icon>
                                    {{ 'roles.manager.verified' | translate }}
                                } @else {
                                    <mat-icon class="!h-4 !w-4" [svgIcon]="SvgIcon.TIMER"></mat-icon>
                                    {{ 'roles.manager.not_verified' | translate }}
                                }
                            </div>
                        </mat-cell>
                    </ng-container>
                    <ng-container [matColumnDef]="RoleManagerTableColumns.ACTIONS">
                        <mat-header-cell *matHeaderCellDef></mat-header-cell>
                        <mat-cell *matCellDef="let element; table: table" mat-cell>
                            @if (isUserOwner()) {
                                <ng-container
                                    [ngTemplateOutlet]="actionMenuTemplate"
                                    [ngTemplateOutletContext]="{
                                        element,
                                    }"></ng-container>
                            }
                        </mat-cell>
                    </ng-container>
                    <mat-header-row *matHeaderRowDef="DISPLAYED_COLUMNS"></mat-header-row>
                    <mat-row *matRowDef="let row; columns: DISPLAYED_COLUMNS; table: table"></mat-row>
                </mat-table>
            } @else {
                <div class="flex h-full flex-col items-center justify-center">
                    <img class="h-32" [src]="'Goggles' | illustrationPathResolver" />
                    <p class="malou-text-14--bold mb-4 mt-9 text-center">
                        {{ 'roles.manager.no_users' | translate }}
                    </p>
                </div>
            }
        </div>
    </div>
</ng-template>

<ng-template #header>
    <div class="mb-4 flex justify-between px-7.5">
        <app-search
            class="mx-2.5 grow sm:basis-full md:ml-0"
            [placeholder]="'common.search' | translate"
            [debounceTime]="500"
            (searchChange)="onSearchChange($event)"></app-search>
        <div class="flex lg:hidden" [matTooltip]="addUserBtnTooltip()" [matTooltipDisabled]="!shouldDisableAddUserBtn">
            <button
                class="malou-btn-raised--primary !h-12.5"
                data-testid="add-user-btn"
                mat-raised-button
                [disabled]="shouldDisableAddUserBtn()"
                [class.opacity-50]="shouldDisableAddUserBtn()"
                (click)="openAddUser()">
                {{ 'roles.add_user_btn' | translate }}
            </button>
        </div>
        <div class="hidden lg:flex" [matTooltip]="addUserBtnTooltip()" [matTooltipDisabled]="!shouldDisableAddUserBtn">
            <button
                class="malou-btn-icon--primary btn-xl"
                data-testid="add-user-btn-icon"
                mat-icon-button
                [disabled]="shouldDisableAddUserBtn()"
                [class.opacity-50]="shouldDisableAddUserBtn()"
                (click)="openAddUser()">
                <mat-icon [svgIcon]="SvgIcon.ADD"></mat-icon>
            </button>
        </div>
    </div>
</ng-template>

<ng-template let-element="element" #actionMenuTemplate>
    <div class="flex items-center">
        <mat-icon
            class="!h-4 !w-4 cursor-pointer"
            color="primary"
            [svgIcon]="SvgIcon.ELLIPSIS"
            [matMenuTriggerFor]="actionsMenu"
            (click)="$event.stopPropagation()"
            (pointerdown)="$event.stopPropagation()"></mat-icon>

        <mat-menu class="malou-mat-menu custom-mat-menu malou-box-shadow !rounded-[10px]" xPosition="before" #actionsMenu="matMenu">
            <button class="malou-menu-item !min-h-[40px]" mat-menu-item (click)="openAddUser(element.userId)">
                <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.EDIT"></mat-icon>
                <span class="malou-color-text-2 malou-text-14--regular"> {{ 'roles.manager.actions.edit' | translate }} </span>
            </button>
            @if (isCurrentUserOrganizationOwner() && element.userId !== currentUserInfos()?._id) {
                <button class="malou-menu-item !min-h-[40px]" mat-menu-item (click)="openOrganizationSettings(element)">
                    <mat-icon class="!h-4 !w-4" color="primary" [svgIcon]="SvgIcon.PADLOCK_OPEN"></mat-icon>
                    <span class="malou-color-text-2 malou-text-14--regular">
                        {{ 'roles.manager.actions.organization_settings' | translate }}
                    </span>
                </button>
            }
            <ng-container
                [ngTemplateOutlet]="deleteMenuTemplate"
                [ngTemplateOutletContext]="{
                    element,
                }"></ng-container>
        </mat-menu>
    </div>
</ng-template>

<ng-template let-element="element" #deleteMenuTemplate>
    <button
        class="malou-menu-item !min-h-[40px] cursor-pointer"
        mat-menu-item
        [matMenuTriggerFor]="deleteMenu"
        (click)="$event.stopPropagation()"
        (pointerdown)="$event.stopPropagation()">
        <div class="flex items-center">
            <mat-icon class="!h-4 !w-4" color="warn" [svgIcon]="SvgIcon.TRASH"></mat-icon>
            <span class="malou-color-text-2 malou-text-14--regular"> {{ 'roles.manager.actions.pull_out' | translate }} </span>
            <mat-icon class="!mr-0 ml-4 !h-4 !w-4" color="primary" [svgIcon]="SvgIcon.CHEVRON_RIGHT"></mat-icon>
        </div>
    </button>
    <mat-menu class="malou-mat-menu custom-mat-menu malou-box-shadow !rounded-[10px]" xPosition="after" #deleteMenu="matMenu">
        <button class="malou-menu-item !min-h-[40px]" mat-menu-item (click)="pullOut(element)">
            <span class="malou-color-text-2 malou-text-14--regular">
                {{ 'roles.manager.actions.pull_out_from_this_location' | translate }}
            </span>
        </button>
        <button class="malou-menu-item !min-h-[40px]" mat-menu-item (click)="pullOut(element, true)">
            <span class="malou-color-text-2 malou-text-14--regular">
                {{ 'roles.manager.actions.pull_out_from_all_my_locations' | translate }}
            </span>
        </button>
    </mat-menu>
</ng-template>
