import { Component, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { ActivatedRoute, Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { TranslateModule } from '@ngx-translate/core';

import { PHONE_CODES } from ':core/constants';
import { ToastService } from ':core/services/toast.service';
import { UsersService } from ':modules/user/users.service';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { PhoneInputComponent } from ':shared/components/phone-input/phone-input.component';
import { getFormControlRecordFromDefaultValue } from ':shared/helpers/form-control-from-default-value';
import { INullableFormGroup } from ':shared/interfaces/form-control-record.interface';
import { HttpErrorPipe } from ':shared/pipes/http-error.pipe';
import { IllustrationPathResolverPipe } from ':shared/pipes/illustration-path-resolver.pipe';
import { ImagePathResolverPipe } from ':shared/pipes/image-path-resolver.pipe';

import * as UserActions from '../store/user.actions';
import { UserState } from '../store/user.reducer';
import { User } from '../user';

type PhoneCodeType = (typeof PHONE_CODES)[number];
interface MiscPhoneForm {
    prefix: PhoneCodeType | null;
    digits: string | number | null;
}
interface MiscForm {
    name: string;
    lastname: string;
    phone: INullableFormGroup<MiscPhoneForm>;
}

@Component({
    selector: 'app-edit-user-miscellaneous',
    templateUrl: './edit-user-miscellaneous.component.html',
    styleUrls: ['./edit-user-miscellaneous.component.scss'],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        InputTextComponent,
        PhoneInputComponent,
        MatButtonModule,
        IllustrationPathResolverPipe,
        ImagePathResolverPipe,
        TranslateModule,
    ],
})
export class EditUserMiscellaneousComponent implements OnInit {
    public miscForm: INullableFormGroup<MiscForm>;
    public user: User;
    public PHONE_CODES = [...PHONE_CODES];

    constructor(
        private readonly _activatedRoute: ActivatedRoute,
        private readonly _usersService: UsersService,
        private readonly _router: Router,
        private readonly _fb: FormBuilder,
        private readonly _store: Store<{ user: UserState }>,
        private readonly _toastService: ToastService,
        private readonly _httpErrorPipe: HttpErrorPipe
    ) {}

    ngOnInit(): void {
        this._activatedRoute.data.subscribe((val) => {
            this.user = val.user;
            const formRecord = getFormControlRecordFromDefaultValue({
                name: this.user.name || '',
                lastname: this.user.lastname || '',
                phone: this._fb.group({
                    prefix: null,
                    digits: null,
                }) as INullableFormGroup<MiscPhoneForm>,
            });
            this.miscForm = this._fb.group(formRecord);
        });
    }

    cancel(): void {
        this._router.navigate(['..'], { relativeTo: this._activatedRoute });
    }

    updateMisc(): void {
        const phoneValue = this.miscForm.get('phone')?.value;
        this._usersService
            .updateUser$(this.user._id, {
                name: this.miscForm.value.name ?? undefined,
                lastname: this.miscForm.value.lastname ?? undefined,
                phone:
                    phoneValue?.prefix && phoneValue?.digits
                        ? {
                              prefix: phoneValue.prefix.code,
                              digits: parseInt(String(phoneValue.digits), 10),
                          }
                        : undefined,
            })
            .subscribe({
                next: (res: any) => {
                    this._store.dispatch(UserActions.editUserInfos({ infos: new User(res.data) }));
                    this._router.navigate(['.']);
                },
                error: (err) => {
                    console.warn('err :>>', err);
                    this._toastService.openErrorToast(this._httpErrorPipe.transform(err?.error?.message || err?.message || err));
                },
            });
    }
}
