import { ChangeDetectionStrategy, Component, input, output } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { TranslateModule } from '@ngx-translate/core';

import { SvgIcon } from ':shared/modules/svg-icon.enum';

@Component({
    selector: 'app-add-media',
    templateUrl: './add-media.component.html',
    imports: [MatIconModule, MatMenuModule, TranslateModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AddMediaComponent {
    readonly isReadonly = input<boolean>(false);
    readonly onlyVideoText = input<boolean>(false);

    readonly importMediaFromComputer = output<void>();
    readonly importMediaFromGallery = output<void>();

    readonly SvgIcon = SvgIcon;

    onImportMediaFromGallery(): void {
        if (this.isReadonly()) {
            return;
        }
        this.importMediaFromGallery.emit();
    }

    onImportMediaFromComputer(): void {
        if (this.isReadonly()) {
            return;
        }
        this.importMediaFromComputer.emit();
    }
}
