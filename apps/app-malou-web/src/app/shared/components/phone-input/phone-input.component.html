@if (isInitialized()) {
    <div class="flex gap-x-2" [formGroup]="phoneGroup">
        <div class="w-1/3">
            <app-select
                formControlName="prefix"
                [idPrefix]="idPrefix()"
                [title]="'admin.users.phone_prefix' | translate"
                [placeholder]="'admin.users.phone_prefix' | translate"
                [values]="PHONE_CODES"
                [displayWith]="phoneCodesDisplayWith">
            </app-select>
        </div>
        <div class="grow">
            <app-input-text
                formControlName="digits"
                [inputType]="'tel'"
                [testId]="testId()"
                [errorMessage]="digitsErrorMessage()"
                [title]="'admin.users.phone' | translate"
                [prefix]="formattedPrefix()"
                [placeholder]="'admin.users.phone' | translate">
            </app-input-text>
        </div>
    </div>
}
