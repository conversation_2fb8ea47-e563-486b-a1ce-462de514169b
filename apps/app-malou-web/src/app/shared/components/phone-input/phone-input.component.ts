import { Component, DestroyRef, inject, input, OnInit, signal, WritableSignal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';

import { PHONE_CODES } from ':core/constants';
import { InputTextComponent } from ':shared/components/input-text/input-text.component';
import { SelectComponent } from ':shared/components/select/select.component';
import { PhoneNumberValidator, PhoneNumberValidatorErrors } from ':shared/validators/phone-number.validator';

type PhoneCode = (typeof PHONE_CODES)[number];

@Component({
    selector: 'app-phone-input',
    standalone: true,
    templateUrl: './phone-input.component.html',
    styleUrls: ['./phone-input.component.scss'],
    imports: [FormsModule, ReactiveFormsModule, SelectComponent, InputTextComponent, TranslateModule],
})
export class PhoneInputComponent implements OnInit {
    readonly parentForm = input.required<FormGroup>();

    readonly initialPhone = input<Partial<{ prefix: number | null; digits: number | null }> | undefined>({});

    readonly idPrefix = input<string | undefined>();
    readonly testId = input<string | undefined>();

    readonly PHONE_CODES = [...PHONE_CODES];

    digitsErrorMessage: WritableSignal<string | undefined> = signal(undefined);
    formattedPrefix: WritableSignal<string> = signal('');
    isInitialized = signal(false);
    private readonly _destroyRef = inject(DestroyRef);

    get phoneGroup(): FormGroup {
        return this.parentForm().get('phone') as FormGroup;
    }

    constructor(private readonly _translateService: TranslateService) {}

    ngOnInit(): void {
        const init = this.initialPhone();
        const defaultPrefix = init?.prefix
            ? (this.PHONE_CODES.find((pc) => pc.code === init.prefix!) ?? this.PHONE_CODES[0])
            : this.PHONE_CODES[0];
        this._updateFormattedPrefix(defaultPrefix);
        const defaultDigits = init?.digits != null ? parseInt(String(init.digits), 10) : null;

        const group = new FormGroup(
            {
                prefix: new FormControl<PhoneCode | null>(defaultPrefix),
                digits: new FormControl<string | null>(defaultDigits?.toString() || null),
            },
            [PhoneNumberValidator('prefix', 'digits', true)]
        );

        this.parentForm().setControl('phone', group);

        group
            .get('prefix')
            ?.valueChanges.pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe((val) => {
                this._updateFormattedPrefix(val);
            });

        group
            .get('digits')
            ?.statusChanges.pipe(takeUntilDestroyed(this._destroyRef))
            .subscribe(() => {
                const digitsErrors = group.get('digits')?.errors;
                if (digitsErrors?.['error'] === PhoneNumberValidatorErrors.INVALID_PHONE_NUMBER) {
                    this.digitsErrorMessage.set(this._translateService.instant('information.information.invalid_phone_number'));
                } else {
                    this.digitsErrorMessage.set(undefined);
                }
            });

        this.isInitialized.set(true);
    }

    private _updateFormattedPrefix(phoneCode: PhoneCode | null): void {
        this.formattedPrefix.set(`+${phoneCode?.code ?? ''}`);
    }

    phoneCodesDisplayWith(phoneCode: any): string {
        return phoneCode?.text || '';
    }
}
