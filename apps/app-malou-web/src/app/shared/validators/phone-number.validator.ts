import { AbstractControl, FormGroup, ValidationErrors, ValidatorFn } from '@angular/forms';
import { parsePhoneNumberFromString } from 'libphonenumber-js';

export enum PhoneNumberValidatorErrors {
    INVALID_PHONE_NUMBER = 'INVALID_PHONE_NUMBER',
}

/**
 * Validator for phone number using libphonenumber-js
 * Validates the combination of prefix and digits to ensure it's a valid phone number
 *
 * @param prefixControlName - Name of the control containing the phone prefix (default: 'prefix')
 * @param digitsControlName - Name of the control containing the phone digits (default: 'digits')
 * @param allowEmpty - Whether to allow empty phone numbers (default: true)
 * @returns ValidatorFn
 */
export function PhoneNumberValidator(
    prefixControlName: string = 'prefix',
    digitsControlName: string = 'digits',
    allowEmpty: boolean = true
): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        if (control instanceof FormGroup) {
            const prefixControl = control.get(prefixControlName);
            const digitsControl = control.get(digitsControlName);

            if (!prefixControl || !digitsControl) {
                return null;
            }

            const prefix = prefixControl.value;
            const digits = digitsControl.value;

            // If both are empty and empty is allowed, it's valid
            if (allowEmpty && (!digits || digits === '')) {
                digitsControl.setErrors(null);
                return null;
            }

            // If digits is provided but prefix is missing
            if (digits && !prefix?.code) {
                digitsControl.setErrors({ error: PhoneNumberValidatorErrors.INVALID_PHONE_NUMBER });
                return { error: PhoneNumberValidatorErrors.INVALID_PHONE_NUMBER };
            }

            // If prefix is provided but digits is missing
            if (prefix?.code && (!digits || digits === '')) {
                digitsControl.setErrors({ error: PhoneNumberValidatorErrors.INVALID_PHONE_NUMBER });
                return { error: PhoneNumberValidatorErrors.INVALID_PHONE_NUMBER };
            }

            // Validate the complete phone number
            if (prefix?.code && digits) {
                const phoneNumber = parsePhoneNumberFromString('+' + String(prefix.code) + String(digits));

                if (!phoneNumber || !phoneNumber.isValid()) {
                    digitsControl.setErrors({ error: PhoneNumberValidatorErrors.INVALID_PHONE_NUMBER });
                    return { error: PhoneNumberValidatorErrors.INVALID_PHONE_NUMBER };
                }
            }

            // Clear any previous errors if validation passes
            digitsControl.setErrors(null);
            return null;
        }

        return null;
    };
}

/**
 * Utility function to validate a phone number with prefix and digits
 * Can be used outside of Angular forms
 *
 * @param prefix - Phone code (e.g., 33 for France)
 * @param digits - Phone digits without prefix
 * @returns boolean indicating if the phone number is valid
 */
export function isValidPhoneNumber(prefix: number | string, digits: string | number): boolean {
    if (!prefix || !digits) {
        return false;
    }

    const phoneNumber = parsePhoneNumberFromString('+' + String(prefix) + String(digits));
    return phoneNumber ? phoneNumber.isValid() : false;
}
