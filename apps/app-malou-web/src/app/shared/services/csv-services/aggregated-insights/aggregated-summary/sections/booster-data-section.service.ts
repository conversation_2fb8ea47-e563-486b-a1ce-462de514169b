import { Injectable } from '@angular/core';
import { catchError, forkJoin, map, Observable, of } from 'rxjs';

import { MalouComparisonPeriod, MalouPeriod } from '@malou-io/package-utils';

import { AggregatedBoostersStatisticsDataV2 } from ':modules/aggregated-statistics/boosters/booster.interface';
import { BoostersAggregatedDataFetchingServiceV2 } from ':modules/aggregated-statistics/boosters/services/get-boosters-aggregated-data-v2.service';
import { LightRestaurant } from ':shared/models';
import {
    AggregatedSummarySectionsData,
    AggregatedSummarySectionsDataFilters,
} from ':shared/services/csv-services/aggregated-insights/aggregated-summary/aggregated-summary.interface';

@Injectable({ providedIn: 'root' })
export class AggregatedSummaryCsvInsightsBoosterSectionService {
    constructor(private readonly _boostersAggregatedDataFetchingServiceV2: BoostersAggregatedDataFetchingServiceV2) {}

    execute(filters: AggregatedSummarySectionsDataFilters['booster']): Observable<{
        current: AggregatedSummarySectionsData['booster'] | null;
        previous: AggregatedSummarySectionsData['booster'] | null;
    }> {
        const { startDate, endDate, nfcs, restaurants, comparisonPeriodEndDate, comparisonPeriodStartDate } = filters;

        return forkJoin([
            this._boostersAggregatedDataFetchingServiceV2.getChartsData({
                nfcs,
                dates: {
                    startDate,
                    endDate,
                    period: MalouPeriod.CUSTOM,
                },
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                restaurantIds: restaurants.map((r) => r.id.toString()),
            }),
            this._boostersAggregatedDataFetchingServiceV2.getChartsData({
                nfcs,
                dates: {
                    startDate: comparisonPeriodStartDate,
                    endDate: comparisonPeriodEndDate,
                    period: MalouPeriod.CUSTOM,
                },
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                restaurantIds: restaurants.map((r) => r.id.toString()),
            }),
            // Gift stats
            this._boostersAggregatedDataFetchingServiceV2.getGiftsData({
                dates: {
                    startDate,
                    endDate,
                    period: MalouPeriod.CUSTOM,
                },
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                restaurantIds: restaurants.map((r) => r.id.toString()),
            }),
            this._boostersAggregatedDataFetchingServiceV2.getGiftsData({
                dates: {
                    startDate: comparisonPeriodStartDate,
                    endDate: comparisonPeriodEndDate,
                    period: MalouPeriod.CUSTOM,
                },
                comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
                restaurantIds: restaurants.map((r) => r.id.toString()),
            }),
        ]).pipe(
            map(([currentBoostersData, previousBoostersData, currentGiftData, previousGiftData]) => {
                const currentEstimatedReviewCount = this._getEstimatedReviewCountOnPeriod(currentBoostersData, restaurants);
                const previousEstimatedReviewCount = this._getEstimatedReviewCountOnPeriod(previousBoostersData, restaurants);

                const totalWinningGifts = currentGiftData.giftsInsightsPerRestaurant.reduce(
                    (acc, curr) => acc + (curr.giftDrawCount ?? 0),
                    0
                );
                const totalRetrievedGifts = currentGiftData.giftsInsightsPerRestaurant.reduce(
                    (acc, curr) => acc + (curr.retrievedGiftDrawCount ?? 0),
                    0
                );
                const previousTotalWinningGifts = previousGiftData.giftsInsightsPerRestaurant.reduce(
                    (acc, curr) => acc + (curr.giftDrawCount ?? 0),
                    0
                );
                const previousTotalRetrievedGifts = previousGiftData.giftsInsightsPerRestaurant.reduce(
                    (acc, curr) => acc + (curr.retrievedGiftDrawCount ?? 0),
                    0
                );

                return {
                    current: {
                        ...currentEstimatedReviewCount,
                        giftCount: totalRetrievedGifts,
                        winnerCount: totalWinningGifts,
                    },
                    previous: {
                        ...previousEstimatedReviewCount,
                        giftCount: previousTotalRetrievedGifts,
                        winnerCount: previousTotalWinningGifts,
                    },
                };
            }),
            catchError(() => of({ current: null, previous: null }))
        );
    }

    private _getEstimatedReviewCountOnPeriod(
        boostersStatisticsData: AggregatedBoostersStatisticsDataV2,
        restaurants: LightRestaurant[]
    ): Pick<
        NonNullable<AggregatedSummarySectionsData['booster']>,
        | 'totalTotemScanCount'
        | 'totalWofScanCount'
        | 'gainedPrivateReviewCount'
        | 'gainedPublicReviewCount'
        | 'top3TotemScanCount'
        | 'top3WofScanCount'
        | 'flop3TotemScanCount'
        | 'flop3WofScanCount'
    > {
        const {
            scans,

            wheelOfFortuneCount,

            totemReviewsPerRestaurant,
        } = boostersStatisticsData;

        if (scans.length === 0) {
            return {
                flop3TotemScanCount: [],
                flop3WofScanCount: [],
                top3TotemScanCount: [],
                top3WofScanCount: [],
                gainedPrivateReviewCount: null,
                gainedPublicReviewCount: null,
                totalTotemScanCount: 0,
                totalWofScanCount: 0,
            };
        }

        const totemScans = scans.filter((scan) => !scan.isWheelOfFortuneRelated());
        const wofScans = scans.filter((scan) => scan.isWheelOfFortuneRelated());

        const totemScanCountPerRestaurant = totemScans.reduce(
            (acc, scan) => {
                const restaurantId = scan.nfcSnapshot?.restaurantId;
                if (restaurantId) {
                    if (!acc[restaurantId]) {
                        acc[restaurantId] = 0;
                    }
                    acc[restaurantId] += 1;
                }
                return acc;
            },
            {} as Record<string, number>
        );

        const wofScanCountPerRestaurant = wofScans.reduce(
            (acc, scan) => {
                const restaurantId = scan.nfcSnapshot?.restaurantId;
                if (restaurantId) {
                    if (!acc[restaurantId]) {
                        acc[restaurantId] = 0;
                    }
                    acc[restaurantId] += 1;
                }
                return acc;
            },
            {} as Record<string, number>
        );

        const top3TotemScanCount = this._getTopOrFlopScans({ scans: totemScanCountPerRestaurant, restaurants, isTop: true });
        const flop3TotemScanCount = this._getTopOrFlopScans({ scans: totemScanCountPerRestaurant, restaurants, isTop: false });

        const top3WofScanCount = this._getTopOrFlopScans({ scans: wofScanCountPerRestaurant, restaurants, isTop: true });
        const flop3WofScanCount = this._getTopOrFlopScans({ scans: wofScanCountPerRestaurant, restaurants, isTop: false });

        const gainedPublicReviewCount = totemReviewsPerRestaurant.reviewsPerPlatform.reduce(
            (acc, curr) => acc + curr.reviewsCount.reduce((acc1, curr1) => acc1 + curr1.count, 0),
            0
        );
        return {
            top3TotemScanCount,
            flop3TotemScanCount,
            flop3WofScanCount,
            top3WofScanCount,
            gainedPrivateReviewCount: totemReviewsPerRestaurant.privateReviewsPerRating.length,
            gainedPublicReviewCount,
            totalTotemScanCount: totemScans.length,
            totalWofScanCount: wheelOfFortuneCount,
        };
    }

    private _getTopOrFlopScans({
        scans,
        restaurants,
        isTop = true,
    }: {
        scans: Record<string, number>;
        restaurants: LightRestaurant[];
        isTop: boolean;
    }): string[] {
        return Object.entries(scans)
            .sort(([, a], [, b]) => (isTop ? b - a : a - b))
            .slice(0, 3)
            .map(([restaurantId, count]) => {
                const restaurant = restaurants.find((r) => r.id.toString() === restaurantId);
                if (!restaurant) {
                    return `Unknown restaurant - ${count}`;
                }
                return restaurant.getDisplayName() + ' - ' + restaurant.getFullFormattedAddress() + ' - ' + count;
            });
    }
}
