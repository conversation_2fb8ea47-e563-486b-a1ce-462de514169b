import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { combineLatest, EMPTY, Observable, switchMap, take } from 'rxjs';

import { RestaurantCsvPostInsights } from '@malou-io/package-dto';
import { getPlatformKeysWithRSStats, PlatformFilterPage, PlatformKey, PostType } from '@malou-io/package-utils';

import { PostInsightsV2Service } from ':core/services/post-insights-v2.service';
import { AggregatedStatisticsFiltersContext } from ':modules/aggregated-statistics/filters/filters.context';
import * as AggregatedStatisticsSelectors from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { selectDatesFilter } from ':modules/aggregated-statistics/store/aggregated-statistics.selectors';
import { EnumTranslatePipe } from ':shared/pipes/enum-translate.pipe';
import { ShortNumberPipe } from ':shared/pipes/short-number.pipe';
import { AbstractCsvService } from ':shared/services/csv-services/csv-service.abstract';

type Data = RestaurantCsvPostInsights[];

@Injectable({ providedIn: 'root' })
export class AggregatedPublicationsCsvInsightsServiceV2 extends AbstractCsvService<Data> {
    constructor(
        private readonly _store: Store,
        private readonly _postInsightsServiceV2: PostInsightsV2Service,
        private readonly _enumTranslatePipe: EnumTranslatePipe,
        private readonly _shortNumberPipe: ShortNumberPipe,
        private readonly _aggregatedStatisticsFiltersContext: AggregatedStatisticsFiltersContext
    ) {
        super();
    }

    protected override _getData$(): Observable<Data> {
        return combineLatest([
            this._store.select(selectDatesFilter).pipe(take(1)),
            this._store
                .select(AggregatedStatisticsSelectors.selectPlatformsFilter({ page: PlatformFilterPage.SOCIAL_NETWORKS }))
                .pipe(take(1)),
            this._aggregatedStatisticsFiltersContext.selectedRestaurants$.pipe(take(1)),
        ]).pipe(
            switchMap(([dates, platformKeys, restaurants]) => {
                const { startDate, endDate } = dates;
                if (!startDate || !endDate) {
                    return EMPTY;
                }

                const restaurantIds = restaurants.map((restaurant) => restaurant._id);
                const socialPlatformKeys = getPlatformKeysWithRSStats().filter((platformKey) => platformKeys.includes(platformKey)) as (
                    | PlatformKey.FACEBOOK
                    | PlatformKey.INSTAGRAM
                    | PlatformKey.TIKTOK
                )[];

                return this._postInsightsServiceV2.getRestaurantsCsvPostInsights$({
                    restaurantIds,
                    platformKeys: socialPlatformKeys,
                    startDate: startDate.toISOString(),
                    endDate: endDate.toISOString(),
                });
            })
        );
    }

    protected override _isDataValid(data: Data): boolean {
        return Array.isArray(data) && data.length > 0;
    }

    protected override _getCsvHeaderRow(): string[] {
        return [
            'Date',
            'Location',
            'Location Internal Name',
            'Location Address',
            'Type',
            'Caption',
            'Platform',
            'Impressions',
            'Likes',
            'Comments',
            'Shares',
            'Saves',
            'Engagement Rate',
        ];
    }

    protected override _getCsvDataRows(insightsData: Data): string[][] {
        return insightsData.flatMap((restaurantCsvPostInsights) =>
            restaurantCsvPostInsights.postInsights.flatMap((postInsight) => {
                const restaurantName = restaurantCsvPostInsights.restaurantName;
                const restaurantInternalName = restaurantCsvPostInsights.restaurantInternalName ?? '';
                const restaurantAddress = restaurantCsvPostInsights.restaurantAddress ?? '';
                const postDate = new Date(postInsight.postCreatedAt).toLocaleDateString();
                const type = postInsight.postType === PostType.REEL ? 'Reel' : 'Post';
                const caption = postInsight.caption ?? '';
                const platform = this._enumTranslatePipe.transform(postInsight.platformKey, 'platform_key');
                const impressions = postInsight.impressions?.toString() ?? '0';
                const likes = postInsight.likes?.toString() ?? '0';
                const comments = postInsight.comments?.toString() ?? '0';
                const shares = postInsight.shares?.toString() ?? '0';
                const saves = postInsight.saved?.toString() ?? '0';
                const engagementRate =
                    typeof postInsight.engagementRate === 'number' ? this._shortNumberPipe.transform(postInsight.engagementRate) : '';

                return [
                    [
                        postDate,
                        restaurantName,
                        restaurantInternalName,
                        restaurantAddress,
                        type,
                        caption,
                        platform,
                        impressions,
                        likes,
                        comments,
                        shares,
                        saves,
                        engagementRate,
                    ],
                ];
            })
        );
    }
}
