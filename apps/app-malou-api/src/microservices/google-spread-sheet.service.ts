import { GoogleSpreadsheet } from 'google-spreadsheet';
import { singleton } from 'tsyringe';

@singleton()
export class GoogleSpreadsheetService {
    async getGoogleSpreadsheet(
        spreadSheetId: string,
        spreadSheetAuth: { client_email: string; private_key: string }
    ): Promise<GoogleSpreadsheet> {
        try {
            const doc = new GoogleSpreadsheet(spreadSheetId);
            await doc.useServiceAccountAuth(spreadSheetAuth);

            await doc.loadInfo();

            return doc;
        } catch (error: any) {
            throw new Error(`Error while loading google spreadsheet: ${error.message}`);
        }
    }

    async reloadGoogleSpreadsheet(doc: GoogleSpreadsheet, spreadSheetAuth: { client_email: string; private_key: string }): Promise<void> {
        try {
            await doc.loadInfo();
        } catch (error) {
            await doc.useServiceAccountAuth(spreadSheetAuth);

            await doc.loadInfo();
        }
    }
}
