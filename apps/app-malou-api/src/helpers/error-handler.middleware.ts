import { ForbiddenError } from '@casl/ability';
import { container } from 'tsyringe';

import { errorReplacer, MalouErrorCode } from '@malou-io/package-utils';

import { AsyncLocalStorageService } from './classes/async-local-storage-service';
import { MalouError } from './classes/malou-error';
import { logger } from './logger';

const asyncLocalStorageService = container.resolve(AsyncLocalStorageService);

export const errorHandlerMiddleware = function (err, req, res, next) {
    err.sentryId = res.sentry;

    const message = err instanceof MalouError ? err.message : '[ERROR_HANDLER]';
    logger.error(message, JSON.stringify(err, errorReplacer));

    if (err instanceof ForbiddenError) {
        res.status(403).json({
            message: err.message,
            casl: true,
        });
    }

    if (err?.fbtrace_id) {
        delete err.code; // facebook api returns error a "code" field in the body
    }

    // Todo: get interface MongooseError from mongoose
    if (err?.code === 11000) {
        res.status(409).json({
            message: 'Duplicate entry',
            error: err?.keyValue,
        });
    }

    const status = err instanceof MalouError ? getHttpStatusFromMalouError(err) : 500;
    const debugId = asyncLocalStorageService.getTraceId();

    res.status(status).json({
        ...err,
        ...(debugId && { debugId }),
        status: err.status,
        message: err.message,
        stack: err.stack,
        error: true,
    });
};

export const getHttpStatusFromMalouError = (error: MalouError): number => malouErrorCodeToHttpStatus[error.malouErrorCode] ?? 500;

/**
 * `null` means that the error should never reach HTTP middleware. However, for some codes,
 * it is expected that the error reaches HTTP middleware but the error is not assigned to an
 * HTTP status code (the value is `null` below). But this is a mistake. The idea is to
 * fix this progressively over time.
 */
const malouErrorCodeToHttpStatus: Record<MalouErrorCode, number | null> = {
    [MalouErrorCode.TOO_MANY_REQUESTS]: 429,
    [MalouErrorCode.DELIVEROO_CREDENTIALS_EXPIRED]: 502, // should be 401 but returning a 401 disconnects the user
    [MalouErrorCode.ABOVE_RESTAURANTS_LIMITS]: 403,
    [MalouErrorCode.ABSTRACT_CLASS_ERROR]: 501,
    [MalouErrorCode.AGENDA_DATABASE_CONNECTION_FAILED]: 500,
    [MalouErrorCode.AI_INVALID_RESPONSE_FORMAT]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.AI_REQUEST_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.ATTRIBUTES_GMB_NOT_CONNECTED]: 403,
    [MalouErrorCode.BAD_REQUEST]: 400,
    [MalouErrorCode.BODY_VALIDATION_ERROR]: 422,
    [MalouErrorCode.CAMPAIGN_DUPLICATE_RECORD_ERROR]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CAMPAIGN_MISSING_QUERY_PARAM]: 400,
    [MalouErrorCode.CAMPAIGN_NOT_FOUND]: 404,
    [MalouErrorCode.CANNOT_ADD_LOCATION_FOR_BRAND_RESTAURANT]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CANNOT_COMPUTE_TRANSFORM_DATA_FROM_OLD_MEDIA]: 400,
    [MalouErrorCode.CANNOT_DELETE_MEDIAS_FROM_OTHER_RESTAURANTS]: 403,
    [MalouErrorCode.CANNOT_GET_THUMBNAIL_FOR_EDITION_MEDIA]: 400,
    [MalouErrorCode.CANNOT_MAP_RESTAURANT_TO_YEXT_ENTITY]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CASL_UNKNOWN_ROLE]: 500,
    [MalouErrorCode.CASL_USER_NOT_DEFINED]: 500,
    [MalouErrorCode.CLIENT_ALREADY_PLAYED]: 400,
    [MalouErrorCode.CLIENT_CANNOT_PARSE_DATA]: 400,
    [MalouErrorCode.CLIENT_CANNOT_PARSE_FILE]: 400,
    [MalouErrorCode.CLIENT_EMAIL_NOT_FOUND]: 404,
    [MalouErrorCode.CLIENT_LANGUAGE_NOT_FOUND__REQUIRED_HEADERS]: 400,
    [MalouErrorCode.CLIENT_NOT_FOUND]: 404,
    [MalouErrorCode.COMMENT_INVALID_TYPE]: 400,
    [MalouErrorCode.COMMENT_NOT_FOUND]: 404,
    [MalouErrorCode.COMPLETE_PUBLISH_POST_ERROR]: 500,
    [MalouErrorCode.COMPLETE_PUBLISH_POST_TIMEOUT_ERROR]: 504,
    [MalouErrorCode.COMPLETE_PUBLISH_STORY_ERROR]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CREATE_COMPLETION_ERROR]: 502,
    [MalouErrorCode.CREATION_ERROR]: 500,
    [MalouErrorCode.CREDENTIALS_DELIVEROO_COULD_NOT_FETCH_API_WITH_SEVERAL_AGENTS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CREDENTIALS_DELIVEROO_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CREDENTIALS_FACEBOOK_CANNOT_FETCH_ACCOUNTS_AND_STORES]: 502,
    [MalouErrorCode.CREDENTIALS_FACEBOOK_ERROR]: 502,
    [MalouErrorCode.CREDENTIALS_FACEBOOK_MEDIA_NOT_SUPPORTED]: 403,
    [MalouErrorCode.CREDENTIALS_FACEBOOK_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CREDENTIALS_FACEBOOK_PLATFORM_NOT_SUPPORTED]: 400,
    [MalouErrorCode.CREDENTIALS_GMB_API_DESCRIPTION_ERROR]: 400,
    [MalouErrorCode.CREDENTIALS_GMB_API_DETAILS_ERROR]: 400,
    [MalouErrorCode.CREDENTIALS_GMB_API_ERROR]: 502,
    [MalouErrorCode.CREDENTIALS_GMB_CREDENTIAL_NOT_FOUND]: 404,
    [MalouErrorCode.CREDENTIALS_GMB_POST_SCHEDULE_END_DATE_IN_PAST]: 400,
    [MalouErrorCode.CREDENTIALS_GMB_REFRESH_TOKEN_EXPIRED]: 403,
    [MalouErrorCode.CREDENTIALS_INSTAGRAM_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.CREDENTIALS_TIKTOK_REFRESH_TOKEN_EXPIRED]: 403,
    [MalouErrorCode.CREDENTIALS_MISSING_PARAM]: 400,
    [MalouErrorCode.CREDENTIALS_PLATFORM_LOGIN_ERROR]: 502,
    [MalouErrorCode.CREDENTIALS_PUPPETEER_OPENTABLE_COOKIE_ERROR]: 500,
    [MalouErrorCode.CREDENTIALS_PUPPETEER_OPENTABLE_OTUMAMIAUTH_COOKIE_NOT_FOUND]: 500,
    [MalouErrorCode.CREDENTIALS_TIKTOK_NOT_FOUND]: 404,
    [MalouErrorCode.CREDENTIALS_TRIPADVISOR_COOKIE_NOT_FOUND]: 502,
    [MalouErrorCode.CREDENTIALS_TRIPADVISOR_UPDATE_COOKIE_FAILED]: 500,
    [MalouErrorCode.CREDENTIALS_UBEREATS_COOKIE_ERROR]: 500,
    [MalouErrorCode.CREDENTIALS_UBEREATS_COOKIE_NOT_FOUND]: 500,
    [MalouErrorCode.CREDENTIALS_UBEREATS_UPDATE_COOKIE_FAILED]: 500,
    [MalouErrorCode.CREDENTIALS_USECASE_NOT_FOUND]: 404,
    [MalouErrorCode.CREDENTIALS_ZENCHEF_API_ERROR]: 502,
    [MalouErrorCode.CREDENTIALS_ZENCHEF_AUTH_ERROR]: 502,
    [MalouErrorCode.CREDENTIALS_ZENCHEF_CREDENTIAL_NOT_FOUND]: 404,
    [MalouErrorCode.CREDENTIALS_ZENCHEF_ERROR]: 502,
    [MalouErrorCode.CREDENTIALS_ZENCHEF_NO_PARAMETERS_ACCESS]: 403,
    [MalouErrorCode.CREDENTIALS_ZENCHEF_WRONG_CREDENTIALS]: 400,
    [MalouErrorCode.DEFAULT_TEMPLATES_GENERATION_FAILED]: 500, // 502 ? Refacto to throw the correct error
    [MalouErrorCode.DIAGNOSTIC_CANNOT_FETCH_PLACE_DETAILS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.DIAGNOSTIC_INSTAGRAM_PAGE_NOT_FOUND]: 400,
    [MalouErrorCode.DIAGNOSTIC_INVALID_CATEGORY]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.DIAGNOSTIC_NO_KEYWORDS_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.DIAGNOSTIC_NOT_FOUND]: 404,
    [MalouErrorCode.DIAGNOSTIC_CANNOT_UPDATE_DIAGNOSTIC]: 500,
    [MalouErrorCode.DOWNLOAD_INSIGHTS_AS_PDF_FAILED]: 500,
    [MalouErrorCode.DOWNLOAD_REVIEWS_AS_PDF_FAILED]: 500,
    [MalouErrorCode.DRAW_GIFT_WENT_WRONG]: 500,
    [MalouErrorCode.EMAIL_INVALID_CATEGORY]: 400,
    [MalouErrorCode.EMAIL_NOT_SENT]: 502,
    [MalouErrorCode.ERROR_WHILE_CREATING_DIAGNOSTIC]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.FACEBOOK_API_EXCEPTION]: 502,
    [MalouErrorCode.FACEBOOK_REEL_BAD_UPLOAD_STATUS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.FACEBOOK_REEL_GET_POST_ID_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.FACEBOOK_REEL_PUBLISH_REEL_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.FACEBOOK_REEL_UPLOAD_VIDEO_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.FAILED_SEMANTIC_ANALYSIS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.FAILED_VIDEO_RESIZE]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PRIVATE_REVIEW_NOT_FOUND]: 404,
    [MalouErrorCode.FEEDBACK_ALREADY_EXISTS]: 400,
    [MalouErrorCode.FETCH_KEYWORDS_VOLUME_MONTHLY_ERROR]: 502,
    [MalouErrorCode.FETCH_KEYWORDS_VOLUME_MONTHLY_SUCCESS]: 200,
    [MalouErrorCode.FETCH_YELP_WEBSITE_FAILED]: 502,
    [MalouErrorCode.FETCH_SEVENROOMS_WEBSITE_FAILED]: 502,
    [MalouErrorCode.FILTER_CANNOT_INITIATE_CLASS]: 500,
    [MalouErrorCode.FILTER_INVALID_INSIGHT_PLATFORM]: 400,
    [MalouErrorCode.FILTER_METHOD_NOT_IMPLEMENTED]: 501,
    [MalouErrorCode.FILTER_MISSING_PARAMS]: 400,
    [MalouErrorCode.FILTER_START_DATE_AFTER_END_DATE]: 400,
    [MalouErrorCode.FILTER_TIME_RANGE_TOO_LONG]: 400,
    [MalouErrorCode.FILTER_TIME_RANGE_TOO_SHORT]: 400,
    [MalouErrorCode.FOLDER_WITH_SAME_NAME_ALREADY_EXISTS]: 400,
    [MalouErrorCode.FORBIDDEN]: 403,
    [MalouErrorCode.GIFT_DRAW_NOT_FOUND]: 404,
    [MalouErrorCode.GIFT_NAME_TOO_LONG]: 400,
    [MalouErrorCode.GMB_FAILED_TO_PUBLISH_POST]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.GMB_GEOLOCATION_SERVICE_ERROR]: 502,
    [MalouErrorCode.GMB_MESSAGES_AGENT_ALREADY_EXISTS]: 403,
    [MalouErrorCode.GMB_MISSING_ADDRESS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.GMB_MISSING_PARAMS]: 400,
    [MalouErrorCode.GMB_NOT_CONNECTED]: 403,
    [MalouErrorCode.GMB_PIN_DROP_REQUIRED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.GMB_STOREFRONT_REQUIRED_FOR_CATEGORY]: null,
    [MalouErrorCode.GMB_PLACE_ACTION_LINK_ALREADY_EXISTS_WITH_SAME_DOMAIN]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.GMB_PLACE_ID_IS_NO_LONGER_VALID]: 400,
    [MalouErrorCode.GMB_ACCOUNT_NOT_FOUND]: 404,
    [MalouErrorCode.HELPERS_DOWNLOAD_FILE_MISSING_FOLDER]: 400,
    [MalouErrorCode.HELPERS_INJECTOR_NOT_IMPLEMENTED]: 501,
    [MalouErrorCode.HELPERS_RETRY_PLATFORM_SCRAPPER_ERROR]: 502,
    [MalouErrorCode.HUBSPOT_CANNOT_SUBMIT_FORM]: 412,
    [MalouErrorCode.HYPERLINE_INTEGRATION_ERROR]: 502,
    [MalouErrorCode.INCONSISTENT_RESTAURANT_ID]: 403,
    [MalouErrorCode.INSIGHTS_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.INTERNAL_SERVER_ERROR]: 500,
    [MalouErrorCode.INVALID_API_KEY]: 401,
    [MalouErrorCode.INVALID_DATA]: 422,
    [MalouErrorCode.INVALID_SOCIAL_NETWORK_URL]: 400,
    [MalouErrorCode.INVALID_UNSUBSCRIBE_HASH]: 400,
    [MalouErrorCode.INVALID_URL]: 400,
    [MalouErrorCode.KEYWORD_LAMBDA_GENERATOR_ERROR]: 502,
    [MalouErrorCode.KEYWORD_NOT_FOUND]: 404,
    [MalouErrorCode.KEYWORDS_GENERATION_ALREADY_RUNNING]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.KEYWORDS_GEOSAMPLE_ERROR]: 502,
    [MalouErrorCode.KEYWORDS_PLATFORM_NOT_AUTHORIZED]: 403,
    [MalouErrorCode.KEYWORDS_VOLUME_API_REQUEST_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.KEYWORDS_VOLUME_API_REQUEST_LIMIT_REACHED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.LAMBDA_RESPONSE_ERROR]: 502,
    [MalouErrorCode.LAMBDA_ERROR_GENERATING_PDF]: 502,
    [MalouErrorCode.LAMBDA_MAXIMUM_CALL_STACK_SIZE_EXCEEDED]: 502,
    [MalouErrorCode.LAMBDA_FUNCTION_NAME_NOT_FOUND]: 404,
    [MalouErrorCode.MAILING_NO_USERS]: 404,
    [MalouErrorCode.MAPSTR_MAPPING_ERROR]: 400,
    [MalouErrorCode.MEDIA_DELETED_FOR_POST]: 400,
    [MalouErrorCode.MEDIA_NOT_READY_MAX_RETRIES_REACHED]: 500,
    [MalouErrorCode.MEDIA_REQUIRED_BECAUSE_THE_STORY_IS_PLANNED_FOR_PUBLICATION]: 400,
    [MalouErrorCode.MEDIA_THUMBNAIL_GENERATION_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.MESSAGES_CLIENT_AUTH_FAILED]: 502,
    [MalouErrorCode.MESSAGES_PLATFORM_HAS_NO_CONVERSATION_MAPPER]: 400,
    [MalouErrorCode.MISSING_AWS_SOURCE_KEY]: 400,
    [MalouErrorCode.MISSING_PARAM]: 400,
    [MalouErrorCode.MONTHLY_SAVE_ROI_INSIGHTS_ERROR]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.MULTIMEDIA_STREAMS_INFORMATION_MISSING_INFORMATION]: 422,
    [MalouErrorCode.NFC_NOT_FOUND]: 404,
    [MalouErrorCode.NO_AVAILABLE_GIFTS]: 404, // TODO : Prevent user from playing before this error
    [MalouErrorCode.NO_FILTER_PROVIDED]: 400,
    [MalouErrorCode.NO_MIN_REDIRECTED_AT]: 404,
    [MalouErrorCode.NO_SUBSCRIPTION_NAME_OR_ID]: 500,
    [MalouErrorCode.NOT_ENOUGH_CREDIT_TO_MAKE_AI_API_CALL]: 429,
    [MalouErrorCode.NOT_FOUND]: 404,
    [MalouErrorCode.NOT_IMPLEMENTED]: 501,
    [MalouErrorCode.NOTIFICATION_NOT_FOUND]: 404,
    [MalouErrorCode.OPEN_AI_REQUEST_FAILED]: 502,
    [MalouErrorCode.ORGANIZATION_IS_LINKED_TO_A_RESTAURANT]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.ORGANIZATION_LIMIT_REACHED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.ORGANIZATION_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PARAMS_VALIDATION_ERROR]: 422,
    [MalouErrorCode.PLATFORM_API_ENDPOINT_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PLATFORM_CATEGORY_NOT_FOUND]: 400,
    [MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND]: 404,
    [MalouErrorCode.PLATFORM_DATA_CRAWLING_ERROR]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PLATFORM_DATA_UNDEFINED_OR_EMPTY]: 400,
    [MalouErrorCode.PLATFORM_DELIVEROO_DRN_ID_NOT_FOUND]: 500,
    [MalouErrorCode.PLATFORM_DELETE_ERROR]: 500,
    [MalouErrorCode.PLATFORM_FB_NOT_CONNECTED]: 403, // TODO: 400 ?
    [MalouErrorCode.PLATFORM_FOURSQUARE_NO_VENUES]: 404,
    [MalouErrorCode.PLATFORM_IG_NOT_CONNECTED]: 403, // TODO: 400 ?
    [MalouErrorCode.PLATFORM_INSIGHTS_ERROR]: 502,
    [MalouErrorCode.PLATFORM_INVALID_ENDPOINT]: 400,
    [MalouErrorCode.PLATFORM_INVALID_KEY]: 500,
    [MalouErrorCode.PLATFORM_INVALID_PASSWORD]: 422,
    [MalouErrorCode.PLATFORM_MAPPER_CANNOT_PARSE_DATA]: 400,
    [MalouErrorCode.PLATFORM_MAPPER_DATA_ERROR]: 400,
    [MalouErrorCode.PLATFORM_MAPPER_FAILED_FIELD_PUBLISH]: 500,
    [MalouErrorCode.PLATFORM_MAPPER_METHOD_NOT_IMPLEMENTED]: 501,
    [MalouErrorCode.PLATFORM_MAPPER_MISSING_PARAM]: 400,
    [MalouErrorCode.PLATFORM_MISSING_LOCATION_ID]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PLATFORM_MISSING_PERMISSIONS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PLATFORM_MISSING_SOCIAL_ID]: 400,
    [MalouErrorCode.PLATFORM_NO_DATA_IN_RESPONSE]: 404,
    [MalouErrorCode.PLATFORM_NO_PROFILE_PICTURE_URL]: 404,
    [MalouErrorCode.PLATFORM_NOT_CONNECTED]: 403,
    [MalouErrorCode.PLATFORM_NOT_FOUND]: 404,
    [MalouErrorCode.PLATFORM_PUBLISH_ERROR]: 500,
    [MalouErrorCode.PLATFORM_PULL_OVERVIEW_ERROR]: 500,
    [MalouErrorCode.PLATFORM_REQUIRED]: 400,
    [MalouErrorCode.PLATFORM_SCRAP_ERROR]: 502,
    [MalouErrorCode.PLATFORM_SCRAPPER_ERROR]: 502,
    [MalouErrorCode.PLATFORM_SEARCH_RESULT_NOT_ARRAY]: 400,
    [MalouErrorCode.PLATFORM_SERVICE_ERROR]: 500,
    [MalouErrorCode.PLATFORM_SERVICE_NOT_IMPLEMENTED]: 501,
    [MalouErrorCode.PLATFORM_THE_FORK_REVIEWS_MALFORMED_RESPONSE]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PLATFORM_UBEREATS_REVIEWS_FETCH_ERROR]: 404,
    [MalouErrorCode.PLATFORM_UPSERT_ERROR]: 500,
    [MalouErrorCode.PLATFORM_ZENCHEF_FETCH_RESTAURANTS_UNAUTHORIZED_ERROR]: 400,
    [MalouErrorCode.POST_INSIGHTS_NOT_FOUND]: 404,
    [MalouErrorCode.POST_INSIGHTS_UNEXPECTED_ERROR]: 500,
    [MalouErrorCode.POST_IS_NOT_SOCIAL_POST]: 400,
    [MalouErrorCode.POST_IS_PUBLISHING]: 400,
    [MalouErrorCode.POST_MUST_HAVE_MEDIA]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.POST_NOT_FOUND]: 404,
    [MalouErrorCode.POSTS_MUST_BE_FROM_SAME_RESTAURANT]: 400,
    [MalouErrorCode.POST_CREATION_FOR_PLATFORM_FAILED]: 500,
    [MalouErrorCode.MISSING_DATA_ON_MEDIA_FOR_TRANSFORM]: 500,
    [MalouErrorCode.PROVIDER_INVALID_PLATFORM_KEY]: 400,
    [MalouErrorCode.PROVIDER_MAPSTR_NO_DATA]: 502,
    [MalouErrorCode.PUBLISH_POST_ERROR]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.PUBLISH_REVIEW_ERROR]: 500,
    [MalouErrorCode.QUERY_VALIDATION_ERROR]: 422,
    [MalouErrorCode.REEL_WITHOUT_VIDEO]: 400,
    [MalouErrorCode.RESTAURANT_AI_SETTINGS_NOT_FOUND]: 404,
    [MalouErrorCode.RESTAURANT_API_LOCATION_ID_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.RESTAURANT_BRICKS_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.RESTAURANT_CLOSED_PERMANENTLY]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.RESTAURANT_HAS_AN_ACTIVE_OR_PROGRAMMED_WHEEL_OF_FORTUNE]: 400,
    [MalouErrorCode.RESTAURANT_HAS_NO_ORGANIZATION]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.RESTAURANT_INACTIVE]: 403,
    [MalouErrorCode.RESTAURANT_KEYWORD_NOT_FOUND]: 404,
    [MalouErrorCode.RESTAURANT_KEYWORD_RANKING_REFRESH_NOT_ALLOWED]: 403,
    [MalouErrorCode.RESTAURANT_MISSING_LATLNG]: 404,
    [MalouErrorCode.RESTAURANT_NOT_FOUND]: 404,
    [MalouErrorCode.RESTAURANT_NOT_MANAGED_MISSING_IN_WHEEL_OF_FORTUNE]: 403,
    [MalouErrorCode.RESTAURANT_WHEEL_OF_FORTUNE_MULTIPLE_RESTAURANTS]: 400,
    [MalouErrorCode.RETRIEVE_OVERVIEW_DATA_ERROR]: 502,
    [MalouErrorCode.RETRIEVE_REVIEWS_ERROR]: 500,
    [MalouErrorCode.REVIEW_INCORRECT_SOCIAL_ID]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.REVIEW_INCORRECT_SOCIAL_LINK]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.REVIEW_NOT_FOUND]: 404,
    [MalouErrorCode.REVIEW_NOT_IN_RESULTS]: 404,
    [MalouErrorCode.REVIEW_TOO_OLD]: 400,
    [MalouErrorCode.REVIEWS_FROM_DIFFERENT_RESTAURANTS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.REVIEWS_UPDATE_ERROR]: 500, // TODO : refactor to throw the correct error ?
    [MalouErrorCode.ROI_ACTIVATION_MESSAGE]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.ROUTING_ERROR]: 400,
    [MalouErrorCode.SCRAPPER_TOO_MANY_TRIES]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.SERVICE_INVALID_CONFIGURATION]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.SERVICE_INVALID_RESPONSE]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.SEVENROOMS_VENUE_ID_NOT_FOUND]: 404,
    [MalouErrorCode.SEVENROOMS_USER_NO_MARKETING_ACCESS]: 502,
    [MalouErrorCode.SQS_INVALID_MESSAGE]: 400,
    [MalouErrorCode.SQS_MESSAGE_NOT_FOUND]: 404,
    [MalouErrorCode.STICKER_NOT_FOUND]: 404,
    [MalouErrorCode.STORY_ALREADY_PUBLISHED]: 400,
    [MalouErrorCode.PUBLISH_STORY_ERROR]: 404,
    [MalouErrorCode.SEGMENT_ANALYSIS_PARENT_TOPIC_NOT_FOUND]: 404,
    [MalouErrorCode.SEGMENT_ANALYSIS_PARENT_TOPIC_NOT_USER_INPUT]: 403,
    [MalouErrorCode.SEGMENT_ANALYSIS_TOPIC_PRUNING_ERROR]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.TEXT_TYPE_NOT_SUPPORTED]: 400,
    [MalouErrorCode.THEFORK_CLIENT_NOT_FOUND]: 404,
    [MalouErrorCode.THEFORK_VISIT_NOT_FOUND]: 404,
    [MalouErrorCode.UNAUTHORIZED]: 401,
    [MalouErrorCode.UNHANDLED_YEXT_PUBLISHERS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.UNKNOWN_FB_POST_TYPE]: 422,
    [MalouErrorCode.UPDATE_YEXT_ADD_REQUEST_STATUS_MAX_RETRY]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.USER_CANNOT_CREATE_ADMIN]: 403,
    [MalouErrorCode.USER_CANNOT_DELETE_ADMIN]: 403,
    [MalouErrorCode.USER_CANNOT_DELETE_USER]: 403,
    [MalouErrorCode.USER_CANNOT_DOWNGRADE_LAST_OWNER]: 403,
    [MalouErrorCode.USER_CANNOT_SEE_RESTAURANTS]: 403,
    [MalouErrorCode.USER_CANNOT_UPDATE_USER_RESTAURANT]: 403,
    [MalouErrorCode.USER_HAS_NO_ORGANIZATION]: 404,
    [MalouErrorCode.USER_HAVE_NO_ACCESS_TO_ACCOUNT]: 403,
    [MalouErrorCode.USER_HAS_LINKED_ENTITIES]: 400,
    [MalouErrorCode.USER_NOT_FOUND]: 404,
    [MalouErrorCode.USER_NOT_VERIFIED]: 403,
    [MalouErrorCode.USER_UNKNOWN_ROLE]: 403,
    [MalouErrorCode.USER_WRONG_PASSWORD]: 404,
    [MalouErrorCode.TIKTOK_OAUTH_SCOPE_NOT_ACCEPTED]: 400,
    [MalouErrorCode.WHEEL_OF_FORTUNE_NOT_FOUND]: 404,
    [MalouErrorCode.WHEEL_OF_FORTUNE_NOT_STARTED]: 400,
    [MalouErrorCode.WRONG_MEDIA_FORMAT]: 400,
    [MalouErrorCode.WRONG_MEDIA_TYPE]: 400,
    [MalouErrorCode.WRONG_RETRIEVAL_DATES]: 400,
    [MalouErrorCode.STORE_LOCATOR_ADDRESS_NOT_FOUND]: 400,
    [MalouErrorCode.STORE_LOCATOR_DATA_FETCH_FAILED]: 400,
    [MalouErrorCode.STORE_LOCATOR_DATA_BACKUP_USED]: 400,
    [MalouErrorCode.STORE_LOCATOR_INVALID_JOB_NAME]: 400,
    [MalouErrorCode.UBEREATS_STORE_NOT_FOUND]: 404,
    [MalouErrorCode.YEXT_ACCOUNT_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_CANT_DELETE_BECAUSE_OF_ADD_REQUEST_STATUS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_DELETE_LOCATION_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_LOCATION_ALREADY_EXISTS]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_LOCATION_DELETION_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_LOCATION_HAS_ACTIVE_SERVICES]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_LOCATION_NOT_FOUND]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_LOCATION_STATUS_IS_NOT_PROCESSED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_NOT_SUPPORTED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.YEXT_UPDATE_LOCATION_FAILED]: null, // FIXME: it is possible that this error should be assigned to a specific HTTP status code
    [MalouErrorCode.INSIGHTS_MAPPER_UNSUPPORTED_METRIC]: 400,
    [MalouErrorCode.FAILED_TO_PROCESS_KEYWORD_SEARCH_IMPRESSIONS]: 500,
    [MalouErrorCode.INVALID_DATE_RANGE]: 400,
    [MalouErrorCode.INVALID_COMPARISON_PERIOD]: 400,
    [MalouErrorCode.CREDENTIALS_DOORDASH_COOKIE_NOT_FOUND]: 404,
    [MalouErrorCode.DOORDASH_STORE_NOT_FOUND]: 404,
    [MalouErrorCode.DOORDASH_REVIEWS_FETCH_ERROR]: 500,
    [MalouErrorCode.DOORDASH_HOURS_FETCH_ERROR]: 500,
    [MalouErrorCode.DOORDASH_STORE_DETAILS_FETCH_ERROR]: 500,
    [MalouErrorCode.STORE_LOCATOR_ORGANIZATION_CONFIG_NOT_FOUND]: 404,
    [MalouErrorCode.STORE_LOCATOR_GENERATION_ALREADY_RUNNING]: 409,
};
