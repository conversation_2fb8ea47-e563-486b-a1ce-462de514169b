import { escapeRegExp } from 'lodash';

import { ID, toDbId } from '@malou-io/package-models';
import { FilterType, MediaCategory, MediaType } from '@malou-io/package-utils';

import { toDiacriticInsensitiveRegexString } from '../utils';
import { BasicFilters } from './basic-filters';

/** Never returns media marked as deleted from the gallery. */
export class MediaFilters extends BasicFilters {
    type: string | null;

    category: MediaCategory;

    isNeverUsed: boolean = false;

    title: string;

    folderIds?: (ID | null)[];

    maxVideoSize?: number;

    constructor(data) {
        super(data);
        this.type = data.mediaType !== 'all' ? data.mediaType : null;
        this.category = MediaCategory.ADDITIONAL;
        this.isNeverUsed = data.isNeverUsed;
        this.title = data.title;
        this.folderIds = data.folderIds;
        this.maxVideoSize = data.maxVideoSize;
    }

    buildQuery() {
        const query = super.buildQuery({ filterType: FilterType.MEDIA });
        query.$and.push({ category: this.category, originalMediaId: { $eq: null }, deletedAt: null });
        if (this.isNeverUsed) {
            query.$and.push({
                postIds: [],
            });
        }
        if (this.type) {
            query.$and.push({ type: this.type });
        }

        if (this.title) {
            const escapedTitle = toDiacriticInsensitiveRegexString(escapeRegExp(this.title));
            query.$and.push({
                $or: [
                    {
                        title: {
                            $regex: escapedTitle,
                            $options: 'i',
                            $exists: true,
                            $ne: null,
                        },
                    },
                    {
                        description: {
                            $regex: escapedTitle,
                            $options: 'i',
                            $exists: true,
                            $ne: null,
                        },
                    },
                    {
                        name: {
                            $regex: escapedTitle,
                            $options: 'i',
                            $exists: true,
                            $ne: null,
                        },
                    },
                ],
            });
        }
        if (this.folderIds?.length) {
            const folderDbIds = this.folderIds.map((folderId) => (folderId ? toDbId(folderId) : null));
            query.$and.push({
                folderId: { $in: folderDbIds },
            });
        }
        if (this.maxVideoSize) {
            query.$and.push({
                $or: [
                    {
                        type: { $ne: MediaType.VIDEO },
                    },
                    {
                        'sizes.original': { $lt: this.maxVideoSize },
                    },
                ],
            });
        }

        return query;
    }
}
