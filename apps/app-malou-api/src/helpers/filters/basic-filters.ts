import { escapeRegExp } from 'lodash';
import { DateTime } from 'luxon';

import { ID, toDbId, toDbIds } from '@malou-io/package-models';
import {
    createDate,
    FilterType,
    getDateRangeFromMalouComparisonPeriod,
    MalouComparisonPeriod,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';

import { toDiacriticInsensitiveRegexString } from '../utils';

type ComparisonPeriodData = {
    comparisonPeriod: MalouComparisonPeriod;
    restaurantStartDate?: Date;
};
export interface IBasicFilters {
    restaurantId?: ID | ID[];
    restaurantIds?: string[];
    platforms?: any[];
    text?: string;
    sortBy?: any;
    sortOrder?: any;
    startDate?: Date | string;
    endDate?: Date | string;
    previousPeriod?: any;
    comparisonPeriodData?: ComparisonPeriodData;
    dateFieldNameInSchema?: string;
    secondDateFieldNameInSchema?: any;
}

export class BasicFilters implements IBasicFilters {
    // TODO cooldown dev: restaurantIds?: ID[] (attention, utilisé dans 57 fichiers !)
    restaurantId?: string;
    restaurantIds?: string[];

    platforms?: any[];

    text?: string;

    sortBy: any;

    sortOrder: any;

    startDate?: Date;

    endDate?: Date;

    previousPeriod: any;

    comparisonPeriodData?: ComparisonPeriodData;

    dateFieldNameInSchema: string;

    secondDateFieldNameInSchema: any;

    constructor(data: IBasicFilters) {
        this.restaurantId = data.restaurantId && !Array.isArray(data.restaurantId) ? data.restaurantId.toString() : undefined;
        this.restaurantIds =
            data.restaurantIds ??
            (data.restaurantId && Array.isArray(data.restaurantId)
                ? data.restaurantId.map((restaurantId: ID) => restaurantId.toString())
                : undefined);
        if (data.platforms) {
            this.platforms = [data.platforms].flat().filter((v) => !!v) ?? [];
        }
        this.text = data.text ?? undefined;
        this.sortBy = data.sortBy ?? null;
        this.sortOrder = parseInt(data.sortOrder, 10) || -1;

        this.startDate = data.startDate ? (createDate(data.startDate) ?? undefined) : undefined;
        this.endDate = data.endDate ? (createDate(data.endDate) ?? undefined) : undefined;

        this.checkStartDateBeforeEndDate();

        this.previousPeriod = typeof data.previousPeriod === 'boolean' ? data.previousPeriod : data.previousPeriod === 'true';
        this.comparisonPeriodData = data.comparisonPeriodData;

        this._setComparisonPeriod({ startDate: this.startDate, endDate: this.endDate });

        this.dateFieldNameInSchema = data.dateFieldNameInSchema ?? 'socialCreatedAt';
        this.secondDateFieldNameInSchema = data.secondDateFieldNameInSchema;
    }

    isAllPlatforms(platforms) {
        return platforms?.[0] === 'all';
    }

    /**
     * Build a generic mongo search query given usual filter values.
     * You can pass in a pre-existing query which will be completed with filters.
     */
    buildQuery({
        filterType,
        includeWithoutPlatformsDrafts,
        source,
    }: {
        filterType?: FilterType | string;
        includeWithoutPlatformsDrafts?: boolean;
        source?: any;
    } = {}): { $and: any[] } {
        const query: { $and: any[] } = { $and: [] };
        if (this.restaurantId) {
            query.$and.push({
                restaurantId: toDbId(this.restaurantId),
            });
        }
        if (this.restaurantIds) {
            query.$and.push({
                restaurantId: {
                    $in: toDbIds(this.restaurantIds),
                },
            });
        }
        if (this.startDate || this.endDate) {
            const queryDate = this._buildQueryDate();
            switch (filterType) {
                case FilterType.REVIEWS:
                    query.$and.push({
                        socialSortDate: queryDate,
                    });
                    break;
                case FilterType.COMMENTS:
                    query.$and.push({
                        $or: [
                            { socialCreatedAt: queryDate },
                            {
                                $and: [{ socialCreatedAt: null }, { socialCreatedAt: queryDate }],
                            },
                        ],
                    });
                    break;
                case FilterType.POSTS:
                    // we make custom date search, see this posts repository
                    break;
                default:
                    const orQuery = [{ [this.dateFieldNameInSchema]: queryDate }];
                    if (this.secondDateFieldNameInSchema) {
                        orQuery.push({ [this.secondDateFieldNameInSchema]: queryDate });
                    }
                    query.$and.push({ $or: orQuery });
            }
        }
        if (this.platforms && Array.isArray(this.platforms)) {
            if (!this.isAllPlatforms(this.platforms)) {
                switch (filterType) {
                    case FilterType.COMMENTS:
                    case FilterType.MENTIONS:
                        query.$and.push({
                            platformKey: {
                                $in: this.platforms,
                            },
                        });
                        break;
                    case FilterType.POSTS:
                        const q: { $or: any[] } = {
                            $or: [{ key: { $in: this.platforms } }, { keys: { $in: this.platforms } }],
                        };
                        if (includeWithoutPlatformsDrafts) {
                            q.$or.push({
                                source,
                                $or: [{ keys: null }, { keys: [] }],
                                key: null,
                            });
                        }
                        query.$and.push(q);
                        break;
                    default:
                        query.$and.push({
                            key: {
                                $in: this.platforms,
                            },
                        });
                        break;
                }
            }
        }
        if (this.text) {
            query.$and.push({
                text: { $regex: toDiacriticInsensitiveRegexString(escapeRegExp(this.text)), $options: 'i', $exists: true, $ne: null },
            });
        }
        return query;
    }

    _buildQueryDate(): any {
        return {
            $ne: null,
            ...(this.startDate && { $gte: this.startDate }),
            ...(this.endDate && { $lte: this.endDate }),
        };
    }

    validDates() {
        if (this.startDate && this.endDate) {
            return true;
        }
        if (!this.startDate && !this.endDate) {
            return true;
        }
        return false;
    }

    periodCantBeSet() {
        // if no dates there is no previous period
        return !this.startDate && !this.endDate && this.previousPeriod;
    }

    checkStartDateBeforeEndDate() {
        if (this.startDate && this.endDate) {
            const dtStart = DateTime.fromJSDate(this.startDate);
            const dtEnd = DateTime.fromJSDate(this.endDate);

            if (dtStart > dtEnd) {
                throw new MalouError(MalouErrorCode.FILTER_START_DATE_AFTER_END_DATE);
            }
        }
    }

    private _setComparisonPeriod({ startDate, endDate }: { startDate?: Date; endDate?: Date }): void {
        if (!startDate || !endDate) {
            return;
        }
        if (this.comparisonPeriodData) {
            const { comparisonPeriod, restaurantStartDate } = this.comparisonPeriodData;
            const { startDate: comparisonStartDate, endDate: comparisonEndDate } = getDateRangeFromMalouComparisonPeriod({
                comparisonPeriod,
                dateFilters: {
                    startDate,
                    endDate,
                },
                restaurantStartDate,
            });
            if (comparisonStartDate && comparisonEndDate) {
                this.startDate = comparisonStartDate;
                this.endDate = comparisonEndDate;
            } else {
                this._setPreviousPeriod({ startDate, endDate });
            }
            return;
        }
        if (this.previousPeriod) {
            this._setPreviousPeriod({ startDate, endDate });
        }
    }

    private _setPreviousPeriod({ startDate, endDate }: { startDate: Date; endDate: Date }): void {
        const startDateLuxon = DateTime.fromJSDate(startDate);
        const endDateLuxon = DateTime.fromJSDate(endDate);
        const duration = endDateLuxon.diff(startDateLuxon);
        const newStartDateLuxon = startDateLuxon.minus(duration);
        const newEndDateLuxon = startDateLuxon;
        this.startDate = newStartDateLuxon.toJSDate();
        this.endDate = newEndDateLuxon.toJSDate();
    }
}
