import { escapeRegExp } from 'lodash';

import { toDiacriticInsensitiveRegexString } from ':helpers/utils';

interface IFoldersFilters {
    restaurantId: string;
    parentFolderId: string | null;
    name?: string;
}

export class FoldersFilters implements IFoldersFilters {
    restaurantId: string;
    parentFolderId: string | null;
    name?: string;

    constructor(data: IFoldersFilters) {
        this.restaurantId = data.restaurantId;
        this.parentFolderId = data.parentFolderId;
        this.name = data.name;
    }

    buildQuery() {
        const query: { $and: any[] } = { $and: [{ restaurantId: this.restaurantId, parentFolderId: this.parentFolderId }] };

        if (this.name) {
            query.$and.push({ name: { $regex: toDiacriticInsensitiveRegexString(escapeRegExp(this.name)), $options: 'i' } });
        }

        return query;
    }
}
