import { DateTime } from 'luxon';
import { injectable } from 'tsyringe';

import { CacheRepository } from ':modules/cache/cache.repository';
import { Cache } from ':plugins/cache';

type CacheKey = string;
type CacheValue = any;

interface CachedData {
    data: CacheValue;
}
@injectable()
export class <PERSON>go<PERSON>ache implements Cache {
    constructor(private readonly _cacheRepository: CacheRepository) {}

    async set(key: CacheK<PERSON>, value: any, ttlInSeconds: number): Promise<void> {
        const valueToCache = this._computeValueToCache(value);
        await this._cacheRepository.atomicUpsert({
            filter: { key },
            update: {
                key,
                value: valueToCache,
                expiresAt: DateTime.now().plus({ seconds: ttlInSeconds }).toJSDate(),
            },
        });
    }

    async get(key: CacheKey): Promise<CacheValue | null> {
        const cachedData = await this._cacheRepository.findOne({
            filter: { key, expiresAt: { $gt: new Date() } },
            options: { lean: true },
        });
        if (!cachedData) {
            return null;
        }
        return this._parseCachedValue(cachedData.value);
    }

    /**
     * Delete one or more keys from the cache.
     *
     * @return the number of keys deleted
     */
    async delete(keys: CacheKey | CacheKey[]): Promise<number> {
        const deleted = await this._cacheRepository.deleteMany({ filter: { key: { $in: Array.isArray(keys) ? keys : [keys] } } });
        return deleted.deletedCount;
    }

    status(): string {
        return 'ready';
    }

    async getKeys(_pattern: string): Promise<string[]> {
        // Not implemented in MongoDB
        return [];
    }

    /**
     * @return the time to live in seconds
     */
    async getTtl(key: CacheKey): Promise<number> {
        const cachedData = await this._cacheRepository.findOne({ filter: { key }, options: { lean: true } });
        if (!cachedData) {
            return -1; // Key not found
        }
        const expiresAt = new Date(cachedData.expiresAt);
        return Math.floor(DateTime.fromJSDate(expiresAt).diffNow().seconds);
    }

    /**
     * Compute a value to cache.
     *
     * We set the value in an object which is 'stringify' to keep his type when we parse it later
     */
    private _computeValueToCache(value: CacheValue): string {
        const dataToCache: CachedData = {
            data: value,
        };
        return JSON.stringify(dataToCache);
    }

    private _parseCachedValue(cacheValue: string): CacheValue {
        const cachedData: CachedData = JSON.parse(cacheValue);
        return cachedData.data;
    }
}
