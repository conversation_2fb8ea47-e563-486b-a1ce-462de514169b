// This file was auto-generated by 'typesafe-i18n'. Any manual changes will be overwritten.
/* eslint-disable */
import type { BaseTranslation as BaseTranslationType, LocalizedString, RequiredParams } from 'typesafe-i18n'

export type BaseTranslation = BaseTranslationType
export type BaseLocale = 'fr'

export type Locales =
	| 'en'
	| 'es'
	| 'fr'
	| 'it'

export type Translation = RootTranslation

export type Translations = RootTranslation

type RootTranslation = {
	apple_business_connect: {
		special_hours: {
			/**
			 * F​e​r​m​e​t​u​r​e​ ​e​x​c​e​p​t​i​o​n​n​e​l​l​e
			 */
			closed: string
			/**
			 * O​u​v​e​r​t​u​r​e​ ​e​x​c​e​p​t​i​o​n​n​e​l​l​e
			 */
			open: string
		}
	}
	automations: {
		intelligent_subjects: {
			reviews: {
				/**
				 * U​r​g​e​n​t​:​ ​S​u​j​e​t​ ​s​e​n​s​i​b​l​e​ ​d​é​t​e​c​t​é​ ​d​a​n​s​ ​u​n​ ​a​v​i​s
				 */
				subject: string
			}
		}
	}
	common: {
		/**
		 * V​o​s​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​s
		 */
		your_locations: string
		/**
		 * L​'​é​q​u​i​p​e​ ​<PERSON>​ ​{​e​n​v​}
		 * @param {string} env
		 */
		malouTeam: RequiredParams<'env'>
	}
	notifications: {
		/**
		 * {​r​a​t​i​n​g​}​⭐​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
		 * @param {string} rating
		 * @param {string} restaurantName
		 */
		new_review: RequiredParams<'rating' | 'restaurantName'>
		/**
		 * V​o​u​s​ ​a​ ​m​e​n​t​i​o​n​n​é​ ​d​a​n​s​ ​u​n​e​ ​s​t​o​r​y
		 */
		mentioned_in_story: string
		/**
		 * V​o​u​s​ ​a​ ​e​n​v​o​y​é​ ​u​n​ ​f​i​c​h​i​e​r
		 */
		sent_attachment: string
		/**
		 * V​o​u​s​ ​a​ ​e​n​v​o​y​é​ ​u​n​e​ ​p​h​o​t​o
		 */
		sent_picture: string
		/**
		 * V​o​u​s​ ​a​ ​e​n​v​o​y​é​ ​u​n​e​ ​v​i​d​é​o
		 */
		sent_video: string
		/**
		 * P​s​s​t​ ​{​u​n​r​e​a​d​M​e​s​s​a​g​e​s​C​o​u​n​t​}​ ​{​{​n​o​u​v​e​a​u​ ​m​e​s​s​a​g​e​|​n​o​u​v​e​a​u​x​ ​m​e​s​s​a​g​e​s​}​}
		 * @param {number} unreadMessagesCount
		 */
		daily_unread_messages: RequiredParams<'unreadMessagesCount'>
		/**
		 * V​o​u​s​ ​a​v​e​z​ ​{​u​n​a​n​s​w​e​r​e​d​R​e​v​i​e​w​s​C​o​u​n​t​}​ ​a​v​i​s​ ​n​é​g​a​t​i​f​s​ ​s​a​n​s​ ​r​é​p​o​n​s​e
		 * @param {number} unansweredReviewsCount
		 */
		daily_unanswered_reviews: RequiredParams<'unansweredReviewsCount'>
		/**
		 * ✍​️​ ​N​'​o​u​b​l​i​e​z​ ​p​a​s​ ​d​e​ ​p​o​s​t​e​r
		 */
		daily_no_more_scheduled_posts: string
		/**
		 * N​'​o​u​b​l​i​e​z​ ​p​a​s​ ​d​'​y​ ​r​é​p​o​n​d​r​e​ ​d​e​p​u​i​s​ ​l​a​ ​M​a​l​o​u​a​p​p​.
		 */
		daily_unread_messages_text: string
		/**
		 * N​e​ ​l​a​i​s​s​e​z​ ​p​a​s​ ​v​o​s​ ​c​l​i​e​n​t​s​ ​s​a​n​s​ ​r​é​p​o​n​s​e​,​ ​r​e​n​d​e​z​-​v​o​u​s​ ​s​u​r​ ​l​a​ ​M​a​l​o​u​a​p​p​.
		 */
		daily_unanswered_reviews_text: string
		/**
		 * V​o​u​s​ ​n​'​a​v​e​z​ ​p​l​u​s​ ​d​e​ ​p​o​s​t​s​ ​p​r​o​g​r​a​m​m​é​s​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
		 * @param {string} restaurantName
		 */
		daily_no_more_scheduled_posts_text: RequiredParams<'restaurantName'>
		/**
		 * E​r​r​e​u​r​ ​d​e​ ​p​u​b​l​i​c​a​t​i​o​n
		 */
		error_publishing_post: string
		/**
		 * V​o​t​r​e​ ​p​o​s​t​ ​n​'​a​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​p​u​b​l​i​é​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​.
		 * @param {string} restaurantName
		 */
		error_publishing_post_text: RequiredParams<'restaurantName'>
		reviews: {
			negative_review_reply_reminder: {
				/**
				 * {​r​e​c​e​i​v​e​r​N​a​m​e​}​,​ ​v​o​u​s​ ​a​v​e​z​ ​{​u​n​a​n​s​w​e​r​e​d​R​e​v​i​e​w​s​C​o​u​n​t​}​ ​a​v​i​s​ ​n​é​g​a​t​i​f​s​ ​e​n​ ​a​t​t​e​n​t​e​ ​d​e​ ​r​é​p​o​n​s​e
				 * @param {string} receiverName
				 * @param {number} unansweredReviewsCount
				 */
				subject: RequiredParams<'receiverName' | 'unansweredReviewsCount'>
				/**
				 * {​r​e​c​e​i​v​e​r​N​a​m​e​}​,​ ​n​’​o​u​b​l​i​e​z​ ​p​a​s​ ​d​e​ ​r​é​p​o​n​d​r​e​ ​à​ ​c​e​t​ ​a​v​i​s​ ​n​é​g​a​t​i​f
				 * @param {string} receiverName
				 */
				subject_single: RequiredParams<'receiverName'>
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​{​u​n​a​n​s​w​e​r​e​d​R​e​v​i​e​w​s​C​o​u​n​t​}​ ​a​v​i​s​ ​n​é​g​a​t​i​f​s​ ​e​n​ ​a​t​t​e​n​t​e​ ​d​e​ ​r​é​p​o​n​s​e
				 * @param {number} unansweredReviewsCount
				 */
				subject_without_name: RequiredParams<'unansweredReviewsCount'>
				/**
				 * N​’​o​u​b​l​i​e​z​ ​p​a​s​ ​d​e​ ​r​é​p​o​n​d​r​e​ ​à​ ​c​e​t​ ​a​v​i​s​ ​n​é​g​a​t​i​f
				 */
				subject_single_without_name: string
			}
		}
		special_hour: {
			/**
			 * {​r​e​c​e​i​v​e​r​N​a​m​e​}​,​ ​i​n​d​i​q​u​e​z​ ​à​ ​v​o​s​ ​c​l​i​e​n​t​s​ ​s​i​ ​v​o​u​s​ ​ê​t​e​s​ ​o​u​v​e​r​t​ ​l​e​ ​{​d​a​t​e​}
			 * @param {string} date
			 * @param {string} receiverName
			 */
			subject: RequiredParams<'date' | 'receiverName'>
		}
		post_suggestion: {
			/**
			 * {​e​v​e​n​t​N​a​m​e​}​U​n​ ​é​v​è​n​e​m​e​n​t​ ​a​p​p​r​o​c​h​e​,​ ​n​'​o​u​b​l​i​e​z​ ​p​a​s​ ​d​'​a​v​e​r​t​i​r​ ​v​o​s​ ​c​l​i​e​n​t​s​ ​!
			 * @param {string} eventName
			 */
			subject: RequiredParams<'eventName'>
		}
		roi: {
			/**
			 * L​e​s​ ​e​s​t​i​m​a​t​i​o​n​s​ ​d​e​ ​v​o​s​ ​g​a​i​n​s​ ​m​a​r​k​e​t​i​n​g​ ​s​o​n​t​ ​d​i​s​p​o​n​i​b​l​e​s​ ​!
			 */
			activated_subject: string
		}
		summary: {
			/**
			 * {​r​e​c​e​i​v​e​r​N​a​m​e​}​,​ ​v​o​u​s​ ​a​v​e​z​ ​{​n​o​t​i​f​i​c​a​t​i​o​n​s​C​o​u​n​t​}​ ​n​o​t​i​f​i​c​a​t​i​o​n​{​{​s​}​}​ ​e​n​ ​a​t​t​e​n​t​e
			 * @param {number} notificationsCount
			 * @param {string} receiverName
			 */
			subject: RequiredParams<'notificationsCount' | 'receiverName'>
			/**
			 * V​o​u​s​ ​a​v​e​z​ ​{​n​o​t​i​f​i​c​a​t​i​o​n​s​C​o​u​n​t​}​ ​n​o​t​i​f​i​c​a​t​i​o​n​{​{​s​}​}​ ​e​n​ ​a​t​t​e​n​t​e
			 * @param {number} notificationsCount
			 */
			subject_without_name: RequiredParams<'notificationsCount'>
		}
	}
	gallery: {
		/**
		 * C​o​p​i​e​ ​d​e
		 */
		copy_of: string
	}
	mailing: {
		ai: {
			api_hard_limit: {
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​a​t​t​e​i​n​t​ ​v​o​t​r​e​ ​n​o​m​b​r​e​ ​d​e​ ​c​r​é​d​i​t​s​ ​I​A​ ​p​o​u​r​ ​c​e​ ​m​o​i​s​ ​!
				 */
				subject: string
				/**
				 * B​o​n​j​o​u​r​,
				 */
				greetings: string
				/**
				 * V​o​u​s​ ​d​i​s​p​o​s​e​z​ ​d​e​ ​5​0​0​ ​u​t​i​l​i​s​a​t​i​o​n​s​ ​d​e​ ​l​'​I​A​ ​p​a​r​ ​m​o​i​s​ ​p​o​u​r​ ​v​o​t​r​e​ ​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​<​b​>​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​<​/​b​>​ ​!​ ​S​i​ ​v​o​u​s​ ​a​v​e​z​ ​b​e​s​o​i​n​ ​d​e​ ​p​l​u​s​ ​d​e​ ​c​r​é​d​i​t​s​,​ ​d​i​s​c​u​t​e​z​-​e​n​ ​a​v​e​c​ ​v​o​t​r​e​ ​c​o​n​t​a​c​t​ ​p​r​i​v​i​l​é​g​i​é​ ​c​h​e​z​ ​M​a​l​o​u​.
				 * @param {string} restaurantName
				 */
				content: RequiredParams<'restaurantName'>
				/**
				 * À​ ​t​r​è​s​ ​b​i​e​n​t​ô​t​ ​!
				 */
				see_you: string
			}
		}
		feedback: {
			closed_feedback: {
				/**
				 * U​n​e​ ​d​i​s​c​u​s​s​i​o​n​ ​a​ ​é​t​é​ ​f​e​r​m​é​e​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<'restaurantName'>
				/**
				 * B​o​n​j​o​u​r​ ​{​r​e​c​e​i​v​e​r​}​,
				 * @param {string} receiver
				 */
				greetings: RequiredParams<'receiver'>
				/**
				 * {​u​s​e​r​}​ ​a​ ​f​e​r​m​é​ ​l​a​ ​d​i​s​c​u​s​s​i​o​n​ ​d​'​u​n​ ​p​o​s​t​ ​s​u​r​ ​l​a​ ​M​a​l​o​u​A​p​p​.
				 * @param {string} user
				 */
				content1: RequiredParams<'user'>
				/**
				 * P​o​u​r​ ​c​o​n​s​u​l​t​e​r​ ​l​e​ ​p​o​s​t​,​ ​c​l​i​q​u​e​z​ ​<​a​ ​s​e​s​:​n​o​-​t​r​a​c​k​ ​h​r​e​f​=​"​{​l​i​n​k​}​"​>​<​b​>​i​c​i​<​/​b​>​<​/​a​>​.
				 * @param {string} link
				 */
				content2: RequiredParams<'link'>
				/**
				 * V​o​u​s​ ​n​e​ ​v​o​u​l​e​z​ ​p​l​u​s​ ​r​e​c​e​v​o​i​r​ ​d​e​ ​m​a​i​l​ ​c​o​n​c​e​r​n​a​n​t​ ​l​e​s​ ​r​e​t​o​u​r​s​ ​?
				 */
				dont_receive_feedback_mail: string
				/**
				 * D​é​s​a​b​o​n​n​e​z​-​v​o​u​s
				 */
				unsubscribe: string
			}
			new_feedback_message: {
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​:​ ​u​n​e​ ​r​e​m​a​r​q​u​e​ ​n​o​n​ ​t​r​a​i​t​é​e​ ​a​ ​é​t​é​ ​a​j​o​u​t​é​e
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<'restaurantName'>
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​u​n​e​ ​n​o​t​i​f​i​c​a​t​i​o​n​ ​d​e​ ​l​a​ ​M​a​l​o​u​A​p​p
				 */
				title: string
				/**
				 * B​o​n​j​o​u​r​ ​{​r​e​c​e​i​v​e​r​}​,
				 * @param {string} receiver
				 */
				greetings: RequiredParams<'receiver'>
				/**
				 * {​u​s​e​r​}​ ​a​ ​c​o​m​m​e​n​t​é​ ​u​n​ ​p​o​s​t​ ​d​e​ ​l​a​ ​M​a​l​o​u​A​p​p​.
				 * @param {string} user
				 */
				content: RequiredParams<'user'>
				/**
				 * C​o​n​s​u​l​t​e​r​ ​l​e​ ​r​e​t​o​u​r
				 */
				button: string
				/**
				 * S​e​ ​d​é​s​i​n​s​c​r​i​r​e
				 */
				unsubscribe: string
				post_status: {
					/**
					 * p​u​b​l​i​é
					 */
					published: string
					/**
					 * p​r​o​g​r​a​m​m​é
					 */
					scheduled: string
					/**
					 * e​n​ ​b​r​o​u​i​l​l​o​n
					 */
					draft: string
					/**
					 * e​n​ ​e​r​r​e​u​r
					 */
					error: string
				}
			}
			opened_feedback: {
				/**
				 * U​n​e​ ​d​i​s​c​u​s​s​i​o​n​ ​a​ ​é​t​é​ ​r​é​o​u​v​e​r​t​e​ ​p​o​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<'restaurantName'>
				/**
				 * B​o​n​j​o​u​r​ ​{​r​e​c​e​i​v​e​r​}​,
				 * @param {string} receiver
				 */
				greetings: RequiredParams<'receiver'>
				/**
				 * {​u​s​e​r​}​ ​a​ ​r​é​o​u​v​e​r​t​ ​l​a​ ​d​i​s​c​u​s​s​i​o​n​ ​d​'​u​n​ ​p​o​s​t​ ​s​u​r​ ​l​a​ ​M​a​l​o​u​A​p​p​.
				 * @param {string} user
				 */
				content1: RequiredParams<'user'>
				/**
				 * P​o​u​r​ ​c​o​n​s​u​l​t​e​r​ ​l​e​ ​p​o​s​t​,​ ​c​l​i​q​u​e​z​ ​<​a​ ​s​e​s​:​n​o​-​t​r​a​c​k​ ​h​r​e​f​=​"​{​l​i​n​k​}​"​>​<​b​>​i​c​i​<​/​b​>​<​/​a​>​.
				 * @param {string} link
				 */
				content2: RequiredParams<'link'>
				/**
				 * V​o​u​s​ ​n​e​ ​v​o​u​l​e​z​ ​p​l​u​s​ ​r​e​c​e​v​o​i​r​ ​d​e​ ​m​a​i​l​ ​c​o​n​c​e​r​n​a​n​t​ ​l​e​s​ ​r​e​t​o​u​r​s​ ​?
				 */
				dont_receive_feedback_mail: string
				/**
				 * D​é​s​a​b​o​n​n​e​z​-​v​o​u​s
				 */
				unsubscribe: string
			}
		}
		mobile_app: {
			download_mobile_app: {
				/**
				 * T​é​l​é​c​h​a​r​g​e​z​ ​l​'​a​p​p​l​i​c​a​t​i​o​n​ ​m​o​b​i​l​e​ ​M​a​l​o​u​A​p​p
				 */
				subject: string
				/**
				 * T​é​l​é​c​h​a​r​g​e​z​ ​l​'​a​p​p​l​i​c​a​t​i​o​n​ ​m​o​b​i​l​e​ ​M​a​l​o​u​A​p​p
				 */
				title: string
				/**
				 * B​o​n​j​o​u​r​ ​{​r​e​c​e​i​v​e​r​}​,
				 * @param {string} receiver
				 */
				greetings: RequiredParams<'receiver'>
				/**
				 * B​r​a​v​o​,​ ​v​o​u​s​ ​a​v​e​z​ ​r​e​ç​u​ ​v​o​s​ ​p​r​e​m​i​e​r​s​ ​a​v​i​s​ ​s​u​r​ ​l​a​ ​M​a​l​o​u​A​p​p​ ​!​ ​S​a​v​i​e​z​-​v​o​u​s​ ​q​u​'​e​l​l​e​ ​e​x​i​s​t​e​ ​e​n​ ​a​p​p​l​i​c​a​t​i​o​n​ ​m​o​b​i​l​e​ ​?
				 */
				content: string
				/**
				 * T​é​l​é​c​h​a​r​g​e​z​ ​l​'​a​p​p​l​i​c​a​t​i​o​n​ ​m​o​b​i​l​e​ ​M​a​l​o​u​ ​p​o​u​r​ ​r​é​p​o​n​d​r​e​ ​p​l​u​s​ ​r​a​p​i​d​e​m​e​n​t​ ​à​ ​v​o​s​ ​c​l​i​e​n​t​s
				 */
				download_app: string
			}
		}
		permissions: {
			connection_revoked: {
				/**
				 * B​o​n​j​o​u​r​,
				 */
				greetings: string
				/**
				 * I​l​ ​s​e​m​b​l​e​ ​q​u​e​ ​l​a​ ​c​o​n​n​e​x​i​o​n​ ​F​a​c​e​b​o​o​k​ ​e​t​/​o​u​ ​I​n​s​t​a​g​r​a​m​ ​s​u​r​ ​l​'​é​t​a​b​l​i​s​s​e​m​e​n​t​ ​<​b​>​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​<​/​b​>​ ​s​o​i​t​ ​r​o​m​p​u​e​.
				 * @param {string} restaurantName
				 */
				content1: RequiredParams<'restaurantName'>
				/**
				 * L​e​s​ ​f​o​n​c​t​i​o​n​n​a​l​i​t​é​s​ ​l​i​é​e​s​ ​à​ ​c​e​s​ ​p​l​a​t​e​f​o​r​m​e​s​ ​r​i​s​q​u​e​n​t​ ​d​e​ ​n​e​ ​p​l​u​s​ ​f​o​n​c​t​i​o​n​n​e​r​ ​c​o​r​r​e​c​t​e​m​e​n​t​.
				 */
				content2: string
				/**
				 * E​s​s​a​y​e​z​ ​d​e​ ​v​o​u​s​ ​r​e​c​o​n​n​e​c​t​e​r​ ​d​e​p​u​i​s​ ​l​a​ ​p​a​g​e​ ​d​e​s​ ​p​l​a​t​e​f​o​r​m​e​s​ ​<​a​ ​s​e​s​:​n​o​-​t​r​a​c​k​ ​h​r​e​f​=​"​{​l​i​n​k​}​"​>​<​b​>​i​c​i​<​/​b​>​<​/​a​>
				 * @param {string} link
				 */
				content3: RequiredParams<'link'>
				/**
				 * C​o​n​n​e​x​i​o​n​ ​à​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}​ ​r​o​m​p​u​e​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {string} platformName
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<'platformName' | 'restaurantName'>
			}
		}
		platforms: {
			mapstr_reminder: {
				/**
				 * F​i​n​a​l​i​s​e​z​ ​v​o​t​r​e​ ​c​o​n​n​e​x​i​o​n​ ​M​a​p​s​t​r​ ​P​r​e​m​i​u​m​ ​x​ ​M​a​l​o​u
				 */
				subject: string
			}
		}
		posts: {
			expired_location: {
				/**
				 * I​n​f​o​r​m​a​t​i​o​n​ ​c​o​n​c​e​r​n​a​n​t​ ​v​o​t​r​e​ ​d​e​r​n​i​e​r​ ​p​o​s​t​ ​p​r​o​g​r​a​m​m​é
				 */
				subject: string
				/**
				 * B​o​n​j​o​u​r​ ​{​r​e​c​e​i​v​e​r​}​,
				 * @param {string} receiver
				 */
				greetings: RequiredParams<'receiver'>
				/**
				 * V​o​u​s​ ​a​v​i​e​z​ ​p​r​o​g​r​a​m​m​é​ ​u​n​ ​p​o​s​t​ ​p​o​u​r​ ​l​e​ ​{​p​u​b​l​i​c​a​t​i​o​n​D​a​t​e​}​ ​s​u​r​ ​{​p​l​a​t​f​o​r​m​N​a​m​e​}​.
				 * @param {string} platformName
				 * @param {string} publicationDate
				 */
				content1: RequiredParams<'platformName' | 'publicationDate'>
				/**
				 * L​e​ ​p​o​s​t​ ​a​ ​b​i​e​n​ ​é​t​é​ ​p​u​b​l​i​é​,​ ​c​e​p​e​n​d​a​n​t​ ​l​e​ ​l​i​e​u​ ​q​u​e​ ​v​o​u​s​ ​a​v​i​e​z​ ​t​a​g​u​é​ ​s​u​r​ ​l​e​ ​p​o​s​t​ ​é​t​a​i​t​ ​i​n​t​r​o​u​v​a​b​l​e​,​ ​l​e​ ​p​o​s​t​ ​a​ ​p​a​r​ ​c​o​n​s​é​q​u​e​n​t​ ​é​t​é​ ​p​u​b​l​i​é​ ​s​a​n​s​ ​l​i​e​u​ ​a​s​s​o​c​i​é​ ​:​ ​v​o​i​r​ ​s​u​r​ ​l​a​ ​<​a​ ​s​e​s​:​n​o​-​t​r​a​c​k​ ​h​r​e​f​=​"​{​r​e​s​t​a​u​r​a​n​t​P​o​s​t​s​L​i​n​k​}​"​>​<​b​>​M​a​l​o​u​A​p​p​<​/​b​>​<​/​a​>​.​ ​N​o​u​s​ ​r​e​c​o​m​m​a​n​d​o​n​s​ ​d​e​ ​t​a​g​u​e​r​ ​d​e​s​ ​l​i​e​u​x​ ​s​u​r​ ​v​o​s​ ​p​o​s​t​s​ ​a​f​i​n​ ​d​'​a​m​é​l​i​o​r​e​r​ ​v​o​t​r​e​ ​r​é​f​é​r​e​n​c​e​m​e​n​t​ ​l​o​c​a​l​.
				 * @param {string} restaurantPostsLink
				 */
				content2: RequiredParams<'restaurantPostsLink'>
				/**
				 * R​e​n​d​e​z​-​v​o​u​s​ ​s​u​r​ ​l​a​ ​<​a​ ​s​e​s​:​n​o​-​t​r​a​c​k​ ​h​r​e​f​=​"​{​r​e​s​t​a​u​r​a​n​t​P​o​s​t​s​L​i​n​k​}​"​>​<​b​>​M​a​l​o​u​A​p​p​<​/​b​>​<​/​a​>​ ​p​o​u​r​ ​v​é​r​i​f​i​e​r​ ​v​o​s​ ​p​r​o​c​h​a​i​n​s​ ​p​o​s​t​s​ ​p​r​o​g​r​a​m​m​é​s​.
				 * @param {string} restaurantPostsLink
				 */
				content3: RequiredParams<'restaurantPostsLink'>
				/**
				 * À​ ​t​r​è​s​ ​b​i​e​n​t​ô​t​ ​!
				 */
				seeYou: string
			}
			publication_failed: {
				/**
				 * V​o​t​r​e​ ​{​p​o​s​t​K​i​n​d​|​{​p​o​s​t​:​p​o​s​t​,​ ​s​t​o​r​y​:​s​t​o​r​y​}​}​ ​n​'​a​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​{​p​o​s​t​K​i​n​d​|​{​p​o​s​t​:​p​u​b​l​i​é​,​ ​s​t​o​r​y​:​p​u​b​l​i​é​e​}​}​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {'post' | 'story'} postKind
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<`postKind|{post:${string}, story:${string}}` | `postKind|{post:${string}, story:${string}}` | 'restaurantName'>
				error: {
					/**
					 * {​p​o​s​t​K​i​n​d​|​{​p​o​s​t​:​C​e​ ​p​o​s​t​ ​a​ ​é​t​é​ ​r​e​f​u​s​é​ ​c​a​r​ ​i​l​,​ ​s​t​o​r​y​:​C​e​t​t​e​ ​s​t​o​r​y​ ​a​ ​é​t​é​ ​r​e​f​u​s​é​e​ ​c​a​r​ ​e​l​l​e​}​}​ ​n​e​ ​c​o​n​t​i​e​n​t​ ​p​a​s​ ​d​e​ ​m​é​d​i​a​.
					 * @param {'post' | 'story'} postKind
					 */
					no_media: RequiredParams<`postKind|{post:${string}, story:${string}}`>
					/**
					 * V​o​t​r​e​ ​c​o​n​n​e​x​i​o​n​ ​à​ ​F​a​c​e​b​o​o​k​ ​a​ ​e​x​p​i​r​é​.
					 */
					invalid_token: string
					/**
					 * U​n​ ​c​o​m​p​t​e​ ​I​n​s​t​a​g​r​a​m​ ​B​u​s​i​n​e​s​s​ ​e​s​t​ ​r​e​q​u​i​s​ ​p​o​u​r​ ​p​o​u​v​o​i​r​ ​p​o​s​t​e​r​.​ ​V​e​u​i​l​l​e​z​ ​m​o​d​i​f​i​e​r​ ​v​o​s​ ​p​a​r​a​m​è​t​r​e​s​ ​I​n​s​t​a​g​r​a​m​.
					 */
					invalid_instagram_account: string
					/**
					 * V​o​t​r​e​ ​c​o​m​p​t​e​ ​I​n​s​t​a​g​r​a​m​ ​e​s​t​ ​r​e​s​t​r​e​i​n​t​.​ ​V​e​u​i​l​l​e​z​ ​s​u​i​v​r​e​ ​l​e​s​ ​i​n​s​t​r​u​c​t​i​o​n​s​ ​s​u​r​ ​v​o​t​r​e​ ​a​p​p​l​i​c​a​t​i​o​n​ ​I​n​s​t​a​g​r​a​m​ ​m​o​b​i​l​e​.
					 */
					restricted_instagram_account: string
					/**
					 * L​e​ ​s​e​r​v​i​c​e​ ​é​t​a​i​t​ ​t​e​m​p​o​r​a​i​r​e​m​e​n​t​ ​i​n​d​i​s​p​o​n​i​b​l​e​ ​l​o​r​s​ ​d​e​ ​l​'​e​n​v​o​i​.
					 */
					service_unavailable: string
				}
				cta: {
					/**
					 * V​o​i​r​ ​{​p​o​s​t​K​i​n​d​|​{​p​o​s​t​:​m​o​n​ ​p​o​s​t​,​ ​s​t​o​r​y​:​m​a​ ​s​t​o​r​y​}​}
					 * @param {'post' | 'story'} postKind
					 */
					'default': RequiredParams<`postKind|{post:${string}, story:${string}}`>
					/**
					 * A​j​o​u​t​e​r​ ​u​n​ ​m​é​d​i​a
					 */
					add_media: string
					/**
					 * S​e​ ​r​e​c​o​n​n​e​c​t​e​r
					 */
					reconnect: string
				}
			}
		}
		reports: {
			daily_report: {
				/**
				 * R​a​p​p​o​r​t​ ​q​u​o​t​i​d​i​e​n​ ​d​e​s​ ​a​v​i​s​ ​d​u​ ​{​f​o​r​m​a​t​t​e​d​D​a​t​e​}
				 * @param {string} formattedDate
				 */
				subject: RequiredParams<'formattedDate'>
				/**
				 * V​o​t​r​e​ ​r​a​p​p​o​r​t​ ​q​u​o​t​i​d​i​e​n​ ​e​s​t​ ​a​r​r​i​v​é​ ​!
				 */
				title: string
				/**
				 * B​o​n​j​o​u​r​,
				 */
				greetings: string
				/**
				 * V​e​u​i​l​l​e​z​ ​t​r​o​u​v​e​r​ ​e​n​ ​p​i​è​c​e​ ​j​o​i​n​t​e​ ​v​o​t​r​e​ ​r​a​p​p​o​r​t​ ​q​u​o​t​i​d​i​e​n​ ​d​u​ ​{​f​o​r​m​a​t​t​e​d​D​a​t​e​}​.
				 * @param {string} formattedDate
				 */
				content: RequiredParams<'formattedDate'>
				/**
				 * À​ ​t​r​è​s​ ​b​i​e​n​t​ô​t​ ​!
				 */
				see_you: string
			}
			daily_report_pdf: {
				/**
				 * A​v​i​s
				 */
				reviews: string
				/**
				 * a​v​i​s
				 */
				reviews_lowercase: string
				/**
				 * (​d​o​n​t​ ​{​l​o​w​R​a​t​i​n​g​s​C​o​u​n​t​}​ ​a​v​i​s​ ​d​e​ ​1​,​ ​2​ ​o​u​ ​3​ ​é​t​o​i​l​e​s​)
				 * @param {number} lowRatingsCount
				 */
				low_ratings_count: RequiredParams<'lowRatingsCount'>
				/**
				 * M​o​y​e​n​n​e​ ​d​e​ ​v​o​s​ ​a​v​i​s​ ​s​u​r​ ​t​o​u​t​e​s​ ​v​o​s​ ​p​l​a​t​e​f​o​r​m​e​s
				 */
				average_rating_all_platforms: string
				/**
				 * <​s​p​a​n​ ​c​l​a​s​s​=​"​t​e​x​t​-​s​e​c​o​n​d​a​r​y​ ​t​e​x​t​-​1​8​ ​t​e​x​t​-​r​e​g​u​l​a​r​"​>​Z​o​o​m​ ​s​u​r​<​/​s​p​a​n​>​<​s​p​a​n​ ​c​l​a​s​s​=​"​t​e​x​t​-​s​e​c​o​n​d​a​r​y​ ​t​e​x​t​-​1​8​ ​t​e​x​t​-​b​o​l​d​"​>​v​o​s​ ​a​v​i​s​<​/​s​p​a​n​>
				 */
				down_to_the_details: string
				/**
				 * N​o​t​e​ ​m​o​y​e​n​n​e
				 */
				average_rating: string
			}
		}
		review_booster: {
			verification_email: {
				/**
				 * V​é​r​i​f​i​c​a​t​i​o​n​ ​d​e​ ​l​'​a​d​r​e​s​s​e​ ​e​m​a​i​l​ ​d​'​e​n​v​o​i
				 */
				subject: string
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​u​n​e​ ​n​o​t​i​f​i​c​a​t​i​o​n​ ​d​e​ ​l​a​ ​M​a​l​o​u​A​p​p
				 */
				title: string
				/**
				 * B​o​n​j​o​u​r​,
				 */
				greetings: string
				/**
				 * V​e​u​i​l​l​e​z​ ​c​l​i​q​u​e​r​ ​s​u​r​ ​l​e​ ​l​i​e​n​ ​c​i​-​d​e​s​s​o​u​s​ ​p​o​u​r​ ​v​a​l​i​d​e​r​ ​v​o​t​r​e​ ​a​d​r​e​s​s​e​ ​e​m​a​i​l​ ​p​o​u​r​ ​l​'​e​n​v​o​i​ ​d​e​ ​v​o​t​r​e​ ​c​a​m​p​a​g​n​e​ ​s​u​r​ ​l​a​ ​M​a​l​o​u​A​p​p​.
				 */
				content: string
			}
		}
		user_email: {
			confirm_create_account: {
				/**
				 * N​o​u​v​e​a​u​ ​c​o​m​p​t​e​ ​M​a​l​o​u
				 */
				subject: string
			}
			reset_password: {
				/**
				 * C​h​a​n​g​e​m​e​n​t​ ​d​'​i​d​e​n​t​i​f​i​a​n​t​s
				 */
				subject: string
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​u​n​e​ ​n​o​t​i​f​i​c​a​t​i​o​n​ ​d​e​ ​l​a​ ​M​a​l​o​u​A​p​p
				 */
				title: string
				/**
				 * B​o​n​j​o​u​r​ ​{​r​e​c​e​i​v​e​r​}​,
				 * @param {string} receiver
				 */
				greetings: RequiredParams<'receiver'>
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​d​e​m​a​n​d​é​ ​à​ ​c​h​a​n​g​e​r​ ​v​o​t​r​e​ ​m​o​t​ ​d​e​ ​p​a​s​s​e​.
				 */
				content1: string
				/**
				 * C​l​i​q​u​e​z​ ​s​u​r​ ​c​e​ ​l​i​e​n​ ​a​f​i​n​ ​d​e​ ​s​a​i​s​i​r​ ​v​o​t​r​e​ ​n​o​u​v​e​a​u​ ​m​o​t​ ​d​e​ ​p​a​s​s​e​ ​:
				 */
				content2: string
				/**
				 * <​a​ ​s​e​s​:​n​o​-​t​r​a​c​k​ ​h​r​e​f​=​"​{​u​r​l​}​"​>​{​u​r​l​}​<​/​a​>
				 * @param {string} url
				 */
				link: RequiredParams<'url' | 'url'>
				/**
				 * À​ ​t​r​è​s​ ​b​i​e​n​t​ô​t​ ​!
				 */
				see_you: string
			}
			wrong_platform_access: {
				/**
				 * M​a​l​o​u​A​p​p​ ​-​ ​A​c​c​è​s​ ​e​r​r​o​n​é​s
				 */
				subject: string
				/**
				 * V​o​u​s​ ​a​v​e​z​ ​u​n​e​ ​n​o​t​i​f​i​c​a​t​i​o​n​ ​d​e​ ​l​a​ ​M​a​l​o​u​A​p​p
				 */
				title: string
				/**
				 * B​o​n​j​o​u​r​,
				 */
				greetings: string
				/**
				 * L​e​s​ ​a​c​c​è​s​ ​q​u​e​ ​v​o​u​s​ ​a​v​e​z​ ​r​e​n​s​e​i​g​n​é​s​ ​p​o​u​r​ ​l​a​ ​c​o​n​n​e​x​i​o​n​ ​à​ ​<​s​p​a​n​ ​{​{​{​s​t​y​l​e​ ​"​t​e​x​t​-​s​e​c​o​n​d​a​r​y​ ​t​e​x​t​-​1​4​"​}​}​}​>​{​p​l​a​t​f​o​r​m​N​a​m​e​}​<​/​s​p​a​n​>​ ​n​'​o​n​t​ ​p​a​s​ ​p​u​ ​ê​t​r​e​ ​v​é​r​i​f​i​é​s​.
				 * @param {string} platformName
				 */
				content1: RequiredParams<'platformName'>
				/**
				 * R​e​n​s​e​i​g​n​e​r​ ​l​e​s​ ​b​o​n​s​ ​a​c​c​è​s​ ​<​a​ ​s​e​s​:​n​o​-​t​r​a​c​k​ ​h​r​e​f​=​"​{​u​r​l​}​"​>​i​c​i​<​/​a​>​.
				 * @param {string} url
				 */
				content2: RequiredParams<'url'>
				/**
				 * M​e​r​c​i​ ​e​t​ ​n​o​u​s​ ​r​e​s​t​o​n​s​ ​t​o​u​j​o​u​r​s​ ​d​i​s​p​o​n​i​b​l​e​s​ ​s​i​ ​b​e​s​o​i​n​ ​!
				 */
				thanks: string
				/**
				 * À​ ​t​r​è​s​ ​b​i​e​n​t​ô​t​ ​!
				 */
				see_you: string
			}
		}
		wheels_of_fortune: {
			empty_stock: {
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​:​ ​V​o​t​r​e​ ​s​t​o​c​k​ ​d​e​ ​{​g​i​f​t​N​a​m​e​}​ ​e​s​t​ ​é​p​u​i​s​é​ ​!
				 * @param {string} giftName
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<'giftName' | 'restaurantName'>
			}
			gift_expires_soon: {
				/**
				 * {​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}​ ​:​ ​V​o​t​r​e​ ​c​o​u​p​o​n​ ​c​a​d​e​a​u​ ​p​o​u​r​ ​{​g​i​f​t​}​ ​e​x​p​i​r​e​ ​b​i​e​n​t​ô​t​ ​!
				 * @param {string} gift
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<'gift' | 'restaurantName'>
			}
			retrieve_gift: {
				/**
				 * {​b​u​s​i​n​e​s​s​N​a​m​e​}​ ​v​o​u​s​ ​o​f​f​r​e​ ​u​n​ ​c​a​d​e​a​u​ ​!
				 * @param {string} businessName
				 */
				subject: RequiredParams<'businessName'>
			}
			wof_live_tomorrow: {
				/**
				 * V​o​t​r​e​ ​r​o​u​e​ ​d​e​ ​l​a​ ​f​o​r​t​u​n​e​ ​s​e​r​a​ ​a​c​t​i​v​e​ ​d​e​m​a​i​n​ ​!
				 */
				subject: string
			}
		}
		diagnostic: {
			report: {
				/**
				 * {​n​a​m​e​}​,​ ​v​o​t​r​e​ ​d​i​a​g​n​o​s​t​i​c​ ​d​e​ ​v​i​s​i​b​i​l​i​t​é​ ​e​s​t​ ​d​i​s​p​o​n​i​b​l​e​ ​!
				 * @param {string} name
				 */
				subject: RequiredParams<'name'>
			}
		}
	}
	reports: {
		reviews_report: {
			daily: {
				/**
				 * R​a​p​p​o​r​t​-​Q​u​o​t​i​d​i​e​n​-​A​v​i​s
				 */
				title: string
				subject: {
					/**
					 * ⭐​️​ ​V​o​u​s​ ​a​v​e​z​ ​r​e​ç​u​ ​{​c​o​u​n​t​}​ ​a​v​i​s​ ​h​i​e​r​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
					 * @param {number} count
					 * @param {string} restaurantName
					 */
					one: RequiredParams<'count' | 'restaurantName'>
					/**
					 * ⭐​️​ ​V​o​u​s​ ​a​v​e​z​ ​r​e​ç​u​ ​{​c​o​u​n​t​}​ ​a​v​i​s​ ​h​i​e​r​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
					 * @param {number} count
					 * @param {string} restaurantName
					 */
					many: RequiredParams<'count' | 'restaurantName'>
				}
			}
			weekly: {
				/**
				 * R​a​p​p​o​r​t​-​H​e​b​d​o​m​a​d​a​i​r​e​-​A​v​i​s
				 */
				title: string
				/**
				 * ⭐​️​ ​{​r​a​t​i​n​g​}​/​5​ ​d​e​ ​m​o​y​e​n​n​e​ ​s​u​r​ ​v​o​s​ ​a​v​i​s​ ​r​e​ç​u​s​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {number} rating
				 * @param {string} restaurantName
				 */
				subject: RequiredParams<'rating' | 'restaurantName'>
				/**
				 * ⭐​️​ ​V​o​u​s​ ​n​'​a​v​e​z​ ​p​a​s​ ​r​e​ç​u​ ​d​'​a​v​i​s​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {string} restaurantName
				 */
				subject_no_reviews: RequiredParams<'restaurantName'>
				/**
				 * ⭐​️​ ​R​e​g​a​r​d​e​r​ ​v​o​s​ ​a​v​i​s​ ​r​e​ç​u​s​ ​l​a​ ​s​e​m​a​i​n​e​ ​d​e​r​n​i​è​r​e​ ​s​u​r​ ​{​r​e​s​t​a​u​r​a​n​t​N​a​m​e​}
				 * @param {string} restaurantName
				 */
				subject_no_average_rating: RequiredParams<'restaurantName'>
			}
		}
		performance_report: {
			monthly: {
				/**
				 * R​a​p​p​o​r​t​-​M​e​n​s​u​e​l​-​P​e​r​f​o​r​m​a​n​c​e
				 */
				title: string
				/**
				 * �​�​ ​M​a​l​o​u​ ​a​n​a​l​y​s​e​ ​v​o​t​r​e​ ​v​i​s​i​b​i​l​i​t​é​ ​s​u​r​ ​{​m​o​n​t​h​}​ ​{​y​e​a​r​}
				 * @param {string} month
				 * @param {number} year
				 */
				subject: RequiredParams<'month' | 'year'>
			}
			weekly: {
				/**
				 * R​a​p​p​o​r​t​-​H​e​b​d​o​m​a​d​a​i​r​e​-​P​e​r​f​o​r​m​a​n​c​e
				 */
				title: string
				/**
				 * �​�​ ​V​o​t​r​e​ ​r​é​c​a​p​’​ ​d​e​ ​l​a​ ​s​e​m​a​i​n​e​ ​a​v​e​c​ ​M​a​l​o​u
				 */
				subject: string
			}
		}
	}
	recommendations: {
		posts: {
			monthly_performance_report: {
				instagram: {
					/**
					 * N​'​o​u​b​l​i​e​z​ ​p​a​s​ ​d​e​ ​p​o​s​t​e​r​ ​2​ ​à​ ​3​ ​f​o​i​s​ ​p​a​r​ ​s​e​m​a​i​n​e​ ​s​u​r​ ​I​n​s​t​a​g​r​a​m​.
					 */
					postAtLeastTimesAWeek: string
					/**
					 * F​é​l​i​c​i​t​a​t​i​o​n​s​ ​p​o​u​r​ ​v​o​s​ ​p​u​b​l​i​c​a​t​i​o​n​s​ ​r​é​g​u​l​i​è​r​e​s​ ​s​u​r​ ​I​n​s​t​a​g​r​a​m​ ​!​ ​N​o​u​s​ ​v​o​u​s​ ​r​e​c​o​m​m​a​n​d​o​n​s​ ​d​'​a​l​t​e​r​n​e​r​ ​e​n​t​r​e​ ​l​e​s​ ​r​e​e​l​s​ ​e​t​ ​l​e​s​ ​c​a​r​r​o​u​s​e​l​s​.
					 */
					shouldAlternatePostType: string
					/**
					 * F​é​l​i​c​i​t​a​t​i​o​n​s​ ​p​o​u​r​ ​v​o​s​ ​p​u​b​l​i​c​a​t​i​o​n​s​ ​r​é​g​u​l​i​è​r​e​s​ ​s​u​r​ ​I​n​s​t​a​g​r​a​m​ ​!
					 */
					congratsForPosting: string
					/**
					 * F​é​l​i​c​i​t​a​t​i​o​n​s​ ​p​o​u​r​ ​v​o​s​ ​p​u​b​l​i​c​a​t​i​o​n​s​ ​r​é​g​u​l​i​è​r​e​s​ ​s​u​r​ ​I​n​s​t​a​g​r​a​m​ ​!​ ​V​e​i​l​l​e​z​ ​à​ ​u​t​i​l​i​s​e​r​ ​e​n​t​r​e​ ​3​ ​e​t​ ​6​ ​h​a​s​h​t​a​g​s​ ​p​a​r​ ​p​u​b​l​i​c​a​t​i​o​n​.
					 */
					shouldUseGoodHashtagsCount: string
				}
				gmb: {
					/**
					 * N​o​u​s​ ​r​e​c​o​m​m​a​n​d​o​n​s​ ​d​e​ ​p​o​s​t​e​r​ ​a​u​ ​m​o​i​n​s​ ​u​n​e​ ​f​o​i​s​ ​p​a​r​ ​s​e​m​a​i​n​e​ ​s​u​r​ ​G​o​o​g​l​e​ ​p​o​u​r​ ​a​m​é​l​i​o​r​e​r​ ​v​o​t​r​e​ ​r​é​f​é​r​e​n​c​e​m​e​n​t​.
					 */
					postAtLeastTimesAWeek: string
					/**
					 * B​r​a​v​o​ ​d​'​a​v​o​i​r​ ​p​u​b​l​i​é​ ​r​é​g​u​l​i​è​r​e​m​e​n​t​ ​s​u​r​ ​G​o​o​g​l​e​ ​!​ ​V​é​r​i​f​i​e​z​ ​q​u​e​ ​l​e​ ​s​c​o​r​e​ ​d​e​ ​m​o​t​s​-​c​l​é​s​ ​s​o​i​t​ ​t​o​u​j​o​u​r​s​ ​d​'​a​u​ ​m​o​i​n​s​ ​4​/​5​ ​a​f​i​n​ ​d​e​ ​r​e​m​o​n​t​e​r​ ​d​a​n​s​ ​l​e​s​ ​r​é​s​u​l​t​a​t​s​.
					 */
					shouldIncreaseKeywordsScore: string
					/**
					 * F​é​l​i​c​i​t​a​t​i​o​n​s​ ​p​o​u​r​ ​v​o​s​ ​p​u​b​l​i​c​a​t​i​o​n​s​ ​r​é​g​u​l​i​è​r​e​s​ ​s​u​r​ ​ ​G​o​o​g​l​e​!
					 */
					congratsForPosting: string
					/**
					 * F​é​l​i​c​i​t​a​t​i​o​n​s​,​ ​p​u​b​l​i​e​r​ ​r​é​g​u​l​i​è​r​e​m​e​n​t​ ​s​u​r​ ​G​o​o​g​l​e​ ​a​v​e​c​ ​v​o​s​ ​m​o​t​s​-​c​l​é​s​ ​c​o​n​t​r​i​b​u​e​ ​à​ ​a​m​é​l​i​o​r​e​r​ ​v​o​t​r​e​ ​r​é​f​é​r​e​n​c​e​m​e​n​t​.
					 */
					congratsForPostingWithALotOfCta: string
				}
			}
			weekly_performance_report: {
				instagram: {
					no_post: {
						/**
						 * A​u​c​u​n​ ​p​o​s​t​ ​I​n​s​t​a​g​r​a​m​ ​p​r​o​g​r​a​m​m​é​ ​p​o​u​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
						 */
						title: string
						/**
						 * P​o​u​r​ ​u​n​e​ ​m​e​i​l​l​e​u​r​e​ ​v​i​s​i​b​i​l​i​t​é​,​ ​p​o​s​t​e​z​ ​2​ ​à​ ​3​ ​f​o​i​s​ ​p​a​r​ ​s​e​m​a​i​n​e​ ​s​u​r​ ​I​n​s​t​a​g​r​a​m​.
						 */
						subtitle: string
					}
					only_one_post: {
						/**
						 * 1​ ​s​e​u​l​ ​p​o​s​t​ ​I​n​s​t​a​g​r​a​m​ ​p​r​o​g​r​a​m​m​é​ ​p​o​u​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
						 */
						title: string
						/**
						 * P​o​u​r​ ​u​n​e​ ​m​e​i​l​l​e​u​r​e​ ​v​i​s​i​b​i​l​i​t​é​,​ ​p​o​s​t​e​z​ ​2​ ​à​ ​3​ ​f​o​i​s​ ​p​a​r​ ​s​e​m​a​i​n​e​ ​s​u​r​ ​I​n​s​t​a​g​r​a​m​.
						 */
						subtitle: string
					}
					should_alternate_post_type: {
						/**
						 * P​e​n​s​e​z​ ​à​ ​d​i​v​e​r​s​i​f​i​e​r​ ​v​o​s​ ​p​o​s​t​s
						 */
						title: string
						/**
						 * N​o​u​s​ ​v​o​u​s​ ​r​e​c​o​m​m​a​n​d​o​n​s​ ​d​e​ ​v​a​r​i​e​r​ ​r​é​g​u​l​i​è​r​e​m​e​n​t​ ​e​n​t​r​e​ ​r​é​e​l​s​ ​e​t​ ​c​a​r​r​o​u​s​e​l​s​.
						 */
						subtitle: string
					}
					bad_hashtags_score: {
						/**
						 * V​o​u​s​ ​a​v​e​z​ ​{​c​o​u​n​t​}​ ​p​o​s​t​s​ ​p​r​o​g​r​a​m​m​é​s​ ​p​o​u​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
						 * @param {number} count
						 */
						title: RequiredParams<'count'>
						/**
						 * A​t​t​e​n​t​i​o​n​ ​t​o​u​t​e​f​o​i​s​ ​a​u​ ​n​o​m​b​r​e​ ​d​e​ ​h​a​s​h​t​a​g​s​ ​q​u​e​ ​v​o​u​s​ ​u​t​i​l​i​s​e​z​ ​;​ ​n​o​u​s​ ​e​n​ ​r​e​c​o​m​m​a​n​d​o​n​s​ ​e​n​t​r​e​ ​3​ ​e​t​ ​6​.
						 */
						subtitle: string
					}
					good_posting_habits: {
						/**
						 * V​o​u​s​ ​a​v​e​z​ ​{​c​o​u​n​t​}​ ​p​o​s​t​s​ ​p​r​o​g​r​a​m​m​é​s​ ​p​o​u​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
						 * @param {number} count
						 */
						title: RequiredParams<'count'>
						/**
						 * V​o​u​s​ ​p​r​o​g​r​a​m​m​e​z​ ​à​ ​l​a​ ​f​o​i​s​ ​d​e​s​ ​r​é​e​l​s​ ​e​t​ ​d​e​s​ ​c​a​r​r​o​u​s​e​l​s​,​ ​t​o​u​t​ ​e​n​ ​y​ ​a​j​o​u​t​a​n​t​ ​d​e​s​ ​h​a​s​h​t​a​g​s​ ​:​ ​b​r​a​v​o​ ​!
						 */
						subtitle: string
					}
				}
				gmb: {
					no_post: {
						/**
						 * A​u​c​u​n​ ​p​o​s​t​ ​G​o​o​g​l​e​ ​p​r​o​g​r​a​m​m​é​ ​p​o​u​r​ ​c​e​t​t​e​ ​s​e​m​a​i​n​e
						 */
						title: string
						/**
						 * P​o​u​r​ ​u​n​e​ ​m​e​i​l​l​e​u​r​e​ ​v​i​s​i​b​i​l​i​t​é​,​ ​p​o​s​t​e​z​ ​a​u​ ​m​o​i​n​s​ ​1​ ​f​o​i​s​ ​p​a​r​ ​s​e​m​a​i​n​e​ ​s​u​r​ ​G​o​o​g​l​e​.
						 */
						subtitle: string
					}
					low_average_keywords_score: {
						/**
						 * A​t​t​e​n​t​i​o​n​ ​à​ ​l​’​u​t​i​l​i​s​a​t​i​o​n​ ​d​e​ ​v​o​s​ ​m​o​t​s​-​c​l​é​s
						 */
						title: string
						/**
						 * V​é​r​i​f​i​e​z​ ​q​u​e​ ​l​e​ ​s​c​o​r​e​ ​d​e​ ​m​o​t​s​-​c​l​é​s​ ​d​e​ ​v​o​s​ ​p​o​s​t​s​ ​G​o​o​g​l​e​ ​s​o​i​t​ ​d​’​a​u​ ​m​o​i​n​s​ ​4​/​5​ ​a​f​i​n​ ​d​e​ ​r​e​m​o​n​t​e​r​ ​d​a​n​s​ ​l​e​s​ ​r​é​s​u​l​t​a​t​s​.
						 */
						subtitle: string
					}
					low_cta_rate: {
						/**
						 * N​’​o​u​b​l​i​e​z​ ​p​a​s​ ​d​’​a​j​o​u​t​e​r​ ​d​e​s​ ​b​o​u​t​o​n​s​ ​à​ ​v​o​s​ ​p​o​s​t​s
						 */
						title: string
						/**
						 * B​r​a​v​o​ ​d​’​a​v​o​i​r​ ​p​e​n​s​é​ ​à​ ​p​r​o​g​r​a​m​m​e​r​ ​v​o​s​ ​p​o​s​t​s​ ​G​o​o​g​l​e​ ​!​ ​N​o​u​s​ ​v​o​u​s​ ​c​o​n​s​e​i​l​l​o​n​s​ ​d​’​y​ ​a​j​o​u​t​e​r​ ​d​e​s​ ​b​o​u​t​o​n​s​ ​(​A​p​p​e​l​e​r​,​ ​R​é​s​e​r​v​e​r​.​.​.​)​.
						 */
						subtitle: string
					}
					good_posting_habits: {
						/**
						 * B​r​a​v​o​ ​p​o​u​r​ ​v​o​s​ ​p​o​s​t​s​ ​G​o​o​g​l​e
						 */
						title: string
						/**
						 * P​u​b​l​i​e​r​ ​r​é​g​u​l​i​è​r​e​m​e​n​t​ ​a​v​e​c​ ​v​o​s​ ​m​o​t​s​-​c​l​é​s​ ​c​o​n​t​r​i​b​u​e​ ​à​ ​a​m​é​l​i​o​r​e​r​ ​v​o​t​r​e​ ​r​é​f​é​r​e​n​c​e​m​e​n​t​.
						 */
						subtitle: string
					}
				}
			}
		}
	}
	store_locator: {
		faq_block: {
			/**
			 * F​o​i​r​e​ ​a​u​x​ ​q​u​e​s​t​i​o​n​s
			 */
			title: string
		}
		ctas: {
			/**
			 * C​o​m​m​a​n​d​e​r
			 */
			order: string
			/**
			 * R​é​s​e​r​v​e​r​ ​u​n​e​ ​t​a​b​l​e
			 */
			reservation: string
			/**
			 * M​e​n​u
			 */
			menu: string
			/**
			 * S​i​t​e​ ​W​e​b
			 */
			website: string
			/**
			 * I​t​i​n​é​r​a​i​r​e
			 */
			itinerary: string
		}
		information_block: {
			hours: {
				/**
				 * F​e​r​m​é
				 */
				closed: string
			}
			payment_method: {
				/**
				 * C​a​r​t​e​s​ ​b​l​e​u​e​s
				 */
				card: string
				/**
				 * E​s​p​è​c​e​s
				 */
				cash: string
				/**
				 * E​s​p​è​c​e​s​ ​s​e​u​l​e​m​e​n​t
				 */
				cash_only: string
				/**
				 * T​i​t​r​e​s​ ​r​e​s​t​a​u​r​a​n​t​s
				 */
				meal_coupons: string
				/**
				 * S​o​d​e​x​o
				 */
				sodexo_meal_voucher: string
				/**
				 * N​F​C
				 */
				nfc: string
				/**
				 * C​h​è​q​u​e​s
				 */
				check: string
			}
		}
	}
	enums: {
		days: {
			/**
			 * V​e​n​d​r​e​d​i
			 */
			friday: string
			/**
			 * L​u​n​d​i
			 */
			monday: string
			/**
			 * S​a​m​e​d​i
			 */
			saturday: string
			/**
			 * D​i​m​a​n​c​h​e
			 */
			sunday: string
			/**
			 * J​e​u​d​i
			 */
			thursday: string
			/**
			 * M​a​r​d​i
			 */
			tuesday: string
			/**
			 * M​e​r​c​r​e​d​i
			 */
			wednesday: string
		}
	}
}

export type TranslationFunctions = {
	apple_business_connect: {
		special_hours: {
			/**
			 * Fermeture exceptionnelle
			 */
			closed: () => LocalizedString
			/**
			 * Ouverture exceptionnelle
			 */
			open: () => LocalizedString
		}
	}
	automations: {
		intelligent_subjects: {
			reviews: {
				/**
				 * Urgent: Sujet sensible détecté dans un avis
				 */
				subject: () => LocalizedString
			}
		}
	}
	common: {
		/**
		 * Vos établissements
		 */
		your_locations: () => LocalizedString
		/**
		 * L'équipe Malou {env}
		 */
		malouTeam: (arg: { env: string }) => LocalizedString
	}
	notifications: {
		/**
		 * {rating}⭐ {restaurantName}
		 */
		new_review: (arg: { rating: string, restaurantName: string }) => LocalizedString
		/**
		 * Vous a mentionné dans une story
		 */
		mentioned_in_story: () => LocalizedString
		/**
		 * Vous a envoyé un fichier
		 */
		sent_attachment: () => LocalizedString
		/**
		 * Vous a envoyé une photo
		 */
		sent_picture: () => LocalizedString
		/**
		 * Vous a envoyé une vidéo
		 */
		sent_video: () => LocalizedString
		/**
		 * Psst {unreadMessagesCount} {{nouveau message|nouveaux messages}}
		 */
		daily_unread_messages: (arg: { unreadMessagesCount: number }) => LocalizedString
		/**
		 * Vous avez {unansweredReviewsCount} avis négatifs sans réponse
		 */
		daily_unanswered_reviews: (arg: { unansweredReviewsCount: number }) => LocalizedString
		/**
		 * ✍️ N'oubliez pas de poster
		 */
		daily_no_more_scheduled_posts: () => LocalizedString
		/**
		 * N'oubliez pas d'y répondre depuis la Malouapp.
		 */
		daily_unread_messages_text: () => LocalizedString
		/**
		 * Ne laissez pas vos clients sans réponse, rendez-vous sur la Malouapp.
		 */
		daily_unanswered_reviews_text: () => LocalizedString
		/**
		 * Vous n'avez plus de posts programmés pour {restaurantName}
		 */
		daily_no_more_scheduled_posts_text: (arg: { restaurantName: string }) => LocalizedString
		/**
		 * Erreur de publication
		 */
		error_publishing_post: () => LocalizedString
		/**
		 * Votre post n'a pas pu être publié sur {restaurantName}.
		 */
		error_publishing_post_text: (arg: { restaurantName: string }) => LocalizedString
		reviews: {
			negative_review_reply_reminder: {
				/**
				 * {receiverName}, vous avez {unansweredReviewsCount} avis négatifs en attente de réponse
				 */
				subject: (arg: { receiverName: string, unansweredReviewsCount: number }) => LocalizedString
				/**
				 * {receiverName}, n’oubliez pas de répondre à cet avis négatif
				 */
				subject_single: (arg: { receiverName: string }) => LocalizedString
				/**
				 * Vous avez {unansweredReviewsCount} avis négatifs en attente de réponse
				 */
				subject_without_name: (arg: { unansweredReviewsCount: number }) => LocalizedString
				/**
				 * N’oubliez pas de répondre à cet avis négatif
				 */
				subject_single_without_name: () => LocalizedString
			}
		}
		special_hour: {
			/**
			 * {receiverName}, indiquez à vos clients si vous êtes ouvert le {date}
			 */
			subject: (arg: { date: string, receiverName: string }) => LocalizedString
		}
		post_suggestion: {
			/**
			 * {eventName}Un évènement approche, n'oubliez pas d'avertir vos clients !
			 */
			subject: (arg: { eventName: string }) => LocalizedString
		}
		roi: {
			/**
			 * Les estimations de vos gains marketing sont disponibles !
			 */
			activated_subject: () => LocalizedString
		}
		summary: {
			/**
			 * {receiverName}, vous avez {notificationsCount} notification{{s}} en attente
			 */
			subject: (arg: { notificationsCount: number, receiverName: string }) => LocalizedString
			/**
			 * Vous avez {notificationsCount} notification{{s}} en attente
			 */
			subject_without_name: (arg: { notificationsCount: number }) => LocalizedString
		}
	}
	gallery: {
		/**
		 * Copie de
		 */
		copy_of: () => LocalizedString
	}
	mailing: {
		ai: {
			api_hard_limit: {
				/**
				 * Vous avez atteint votre nombre de crédits IA pour ce mois !
				 */
				subject: () => LocalizedString
				/**
				 * Bonjour,
				 */
				greetings: () => LocalizedString
				/**
				 * Vous disposez de 500 utilisations de l'IA par mois pour votre établissement <b>{restaurantName}</b> ! Si vous avez besoin de plus de crédits, discutez-en avec votre contact privilégié chez Malou.
				 */
				content: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * À très bientôt !
				 */
				see_you: () => LocalizedString
			}
		}
		feedback: {
			closed_feedback: {
				/**
				 * Une discussion a été fermée pour {restaurantName}
				 */
				subject: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * Bonjour {receiver},
				 */
				greetings: (arg: { receiver: string }) => LocalizedString
				/**
				 * {user} a fermé la discussion d'un post sur la MalouApp.
				 */
				content1: (arg: { user: string }) => LocalizedString
				/**
				 * Pour consulter le post, cliquez <a ses:no-track href="{link}"><b>ici</b></a>.
				 */
				content2: (arg: { link: string }) => LocalizedString
				/**
				 * Vous ne voulez plus recevoir de mail concernant les retours ?
				 */
				dont_receive_feedback_mail: () => LocalizedString
				/**
				 * Désabonnez-vous
				 */
				unsubscribe: () => LocalizedString
			}
			new_feedback_message: {
				/**
				 * {restaurantName} : une remarque non traitée a été ajoutée
				 */
				subject: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * Vous avez une notification de la MalouApp
				 */
				title: () => LocalizedString
				/**
				 * Bonjour {receiver},
				 */
				greetings: (arg: { receiver: string }) => LocalizedString
				/**
				 * {user} a commenté un post de la MalouApp.
				 */
				content: (arg: { user: string }) => LocalizedString
				/**
				 * Consulter le retour
				 */
				button: () => LocalizedString
				/**
				 * Se désinscrire
				 */
				unsubscribe: () => LocalizedString
				post_status: {
					/**
					 * publié
					 */
					published: () => LocalizedString
					/**
					 * programmé
					 */
					scheduled: () => LocalizedString
					/**
					 * en brouillon
					 */
					draft: () => LocalizedString
					/**
					 * en erreur
					 */
					error: () => LocalizedString
				}
			}
			opened_feedback: {
				/**
				 * Une discussion a été réouverte pour {restaurantName}
				 */
				subject: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * Bonjour {receiver},
				 */
				greetings: (arg: { receiver: string }) => LocalizedString
				/**
				 * {user} a réouvert la discussion d'un post sur la MalouApp.
				 */
				content1: (arg: { user: string }) => LocalizedString
				/**
				 * Pour consulter le post, cliquez <a ses:no-track href="{link}"><b>ici</b></a>.
				 */
				content2: (arg: { link: string }) => LocalizedString
				/**
				 * Vous ne voulez plus recevoir de mail concernant les retours ?
				 */
				dont_receive_feedback_mail: () => LocalizedString
				/**
				 * Désabonnez-vous
				 */
				unsubscribe: () => LocalizedString
			}
		}
		mobile_app: {
			download_mobile_app: {
				/**
				 * Téléchargez l'application mobile MalouApp
				 */
				subject: () => LocalizedString
				/**
				 * Téléchargez l'application mobile MalouApp
				 */
				title: () => LocalizedString
				/**
				 * Bonjour {receiver},
				 */
				greetings: (arg: { receiver: string }) => LocalizedString
				/**
				 * Bravo, vous avez reçu vos premiers avis sur la MalouApp ! Saviez-vous qu'elle existe en application mobile ?
				 */
				content: () => LocalizedString
				/**
				 * Téléchargez l'application mobile Malou pour répondre plus rapidement à vos clients
				 */
				download_app: () => LocalizedString
			}
		}
		permissions: {
			connection_revoked: {
				/**
				 * Bonjour,
				 */
				greetings: () => LocalizedString
				/**
				 * Il semble que la connexion Facebook et/ou Instagram sur l'établissement <b>{restaurantName}</b> soit rompue.
				 */
				content1: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * Les fonctionnalités liées à ces plateformes risquent de ne plus fonctionner correctement.
				 */
				content2: () => LocalizedString
				/**
				 * Essayez de vous reconnecter depuis la page des plateformes <a ses:no-track href="{link}"><b>ici</b></a>
				 */
				content3: (arg: { link: string }) => LocalizedString
				/**
				 * Connexion à {platformName} rompue sur {restaurantName}
				 */
				subject: (arg: { platformName: string, restaurantName: string }) => LocalizedString
			}
		}
		platforms: {
			mapstr_reminder: {
				/**
				 * Finalisez votre connexion Mapstr Premium x Malou
				 */
				subject: () => LocalizedString
			}
		}
		posts: {
			expired_location: {
				/**
				 * Information concernant votre dernier post programmé
				 */
				subject: () => LocalizedString
				/**
				 * Bonjour {receiver},
				 */
				greetings: (arg: { receiver: string }) => LocalizedString
				/**
				 * Vous aviez programmé un post pour le {publicationDate} sur {platformName}.
				 */
				content1: (arg: { platformName: string, publicationDate: string }) => LocalizedString
				/**
				 * Le post a bien été publié, cependant le lieu que vous aviez tagué sur le post était introuvable, le post a par conséquent été publié sans lieu associé : voir sur la <a ses:no-track href="{restaurantPostsLink}"><b>MalouApp</b></a>. Nous recommandons de taguer des lieux sur vos posts afin d'améliorer votre référencement local.
				 */
				content2: (arg: { restaurantPostsLink: string }) => LocalizedString
				/**
				 * Rendez-vous sur la <a ses:no-track href="{restaurantPostsLink}"><b>MalouApp</b></a> pour vérifier vos prochains posts programmés.
				 */
				content3: (arg: { restaurantPostsLink: string }) => LocalizedString
				/**
				 * À très bientôt !
				 */
				seeYou: () => LocalizedString
			}
			publication_failed: {
				/**
				 * Votre {postKind|{post:post, story:story}} n'a pas pu être {postKind|{post:publié, story:publiée}} sur {restaurantName}
				 */
				subject: (arg: { postKind: 'post' | 'story', restaurantName: string }) => LocalizedString
				error: {
					/**
					 * {postKind|{post:Ce post a été refusé car il, story:Cette story a été refusée car elle}} ne contient pas de média.
					 */
					no_media: (arg: { postKind: 'post' | 'story' }) => LocalizedString
					/**
					 * Votre connexion à Facebook a expiré.
					 */
					invalid_token: () => LocalizedString
					/**
					 * Un compte Instagram Business est requis pour pouvoir poster. Veuillez modifier vos paramètres Instagram.
					 */
					invalid_instagram_account: () => LocalizedString
					/**
					 * Votre compte Instagram est restreint. Veuillez suivre les instructions sur votre application Instagram mobile.
					 */
					restricted_instagram_account: () => LocalizedString
					/**
					 * Le service était temporairement indisponible lors de l'envoi.
					 */
					service_unavailable: () => LocalizedString
				}
				cta: {
					/**
					 * Voir {postKind|{post:mon post, story:ma story}}
					 */
					'default': (arg: { postKind: 'post' | 'story' }) => LocalizedString
					/**
					 * Ajouter un média
					 */
					add_media: () => LocalizedString
					/**
					 * Se reconnecter
					 */
					reconnect: () => LocalizedString
				}
			}
		}
		reports: {
			daily_report: {
				/**
				 * Rapport quotidien des avis du {formattedDate}
				 */
				subject: (arg: { formattedDate: string }) => LocalizedString
				/**
				 * Votre rapport quotidien est arrivé !
				 */
				title: () => LocalizedString
				/**
				 * Bonjour,
				 */
				greetings: () => LocalizedString
				/**
				 * Veuillez trouver en pièce jointe votre rapport quotidien du {formattedDate}.
				 */
				content: (arg: { formattedDate: string }) => LocalizedString
				/**
				 * À très bientôt !
				 */
				see_you: () => LocalizedString
			}
			daily_report_pdf: {
				/**
				 * Avis
				 */
				reviews: () => LocalizedString
				/**
				 * avis
				 */
				reviews_lowercase: () => LocalizedString
				/**
				 * (dont {lowRatingsCount} avis de 1, 2 ou 3 étoiles)
				 */
				low_ratings_count: (arg: { lowRatingsCount: number }) => LocalizedString
				/**
				 * Moyenne de vos avis sur toutes vos plateformes
				 */
				average_rating_all_platforms: () => LocalizedString
				/**
				 * <span class="text-secondary text-18 text-regular">Zoom sur</span><span class="text-secondary text-18 text-bold">vos avis</span>
				 */
				down_to_the_details: () => LocalizedString
				/**
				 * Note moyenne
				 */
				average_rating: () => LocalizedString
			}
		}
		review_booster: {
			verification_email: {
				/**
				 * Vérification de l'adresse email d'envoi
				 */
				subject: () => LocalizedString
				/**
				 * Vous avez une notification de la MalouApp
				 */
				title: () => LocalizedString
				/**
				 * Bonjour,
				 */
				greetings: () => LocalizedString
				/**
				 * Veuillez cliquer sur le lien ci-dessous pour valider votre adresse email pour l'envoi de votre campagne sur la MalouApp.
				 */
				content: () => LocalizedString
			}
		}
		user_email: {
			confirm_create_account: {
				/**
				 * Nouveau compte Malou
				 */
				subject: () => LocalizedString
			}
			reset_password: {
				/**
				 * Changement d'identifiants
				 */
				subject: () => LocalizedString
				/**
				 * Vous avez une notification de la MalouApp
				 */
				title: () => LocalizedString
				/**
				 * Bonjour {receiver},
				 */
				greetings: (arg: { receiver: string }) => LocalizedString
				/**
				 * Vous avez demandé à changer votre mot de passe.
				 */
				content1: () => LocalizedString
				/**
				 * Cliquez sur ce lien afin de saisir votre nouveau mot de passe :
				 */
				content2: () => LocalizedString
				/**
				 * <a ses:no-track href="{url}">{url}</a>
				 */
				link: (arg: { url: string }) => LocalizedString
				/**
				 * À très bientôt !
				 */
				see_you: () => LocalizedString
			}
			wrong_platform_access: {
				/**
				 * MalouApp - Accès erronés
				 */
				subject: () => LocalizedString
				/**
				 * Vous avez une notification de la MalouApp
				 */
				title: () => LocalizedString
				/**
				 * Bonjour,
				 */
				greetings: () => LocalizedString
				/**
				 * Les accès que vous avez renseignés pour la connexion à <span {{{style "text-secondary text-14"}}}>{platformName}</span> n'ont pas pu être vérifiés.
				 */
				content1: (arg: { platformName: string }) => LocalizedString
				/**
				 * Renseigner les bons accès <a ses:no-track href="{url}">ici</a>.
				 */
				content2: (arg: { url: string }) => LocalizedString
				/**
				 * Merci et nous restons toujours disponibles si besoin !
				 */
				thanks: () => LocalizedString
				/**
				 * À très bientôt !
				 */
				see_you: () => LocalizedString
			}
		}
		wheels_of_fortune: {
			empty_stock: {
				/**
				 * {restaurantName} : Votre stock de {giftName} est épuisé !
				 */
				subject: (arg: { giftName: string, restaurantName: string }) => LocalizedString
			}
			gift_expires_soon: {
				/**
				 * {restaurantName} : Votre coupon cadeau pour {gift} expire bientôt !
				 */
				subject: (arg: { gift: string, restaurantName: string }) => LocalizedString
			}
			retrieve_gift: {
				/**
				 * {businessName} vous offre un cadeau !
				 */
				subject: (arg: { businessName: string }) => LocalizedString
			}
			wof_live_tomorrow: {
				/**
				 * Votre roue de la fortune sera active demain !
				 */
				subject: () => LocalizedString
			}
		}
		diagnostic: {
			report: {
				/**
				 * {name}, votre diagnostic de visibilité est disponible !
				 */
				subject: (arg: { name: string }) => LocalizedString
			}
		}
	}
	reports: {
		reviews_report: {
			daily: {
				/**
				 * Rapport-Quotidien-Avis
				 */
				title: () => LocalizedString
				subject: {
					/**
					 * ⭐️ Vous avez reçu {count} avis hier sur {restaurantName}
					 */
					one: (arg: { count: number, restaurantName: string }) => LocalizedString
					/**
					 * ⭐️ Vous avez reçu {count} avis hier sur {restaurantName}
					 */
					many: (arg: { count: number, restaurantName: string }) => LocalizedString
				}
			}
			weekly: {
				/**
				 * Rapport-Hebdomadaire-Avis
				 */
				title: () => LocalizedString
				/**
				 * ⭐️ {rating}/5 de moyenne sur vos avis reçus la semaine dernière sur {restaurantName}
				 */
				subject: (arg: { rating: number, restaurantName: string }) => LocalizedString
				/**
				 * ⭐️ Vous n'avez pas reçu d'avis la semaine dernière sur {restaurantName}
				 */
				subject_no_reviews: (arg: { restaurantName: string }) => LocalizedString
				/**
				 * ⭐️ Regarder vos avis reçus la semaine dernière sur {restaurantName}
				 */
				subject_no_average_rating: (arg: { restaurantName: string }) => LocalizedString
			}
		}
		performance_report: {
			monthly: {
				/**
				 * Rapport-Mensuel-Performance
				 */
				title: () => LocalizedString
				/**
				 * 🚀 Malou analyse votre visibilité sur {month} {year}
				 */
				subject: (arg: { month: string, year: number }) => LocalizedString
			}
			weekly: {
				/**
				 * Rapport-Hebdomadaire-Performance
				 */
				title: () => LocalizedString
				/**
				 * 🚀 Votre récap’ de la semaine avec Malou
				 */
				subject: () => LocalizedString
			}
		}
	}
	recommendations: {
		posts: {
			monthly_performance_report: {
				instagram: {
					/**
					 * N'oubliez pas de poster 2 à 3 fois par semaine sur Instagram.
					 */
					postAtLeastTimesAWeek: () => LocalizedString
					/**
					 * Félicitations pour vos publications régulières sur Instagram ! Nous vous recommandons d'alterner entre les reels et les carrousels.
					 */
					shouldAlternatePostType: () => LocalizedString
					/**
					 * Félicitations pour vos publications régulières sur Instagram !
					 */
					congratsForPosting: () => LocalizedString
					/**
					 * Félicitations pour vos publications régulières sur Instagram ! Veillez à utiliser entre 3 et 6 hashtags par publication.
					 */
					shouldUseGoodHashtagsCount: () => LocalizedString
				}
				gmb: {
					/**
					 * Nous recommandons de poster au moins une fois par semaine sur Google pour améliorer votre référencement.
					 */
					postAtLeastTimesAWeek: () => LocalizedString
					/**
					 * Bravo d'avoir publié régulièrement sur Google ! Vérifiez que le score de mots-clés soit toujours d'au moins 4/5 afin de remonter dans les résultats.
					 */
					shouldIncreaseKeywordsScore: () => LocalizedString
					/**
					 * Félicitations pour vos publications régulières sur  Google!
					 */
					congratsForPosting: () => LocalizedString
					/**
					 * Félicitations, publier régulièrement sur Google avec vos mots-clés contribue à améliorer votre référencement.
					 */
					congratsForPostingWithALotOfCta: () => LocalizedString
				}
			}
			weekly_performance_report: {
				instagram: {
					no_post: {
						/**
						 * Aucun post Instagram programmé pour cette semaine
						 */
						title: () => LocalizedString
						/**
						 * Pour une meilleure visibilité, postez 2 à 3 fois par semaine sur Instagram.
						 */
						subtitle: () => LocalizedString
					}
					only_one_post: {
						/**
						 * 1 seul post Instagram programmé pour cette semaine
						 */
						title: () => LocalizedString
						/**
						 * Pour une meilleure visibilité, postez 2 à 3 fois par semaine sur Instagram.
						 */
						subtitle: () => LocalizedString
					}
					should_alternate_post_type: {
						/**
						 * Pensez à diversifier vos posts
						 */
						title: () => LocalizedString
						/**
						 * Nous vous recommandons de varier régulièrement entre réels et carrousels.
						 */
						subtitle: () => LocalizedString
					}
					bad_hashtags_score: {
						/**
						 * Vous avez {count} posts programmés pour cette semaine
						 */
						title: (arg: { count: number }) => LocalizedString
						/**
						 * Attention toutefois au nombre de hashtags que vous utilisez ; nous en recommandons entre 3 et 6.
						 */
						subtitle: () => LocalizedString
					}
					good_posting_habits: {
						/**
						 * Vous avez {count} posts programmés pour cette semaine
						 */
						title: (arg: { count: number }) => LocalizedString
						/**
						 * Vous programmez à la fois des réels et des carrousels, tout en y ajoutant des hashtags : bravo !
						 */
						subtitle: () => LocalizedString
					}
				}
				gmb: {
					no_post: {
						/**
						 * Aucun post Google programmé pour cette semaine
						 */
						title: () => LocalizedString
						/**
						 * Pour une meilleure visibilité, postez au moins 1 fois par semaine sur Google.
						 */
						subtitle: () => LocalizedString
					}
					low_average_keywords_score: {
						/**
						 * Attention à l’utilisation de vos mots-clés
						 */
						title: () => LocalizedString
						/**
						 * Vérifiez que le score de mots-clés de vos posts Google soit d’au moins 4/5 afin de remonter dans les résultats.
						 */
						subtitle: () => LocalizedString
					}
					low_cta_rate: {
						/**
						 * N’oubliez pas d’ajouter des boutons à vos posts
						 */
						title: () => LocalizedString
						/**
						 * Bravo d’avoir pensé à programmer vos posts Google ! Nous vous conseillons d’y ajouter des boutons (Appeler, Réserver...).
						 */
						subtitle: () => LocalizedString
					}
					good_posting_habits: {
						/**
						 * Bravo pour vos posts Google
						 */
						title: () => LocalizedString
						/**
						 * Publier régulièrement avec vos mots-clés contribue à améliorer votre référencement.
						 */
						subtitle: () => LocalizedString
					}
				}
			}
		}
	}
	store_locator: {
		faq_block: {
			/**
			 * Foire aux questions
			 */
			title: () => LocalizedString
		}
		ctas: {
			/**
			 * Commander
			 */
			order: () => LocalizedString
			/**
			 * Réserver une table
			 */
			reservation: () => LocalizedString
			/**
			 * Menu
			 */
			menu: () => LocalizedString
			/**
			 * Site Web
			 */
			website: () => LocalizedString
			/**
			 * Itinéraire
			 */
			itinerary: () => LocalizedString
		}
		information_block: {
			hours: {
				/**
				 * Fermé
				 */
				closed: () => LocalizedString
			}
			payment_method: {
				/**
				 * Cartes bleues
				 */
				card: () => LocalizedString
				/**
				 * Espèces
				 */
				cash: () => LocalizedString
				/**
				 * Espèces seulement
				 */
				cash_only: () => LocalizedString
				/**
				 * Titres restaurants
				 */
				meal_coupons: () => LocalizedString
				/**
				 * Sodexo
				 */
				sodexo_meal_voucher: () => LocalizedString
				/**
				 * NFC
				 */
				nfc: () => LocalizedString
				/**
				 * Chèques
				 */
				check: () => LocalizedString
			}
		}
	}
	enums: {
		days: {
			/**
			 * Vendredi
			 */
			friday: () => LocalizedString
			/**
			 * Lundi
			 */
			monday: () => LocalizedString
			/**
			 * Samedi
			 */
			saturday: () => LocalizedString
			/**
			 * Dimanche
			 */
			sunday: () => LocalizedString
			/**
			 * Jeudi
			 */
			thursday: () => LocalizedString
			/**
			 * Mardi
			 */
			tuesday: () => LocalizedString
			/**
			 * Mercredi
			 */
			wednesday: () => LocalizedString
		}
	}
}

export type Formatters = {}
