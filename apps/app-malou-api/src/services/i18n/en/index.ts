import type { Translation } from '../i18n-types';

const en: Translation = {
    apple_business_connect: {
        special_hours: {
            closed: 'Special Closed Hours',
            open: 'Special Open Hours',
        },
    },
    automations: {
        intelligent_subjects: {
            reviews: {
                subject: 'Urgent: Risky topic detected in a review',
            },
        },
    },
    common: {
        your_locations: 'Your locations',
        malouTeam: 'Malou team {env}',
    },
    notifications: {
        mentioned_in_story: 'You were mentioned in a story',
        sent_attachment: 'Sent you an attachment',
        sent_picture: 'Sent you a picture',
        sent_video: 'Sent you a video',
        daily_unread_messages: 'Psst {unreadMessagesCount} {{new message|new messages}}',
        daily_unanswered_reviews: 'You have {unansweredReviewsCount} negative reviews waiting for a response',
        new_review: '{rating}⭐ {restaurantName}',
        daily_no_more_scheduled_posts: "✍️ Don't forget to post",
        daily_unread_messages_text: 'Don`t forget to respond to them from the Malouapp.',
        daily_unanswered_reviews_text: "Don't leave your customers without a response...respond on MalouApp!",
        daily_no_more_scheduled_posts_text: 'You have no more scheduled posts for {restaurantName}',
        error_publishing_post: 'Error publishing post',
        error_publishing_post_text: 'Your post could not be published on {restaurantName}.',
        reviews: {
            negative_review_reply_reminder: {
                subject: '{receiverName}, you have {unansweredReviewsCount} negative reviews waiting to be replied',
                subject_single: "{receiverName}, don't forget to answer this negative review",
                subject_without_name: '{unansweredReviewsCount} negative reviews waiting to be replied',
                subject_single_without_name: "Don't forget to answer this negative review",
            },
        },
        special_hour: {
            subject: "{receiverName}, let your customers know if you're open on {date}",
        },
        post_suggestion: {
            subject: '{eventName} is coming! ',
        },
        roi: {
            activated_subject: 'Discover the impact of your marketing efforts with Malou',
        },
        summary: {
            subject: '{receiverName}, you have {notificationsCount} pending notification{{s}}',
            subject_without_name: 'You have {notificationsCount} pending notification{{s}}',
        },
    },
    gallery: {
        copy_of: 'Copy of',
    },
    mailing: {
        ai: {
            api_hard_limit: {
                subject: 'Your monthly AI credits limit has been reached!',
                greetings: 'Hi,',
                content:
                    'Your location <b>{restaurantName}</b> is granted 500 AI credits each month. If you need any more credits, please reach your Malou contact.',
                see_you: 'Looking forward to seeing you soon!',
            },
        },
        feedback: {
            closed_feedback: {
                subject: 'A discussion has been closed for {restaurantName}',
                greetings: 'Hi {receiver},',
                content1: '{user} closed a discussion on a post.',
                content2: 'Click <a ses:no-track href="{link}"><b>here</b></a> to go to the post.',
                dont_receive_feedback_mail: 'Do you no longer want to receive emails regarding returns?',
                unsubscribe: 'Unsubscribe',
            },
            new_feedback_message: {
                subject: '{restaurantName}: a new feedback has been added on a post',
                title: 'You have a MalouApp notification',
                greetings: 'Hi {receiver},',
                content: '{user} added a new feedback to a post.',
                button: 'View feedback',
                unsubscribe: 'Unsubscribe',
                post_status: {
                    published: 'published',
                    scheduled: 'scheduled',
                    draft: 'in draft',
                    error: 'in error',
                },
            },
            opened_feedback: {
                subject: 'A discussion has been closed for {restaurantName}',
                greetings: 'Hi {receiver},',
                content1: '{user} reopened a discussion on a post.',
                content2: 'Click <a ses:no-track href="{link}"><b>here</b></a> to go to the post.',
                dont_receive_feedback_mail: 'Do you no longer want to receive emails regarding returns?',
                unsubscribe: 'Unsubscribe',
            },
        },
        mobile_app: {
            download_mobile_app: {
                subject: 'Download Malou Mobile App',
                title: 'Download Malou Mobile App',
                greetings: 'Hi {receiver},',
                content: 'Congrats, you received your first reviews on the MalouApp! Did you know we also have a mobile app?',
                download_app: 'Download the Malou mobile app to quickly answer to your customers',
            },
        },
        permissions: {
            connection_revoked: {
                greetings: 'Hi,',
                content1: 'It seems that the Facebook and/or Instagram connection on the <b>{restaurantName}</b> establishment is broken.',
                content2: 'The functionalities linked to these platforms may no longer work correctly.',
                content3: 'Try reconnecting from the platforms page <a ses:no-track href="{link}"><b>here</b></a>',
                subject: 'Issue with your {platformName} connection on {restaurantName}',
            },
        },
        platforms: {
            mapstr_reminder: {
                subject: 'Finalize your Mapstr Premium x Malou connection',
            },
        },
        posts: {
            expired_location: {
                subject: 'About your last publication',
                greetings: 'Hi {receiver},',
                content1: 'You had scheduled a post on {platformName} on {publicationDate}.',
                content2:
                    'Your post was successfully posted, however the location you chose could not be found. The post was thus published without any location: see post on <a ses:no-track href="{restaurantPostsLink}"><b>MalouApp</b></a>. We recommend that you add locations on your posts for a better SEO.',
                content3: 'You can go on <a ses:no-track href="{restaurantPostsLink}"><b>MalouApp</b></a> to check your programmed posts.',
                seeYou: 'Looking forward to seeing you soon!',
            },
            publication_failed: {
                subject: 'Your {postKind|{post:post, story:story}} could not be published on {restaurantName}',
                error: {
                    no_media: 'This {postKind|{post:post, story:story}} has been rejected because it does not contain any media.',
                    invalid_token: 'Your Facebook connection has expired.',
                    invalid_instagram_account: 'An Instagram Business account is required to post. Please change your Instagram settings.',
                    restricted_instagram_account:
                        'Your Instagram account is restricted. Please follow the instructions on your mobile Instagram app.',
                    service_unavailable: 'The service was temporarily unavailable during submission.',
                },
                cta: {
                    default: 'See my {postKind|{post:post, story:story}}',
                    add_media: 'Add media',
                    reconnect: 'Reconnect',
                },
            },
        },
        reports: {
            daily_report: {
                subject: 'Your daily review report for {formattedDate}',
                title: 'Your daily report is here!',
                greetings: 'Hi,',
                content: 'Please find attached your daily review report from {formattedDate}.',
                see_you: 'Looking forward to seeing you soon!',
            },
            daily_report_pdf: {
                reviews: 'Reviews',
                reviews_lowercase: 'reviews',
                low_ratings_count: '({lowRatingsCount} of them are 1, 2 or 3 stars)',
                average_rating_all_platforms: 'Average rating of your reviews on all platforms',
                down_to_the_details: '<span class="text-secondary text-18 text-bold">Down to the details</span>',
                average_rating: 'Average rating',
            },
        },
        review_booster: {
            verification_email: {
                subject: 'Validation of your email',
                title: 'Almost there!',
                greetings: 'Hi,',
                content: 'Please click on the link below to confirm your email address and launch your review campaign on MalouApp.',
            },
        },
        user_email: {
            confirm_create_account: {
                subject: 'New Malou account',
            },
            reset_password: {
                subject: 'Reset your password',
                title: 'You have a MalouApp notification',
                greetings: 'Hi {receiver},',
                content1: 'You made a password reset request.',
                content2: 'Please click on the following link to enter your new password:',
                link: '<a ses:no-track href="{url}">{url}</a>',
                see_you: 'Looking forward to seeing you soon!',
            },
            wrong_platform_access: {
                subject: 'MalouApp - Wrong credentials',
                title: 'You have a MalouApp notification',
                greetings: 'Hi,',
                content1:
                    'The credentials you typed for <span {{{style "text-secondary text-14"}}}>{platformName}</span> could not be verified.',
                content2: 'Click <a ses:no-track href="{url}">here</a> to type them again.',
                thanks: 'If you require any assistance, feel free to inform us.',
                see_you: 'Looking forward to seeing you soon!',
            },
        },
        wheels_of_fortune: {
            empty_stock: {
                subject: '{restaurantName}: Your gift {giftName} is out of stock!',
            },
            gift_expires_soon: {
                subject: '{restaurantName}: Your gift voucher for {gift} expires soon',
            },
            retrieve_gift: {
                subject: '{businessName} offers you a gift!',
            },
            wof_live_tomorrow: {
                subject: 'Your wheel of fortune is going live tomorrow!',
            },
        },
        diagnostic: {
            report: {
                subject: '{name}, your visibility diagnosis is available!',
            },
        },
    },
    reports: {
        reviews_report: {
            daily: {
                title: 'Daily-Reviews-Report',
                subject: {
                    one: '⭐️ You received {count} review yesterday on {restaurantName}',
                    many: '⭐️ You received {count} reviews yesterday on {restaurantName}',
                },
            },
            weekly: {
                title: 'Weekly-Reviews-Report',
                subject: '⭐️ {rating}/5 average rating on your reviews received last week on {restaurantName}',
                subject_no_reviews: '⭐️ You did not receive any reviews last week on {restaurantName}',
                subject_no_average_rating: '⭐️ Check your reviews from last week for {restaurantName}',
            },
        },
        performance_report: {
            monthly: {
                title: 'Monthly-Performance-Report',
                subject: '🚀 Malou analyzed your visibility in {month} {year}',
            },
            weekly: {
                title: 'Weekly-Performance-Report',
                subject: '🚀 Your weekly recap with Malou',
            },
        },
    },
    recommendations: {
        posts: {
            monthly_performance_report: {
                instagram: {
                    postAtLeastTimesAWeek: "Don't forget to post 2 to 3 times a week on Instagram.",
                    shouldAlternatePostType:
                        'Congratulations on your regular posts on Instagram! We recommend alternating between reels and carousels.',
                    congratsForPosting: 'Congratulations on your regular posts on Instagram!',
                    shouldUseGoodHashtagsCount:
                        'Congratulations on your regular posts on Instagram! Make sure to use between 3 and 6 hashtags per post.',
                },
                gmb: {
                    postAtLeastTimesAWeek: 'We recommend posting at least once a week on Google to improve your SEO.',
                    shouldIncreaseKeywordsScore:
                        'Great job on posting regularly on Google! Check that the keyword score is always at least 4/5 to rank higher in search results.',
                    congratsForPosting: 'Congratulations on your regular posts on Google!',
                    congratsForPostingWithALotOfCta:
                        'Congratulations, regularly posting on Google with your keywords helps improve your SEO.',
                },
            },
            weekly_performance_report: {
                instagram: {
                    no_post: {
                        title: 'You have no Instagram post scheduled for this week',
                        subtitle: 'We advise you to post 2-3 times a week on Instagram for better visibility.',
                    },
                    only_one_post: {
                        title: 'Only 1 Instagram post scheduled for this week',
                        subtitle: 'We recommend posting 2-3 times a week on Instagram for better visibility.',
                    },
                    should_alternate_post_type: {
                        title: 'Don’t forget to diversify your posts',
                        subtitle: 'We recommend regularly alternating between reels and carousels.',
                    },
                    bad_hashtags_score: {
                        title: 'You have {count} posts scheduled for this week',
                        subtitle: 'Be careful to use between 3 and 6 hashtags per post.',
                    },
                    good_posting_habits: {
                        title: 'You have {count} posts scheduled for this week',
                        subtitle: 'We noticed you planned a mix of reels and carrousels, using various hashtags: congrats!',
                    },
                },
                gmb: {
                    no_post: {
                        title: 'You have no Google post scheduled for this week',
                        subtitle: 'Post at least once a week on Google to improve your SEO.',
                    },
                    low_average_keywords_score: {
                        title: 'Don’t forget to use your keywords',
                        subtitle: 'Always make sure that your average keyword score in your posts is at least 4/5.',
                    },
                    low_cta_rate: {
                        title: 'Don’t forget adding CTAs to your posts',
                        subtitle: 'Congrats for scheduling your Google posts! We advise you to always add a button (Call, Book...).',
                    },
                    good_posting_habits: {
                        title: 'Congrats for your Google posts',
                        subtitle: 'Posting regularly with your keywords improves your SEO.',
                    },
                },
            },
        },
    },
    store_locator: {
        faq_block: {
            title: 'Frequently Asked Questions',
        },
        ctas: {
            order: 'Order',
            reservation: 'Book a table',
            menu: 'Menu',
            website: 'Website',
            itinerary: 'Get directions',
        },
        information_block: {
            hours: {
                closed: 'Closed',
            },
            payment_method: {
                card: 'Credit card',
                cash: 'Cash',
                cash_only: 'Cash only',
                meal_coupons: 'Meal coupons',
                sodexo_meal_voucher: 'Sodexo',
                nfc: 'NFC',
                check: 'Pay check',
            },
        },
    },
    enums: {
        days: {
            friday: 'Friday',
            monday: 'Monday',
            saturday: 'Saturday',
            sunday: 'Sunday',
            thursday: 'Thursday',
            tuesday: 'Tuesday',
            wednesday: 'Wednesday',
        },
    },
} satisfies Translation;

export default en;
