import 'reflect-metadata';

import ':env';

import ':di';
import { autoInjectable, container } from 'tsyringe';

import { toDbId } from '@malou-io/package-models';
import { BusinessCategory } from '@malou-io/package-utils';

import OrganizationsRepository from ':modules/organizations/organizations.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import ':plugins/db';
import { HyperlineCreateCustomerPayload } from ':providers/hyperline/hyperline.interfaces';
import { HyperlineProvider } from ':providers/hyperline/hyperline.provider';

@autoInjectable()
class SyncDevDataTask {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _hyperlineProvider: HyperlineProvider
    ) {}

    async execute() {
        const organizations = await this._organizationsRepository.find({ filter: {}, options: { lean: true } });

        for (const organization of organizations) {
            if (organization.subscriptionsProviderId) {
                continue;
            }
            const orgCustomer = await this._hyperlineProvider.createCustomer({
                name: `dev_group_${organization.name}`,
                external_id: organization._id.toString(),
            });

            await this._organizationsRepository.updateOne({
                filter: { _id: toDbId(organization._id) },
                update: { subscriptionsProviderId: orgCustomer.id },
            });

            const subOrg = await this._hyperlineProvider.createCustomer({
                name: `dev_company_${organization.name}`,
                organisation_id: orgCustomer.id,
            });

            await this._syncRestaurants(organization._id.toString(), subOrg.id);
        }
    }

    private async _syncRestaurants(organizationId: string, hyperlineSubOrgId: string) {
        const restaurants = await this._restaurantsRepository.find({
            filter: { organizationId: toDbId(organizationId) },
            options: { lean: true },
        });

        for (const restaurant of restaurants) {
            const payload: HyperlineCreateCustomerPayload = {
                name: `dev_unknown_type_${restaurant.name}`,
                external_id: restaurant._id.toString(),
                organisation_id: hyperlineSubOrgId,
            };
            if (restaurant.type === BusinessCategory.LOCAL_BUSINESS) {
                payload.custom_properties = { google_place_id: restaurant.placeId };
                payload.name = `dev_location_${restaurant.name}`;
            }
            if (restaurant.type === BusinessCategory.BRAND) {
                payload.custom_properties = { brand_account: true };
                payload.name = `dev_brand_account_${restaurant.name}`;
            }
            const customer = await this._hyperlineProvider.createCustomer(payload);
            await this._restaurantsRepository.updateOne({
                filter: { _id: toDbId(restaurant._id) },
                update: { subscriptionsProviderId: customer.id },
            });
        }
    }
}

const task = container.resolve(SyncDevDataTask);

task.execute()
    .then(() => process.exit(0))
    .catch((error) => {
        console.error('err :>>', error);
        process.exit(1);
    });
