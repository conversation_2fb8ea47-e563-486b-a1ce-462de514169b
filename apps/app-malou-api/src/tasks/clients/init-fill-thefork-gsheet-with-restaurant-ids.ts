import 'reflect-metadata';

import ':env';

import { container, singleton } from 'tsyringe';

import { FillGsheetWithRestaurantIdsUseCase } from ':modules/clients/provider-clients/providers/thefork/use-cases/fill-gsheet-with-restaurant-ids/fill-gsheet-with-restaurant-ids.use-case';
import ':plugins/db';

@singleton()
class InitFillTheforkGsheetWithRestaurantIdsTask {
    constructor(private readonly _fillGsheetWithRestaurantIdsUseCase: FillGsheetWithRestaurantIdsUseCase) {}

    async execute(): Promise<void> {
        await this._fillGsheetWithRestaurantIdsUseCase.execute();
    }
}

const task = container.resolve(InitFillTheforkGsheetWithRestaurantIdsTask);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
