import 'reflect-metadata';

import ':env';

import { partition } from 'lodash';
import { DateTime } from 'luxon';
import { container, singleton } from 'tsyringe';

import { IPost, ReadPreferenceMode, toDbId } from '@malou-io/package-models';
import {
    isNotNil,
    PlatformKey,
    PostInsightEntityType,
    PostPublicationStatus,
    PostType,
    StoredInDBInsightsMetric,
} from '@malou-io/package-utils';

import { CallTiktokApiService } from ':modules/credentials/platforms/tiktok/services/call-tiktok-api.service';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { FacebookPostInsightsMapper } from ':modules/post-insights/v2/platforms/facebook/facebook-post-insights.mapper';
import { InstagramPostInsightMapper } from ':modules/post-insights/v2/platforms/instagram/instagram-post-insights.mapper';
import { MappedPostInsight } from ':modules/post-insights/v2/platforms/interface';
import { TiktokPostInsightsMapper } from ':modules/post-insights/v2/platforms/tiktok/tiktok-post-insights.mapper';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { FacebookPostInsightsApiProvider } from ':providers/meta/facebook/post-insights/facebook-post-insights-api-provider';
import { InstagramPostInsightsApiProvider } from ':providers/meta/instagram/post-insights/instagram-post-insights-api-provider';
import { TiktokProvider } from ':providers/tiktok/tiktok.provider';

@singleton()
class CreatePostInsightsFromPostsTask {
    private readonly PLATFORMS_TO_PROCESS = [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK, PlatformKey.TIKTOK];
    constructor(
        private readonly _postInsightsRepo: PostInsightRepository,
        private readonly _platformRepository: PlatformsRepository,
        private readonly _restaurantRepository: RestaurantsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _facebookPostInsightsApiProvider: FacebookPostInsightsApiProvider,
        private readonly _instagramPostInsightsApiProvider: InstagramPostInsightsApiProvider,
        private readonly _callTiktokApiService: CallTiktokApiService,
        private readonly _tiktokProvider: TiktokProvider,
        private readonly _platformInsightsRepository: PlatformInsightsRepository
    ) {}

    async run(): Promise<void> {
        const start = Date.now();
        const restaurantIds = await this._restaurantRepository.getAllIds();
        const platforms = await this._platformRepository.getPlatformsByRestaurantIdsAndPlatformKeys(
            restaurantIds,
            this.PLATFORMS_TO_PROCESS
        );

        console.log(`[CreatePostInsightsFromPostsTask] - Found ${platforms.length} platforms to process`);
        for (const platform of platforms) {
            console.log(
                `[CreatePostInsightsFromPostsTask] - Processing platform ${platform._id.toString()} (${platform.key}) for restaurant ${platform.restaurantId.toString()}`
            );
            await this._createPostInsightsForPlatform(platform).catch((error) => {
                console.error(
                    `[CreatePostInsightsFromPostsTask] - Error processing platform ${platform._id.toString()} (${platform.key}) for restaurant ${platform.restaurantId.toString()}:`,
                    error
                );
            });
        }
        const end = Date.now();
        console.log(`[CreatePostInsightsFromPostsTask] - Total time: ${(end - start) / 1000} seconds`);
        console.log('[CreatePostInsightsFromPostsTask] - Completed processing all platforms');
    }

    private async _createPostInsightsForPlatform(platform: Platform): Promise<void> {
        const posts = await this._getPlatformPublishedPosts({
            restaurantId: platform.restaurantId.toString(),
            platformKey: platform.key,
        });

        const platformSocialId = platform.socialId;
        if (!platformSocialId) {
            console.warn(`[CreatePostInsightsFromPostsTask] Platform ${platform._id.toString()} has no socialId, skipping.`);
            return;
        }

        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            console.warn(`[CreatePostInsightsFromPostsTask] Platform ${platform._id.toString()} has no credentialId, skipping.`);
            return;
        }

        switch (platform.key) {
            case PlatformKey.FACEBOOK:
                await this._fetchAndCreateFbPostInsights({ posts, platformSocialId, credentialId });
                break;
            case PlatformKey.INSTAGRAM:
                await this._fetchAndCreateIgPostInsights({ posts, platformSocialId, credentialId });
                break;
            case PlatformKey.TIKTOK:
                await this._fetchAndCreateTiktokPostInsights({
                    posts,
                    platformSocialId,
                    restaurantId: platform.restaurantId.toString(),
                });
                break;
            default:
                console.warn(
                    `[CreatePostInsightsFromPostsTask] Platform ${platform._id.toString()} has unsupported key ${platform.key}, skipping.`
                );
                break;
        }
    }

    private async _getPlatformPublishedPosts({
        restaurantId,
        platformKey,
    }: {
        restaurantId: string;
        platformKey: PlatformKey;
    }): Promise<IPost[]> {
        return this._postsRepository.find({
            filter: {
                restaurantId: toDbId(restaurantId),
                key: platformKey,
                published: PostPublicationStatus.PUBLISHED,
                isStory: false,
            },
            options: { lean: true, readPreference: ReadPreferenceMode.SECONDARY_PREFERRED },
        });
    }

    private async _fetchAndCreateFbPostInsights({
        posts,
        platformSocialId,
        credentialId,
    }: {
        posts: IPost[];
        platformSocialId: string;
        credentialId: string;
    }): Promise<void> {
        const _isFacebookReel = (post: IPost) => post.socialLink?.includes('/reel/') && !post.socialId?.includes('_');
        const validPosts = posts.filter((post) => isNotNil(post.socialId) && isNotNil(post.socialCreatedAt));
        const postsCreatedTime = validPosts.map((post) => post.socialCreatedAt).filter(isNotNil);

        const [reelPosts, regularPosts] = partition(validPosts, (post) => _isFacebookReel(post));
        const socialPostIds = regularPosts.map((post) => post.socialId).filter(isNotNil);
        const socialReelIds = reelPosts.map((post) => post.socialId).filter(isNotNil);

        const [livePostInsights, liveReelInsights] = await Promise.all([
            this._facebookPostInsightsApiProvider.getPagePostInsightsByIds({
                pageId: platformSocialId,
                socialPostIds,
                credentialId,
            }),
            this._facebookPostInsightsApiProvider.getPageReelInsightsByIds({
                pageId: platformSocialId,
                socialPostIds: socialReelIds,
                credentialId,
            }),
        ]);

        const { startDate, endDate } = this._getPostsPeriod(postsCreatedTime);
        const followers = await this._getPlatformFollowers({ platformSocialId, platformKey: PlatformKey.FACEBOOK, startDate, endDate });

        const _mapPostToPartialPostInsight = (
            post: IPost,
            entityType: PostInsightEntityType,
            followers: { [isoDate: string]: number } | undefined
        ) => {
            const postSocialCreatedAt = post.socialCreatedAt!;
            const followersCountAtPostTime = FacebookPostInsightsMapper.getFollowersCountAtPostTime(followers, postSocialCreatedAt);
            return {
                socialId: post.socialId!,
                platformKey: PlatformKey.FACEBOOK,
                entityType,
                postSocialCreatedAt,
                platformSocialId,
                followersCountAtPostTime,
            };
        };

        const postInsights: MappedPostInsight[] = [];
        for (const post of validPosts) {
            const livePostInsight = livePostInsights[post.socialId!];
            const liveReelInsight = liveReelInsights[post.socialId!];
            if (livePostInsight) {
                const mappedPost = _mapPostToPartialPostInsight(post, PostInsightEntityType.POST, followers);
                const mappedData = FacebookPostInsightsMapper.mapToMalouPostInsightData(livePostInsight);
                postInsights.push({ ...mappedPost, data: mappedData, lastFetchedAt: new Date() });
            }

            if (liveReelInsight) {
                const mappedPost = _mapPostToPartialPostInsight(post, PostInsightEntityType.REEL, followers);
                const mappedData = FacebookPostInsightsMapper.mapToMalouReelInsightData(liveReelInsight);
                postInsights.push({ ...mappedPost, data: mappedData, lastFetchedAt: new Date() });
            }
        }

        await this._upsertManyPostInsights(postInsights);
    }

    private async _fetchAndCreateIgPostInsights({
        posts,
        platformSocialId,
        credentialId,
    }: {
        posts: IPost[];
        platformSocialId: string;
        credentialId: string;
    }): Promise<void> {
        const validPosts = posts.filter((post) => isNotNil(post.socialId) && isNotNil(post.socialCreatedAt));
        const postsCreatedTime = validPosts.map((post) => post.socialCreatedAt).filter(isNotNil);
        const socialPostIds = validPosts.map((post) => post.socialId).filter(isNotNil);

        const { startDate, endDate } = this._getPostsPeriod(postsCreatedTime);
        const followers = await this._getPlatformFollowers({ platformSocialId, platformKey: PlatformKey.INSTAGRAM, startDate, endDate });

        const livePostInsights = await this._instagramPostInsightsApiProvider.getPagePostInsightsByIds({
            pageId: platformSocialId,
            socialPostIds,
            credentialId,
        });

        const _mapPostToPartialPostInsight = (post: IPost, followers: { [isoDate: string]: number } | undefined) => {
            const entityType = post.postType === PostType.REEL ? PostInsightEntityType.REEL : PostInsightEntityType.POST;
            const postSocialCreatedAt = post.socialCreatedAt!;
            const followersCountAtPostTime = FacebookPostInsightsMapper.getFollowersCountAtPostTime(followers, postSocialCreatedAt);
            return {
                socialId: post.socialId!,
                platformKey: PlatformKey.INSTAGRAM,
                entityType,
                postSocialCreatedAt,
                platformSocialId,
                followersCountAtPostTime,
            };
        };

        const postInsights: MappedPostInsight[] = [];
        for (const post of validPosts) {
            const livePostInsight = livePostInsights[post.socialId!];
            if (livePostInsight) {
                const entityType = post.postType === PostType.REEL ? PostInsightEntityType.REEL : PostInsightEntityType.POST;
                const mappedData = InstagramPostInsightMapper.mapToMalouPostInsightData(livePostInsight, entityType);
                const mappedPost = _mapPostToPartialPostInsight(post, followers);
                postInsights.push({ ...mappedPost, data: mappedData, lastFetchedAt: new Date() });
            }
        }

        await this._upsertManyPostInsights(postInsights);
    }

    private async _fetchAndCreateTiktokPostInsights({
        posts,
        platformSocialId,
        restaurantId,
    }: {
        posts: IPost[];
        platformSocialId: string;
        restaurantId: string;
    }): Promise<void> {
        const validPosts = posts.filter((post) => isNotNil(post.socialId) && isNotNil(post.socialCreatedAt));
        const postsCreatedTime = validPosts.map((post) => post.socialCreatedAt).filter(isNotNil);
        const socialPostIds = validPosts.map((post) => post.socialId).filter(isNotNil);

        const { startDate, endDate } = this._getPostsPeriod(postsCreatedTime);
        const followers = await this._getPlatformFollowers({ platformSocialId, platformKey: PlatformKey.TIKTOK, startDate, endDate });

        const { data } = await this._callTiktokApiService.execute({
            restaurantId,
            method: this._tiktokProvider.queryVideos,
            args: {
                ids: socialPostIds,
            },
        });

        const livePostInsights = data.videos;

        const postInsights: MappedPostInsight[] = [];
        for (const post of validPosts) {
            const livePostInsight = livePostInsights.find((p) => p.id === post.socialId);
            if (livePostInsight) {
                const mappedPost = TiktokPostInsightsMapper.mapToMalouPostInsight({ post: livePostInsight, platformSocialId, followers });
                postInsights.push({ ...mappedPost, lastFetchedAt: new Date() });
            }
        }
        await this._upsertManyPostInsights(postInsights);
    }

    private async _upsertManyPostInsights(postInsights: MappedPostInsight[]): Promise<void> {
        await this._postInsightsRepo.upsertMany(postInsights).catch((error) => {
            console.error('[CreatePostInsightsFromPostsTask] Error upserting post insights:', {
                error,
                postInsights: postInsights.map((pi) => ({
                    socialId: pi.socialId,
                    platformSocialId: pi.platformSocialId,
                    lastFetchedAt: pi.lastFetchedAt,
                })),
            });
        });
    }

    private async _getPlatformFollowers({
        platformSocialId,
        platformKey,
        startDate,
        endDate,
    }: {
        platformSocialId: string;
        platformKey: PlatformKey;
        startDate: Date;
        endDate: Date;
    }): Promise<{ [isoDate: string]: number } | undefined> {
        const res = await this._platformInsightsRepository.getInsightsGroupedByPlatform({
            socialIds: [platformSocialId],
            metrics: [StoredInDBInsightsMetric.FOLLOWERS],
            platformKeys: [platformKey],
            startDate,
            endDate,
        });
        return res[0]?.insights?.[StoredInDBInsightsMetric.FOLLOWERS];
    }

    private _getPostsPeriod(postsCreatedTime: Date[]): { startDate: Date; endDate: Date } {
        const defaultPeriod = {
            startDate: DateTime.now().minus({ month: 3 }).toJSDate(),
            endDate: DateTime.now().toJSDate(),
        };
        if (!postsCreatedTime?.length) {
            return defaultPeriod;
        }

        const ascSortedDates = postsCreatedTime.sort((a, b) => new Date(a).getTime() - new Date(b).getTime());

        if (ascSortedDates.length === 1) {
            const date = new Date(ascSortedDates[0]);
            return {
                startDate: DateTime.fromJSDate(date).minus({ days: 15 }).toJSDate(),
                endDate: DateTime.fromJSDate(date).plus({ days: 15 }).toJSDate(),
            };
        }

        return {
            startDate: new Date(ascSortedDates[0]),
            endDate: new Date(ascSortedDates[ascSortedDates.length - 1]),
        };
    }
}

const task = container.resolve(CreatePostInsightsFromPostsTask);

task.run()
    .then(() => {
        console.log('CreatePostInsightsFromPostsTask completed successfully.');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error running CreatePostInsightsFromPostsTask:', error);
        process.exit(1);
    });
