import { AnyBulkWriteOperation } from 'mongoose';
import { autoInjectable } from 'tsyringe';

import { isNotNil } from '@malou-io/package-utils';

import { StoriesRepository } from ':modules/stories/repository/stories.repository';

@autoInjectable()
export class FixSortDateTask {
    constructor(private readonly _storiesRepository: StoriesRepository) {}

    async execute() {
        const cursor = this._storiesRepository.model
            .find(
                {
                    isStory: true,
                },
                { _id: 1, sortDate: 1, socialCreatedAt: 1, plannedPublicationDate: 1 },
                { lean: true }
            )
            .cursor();

        await cursor.eachAsync(
            async (stories, i) => {
                console.log('Processing from', i * stories.length, 'to', (i + 1) * stories.length);

                const bulkOperations: AnyBulkWriteOperation[] = stories
                    .map((story) => {
                        const newSortDate = story.socialCreatedAt ?? story.plannedPublicationDate ?? new Date();
                        if (story.sortDate?.getTime() === newSortDate.getTime()) {
                            return null;
                        }
                        return {
                            updateOne: {
                                filter: { _id: story._id },
                                update: { $set: { sortDate: newSortDate } },
                            },
                        };
                    })
                    .filter(isNotNil);

                console.log('Bulk write', bulkOperations.length);
                await this._storiesRepository.model.bulkWrite(bulkOperations);
            },
            { batchSize: 500 }
        );
    }
}
