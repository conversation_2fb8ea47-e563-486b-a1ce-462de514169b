import 'reflect-metadata';

import ':env';

import prompts from 'prompts';
import { container, singleton } from 'tsyringe';

import { ReviewModel } from '@malou-io/package-models';
import { waitFor } from '@malou-io/package-utils';

import { Config } from ':config';
import { GeneratePublicBusinessIdProducer } from ':modules/reviews/queues/generate-public-business-id/generate-public-business-id.producer';
import { GeneratePublicBusinessIdService } from ':modules/reviews/services/generate-public-business-id/generate-public-business-id.service';

@singleton()
export class GenerateMissingPublicBusinessIdsTask {
    private readonly REVIEW_BATCH_SIZE = 500;

    constructor(private readonly _generatePublicBusinessIdService: GeneratePublicBusinessIdService) {}

    async execute() {
        const reviewsWithoutPublicBusinessId = ReviewModel.find(
            {
                publicBusinessId: null,
            },
            { _id: 1 },
            { lean: true }
        ).cursor();

        // Wait for db to be ready in order to not be spammed by logs
        await waitFor(5000);

        const response = await prompts({
            type: 'confirm',
            name: 'value',
            message: `You are going to use the following queue to generate public business id: ${Config.services.sqs.generatePublicBusinessIdFifoQueueUrl},\
				 is it correct ?`,
            initial: false,
        });
        if (!response.value) {
            console.error('Exit process');
            process.exit(1);
        }

        let count = 0;

        // Initialize producer
        const producer = container.resolve(GeneratePublicBusinessIdProducer);
        producer.initialize();

        await reviewsWithoutPublicBusinessId.eachAsync(
            async (reviews) => {
                count += reviews.length;
                console.log(`Starting public business id generation for ${count} reviews`);
                await this._generatePublicBusinessIdService.startGeneratePublicBusinessId(reviews.map((r) => r._id.toString()));
            },
            { batchSize: this.REVIEW_BATCH_SIZE }
        );

        console.log('Done!');
    }
}

const task = container.resolve(GenerateMissingPublicBusinessIdsTask);
task.execute()
    .then(() => process.exit(0))
    .catch((err) => {
        console.error(err);
        process.exit(1);
    });
