export interface HyperlineApiCustomer {
    id: string;
    name: string;
    custom_properties: {
        google_place_id: string | null;
        brand_account: boolean | null;
    };
    external_id: string | null; // malouId
    children?: HyperlineApiCustomer[];
}

export interface HyperlineApiOrganization {
    id: string;
    child_customer_ids: string[];
}

export type HyperlineUpdateCustomerPayload = Partial<HyperlineApiCustomer>;

export interface HyperlineCreateCustomerPayload {
    name: string;
    type?: string;
    external_id?: string;
    organisation_id?: string;
    custom_properties?: {
        google_place_id?: string | null;
        brand_account?: boolean | null;
    };
}
