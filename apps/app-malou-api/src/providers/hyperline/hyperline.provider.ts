import axios, { AxiosInstance } from 'axios';
import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import {
    GetLocationsByOrganizationRequest,
    GetLocationsByOrganizationResponse,
    SubscriptionProviderLocation,
    UpdateSubscriptionsProviderLocationRequest,
} from ':modules/restaurants/services/subscriptions.provider.interfaces';
import {
    HyperlineApiCustomer,
    HyperlineApiOrganization,
    HyperlineCreateCustomerPayload,
    HyperlineUpdateCustomerPayload,
} from ':providers/hyperline/hyperline.interfaces';
import { ProviderMetricsService } from ':providers/provider.metrics.service';

@singleton()
export class HyperlineProvider implements SubscriptionsProvider {
    private _axiosInstance: AxiosInstance;

    constructor(private readonly _providerMetricsService: ProviderMetricsService) {
        this._axiosInstance = axios.create({
            baseURL: Config.vendors.hyperline.baseUrl,
            headers: {
                Authorization: `Bearer ${Config.vendors.hyperline.apiKey}`,
                'Content-Type': 'application/json',
            },
        });
    }

    async getLocationsByOrganization(request: GetLocationsByOrganizationRequest): Promise<GetLocationsByOrganizationResponse> {
        try {
            const parentOrganization = await this._getOrganization(request.organizationProviderId);
            const legalEntities = await Promise.all(
                parentOrganization.child_customer_ids.map((customerId) => this._getOrganization(customerId))
            );
            const locationIds = legalEntities.flatMap((legalEntity) => legalEntity.child_customer_ids);

            const locations: SubscriptionProviderLocation[] = [];

            if (locationIds?.length > 0) {
                const locationPromises = locationIds.map((locationId) => this._getCustomer(locationId));
                const locationsData = await Promise.all(locationPromises);
                locationsData.forEach((location) => {
                    locations.push({
                        id: location.id,
                        name: location.name,
                        placeId: location.custom_properties.google_place_id,
                        malouRestaurantId: location.external_id,
                        isBrandAccount: !!location.custom_properties.brand_account,
                    });
                });
            }

            return {
                locations,
            };
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error fetching locations by organization', {
                organizationProviderId: request.organizationProviderId,
                error: error.message,
            });
            throw error;
        }
    }

    async updateSubscriptionsProviderLocation(request: UpdateSubscriptionsProviderLocationRequest): Promise<void> {
        try {
            const hyperlineUpdatePayload: HyperlineUpdateCustomerPayload =
                this._mapUpdateSubscriptionsProviderLocationRequestToHyperlinePayload(request);
            await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'hyperline',
                requestId: 'api.customer.update',
                request: async () =>
                    this._axiosInstance.put<any>(`/customers/${request.subscriptionsProviderLocationId}`, hyperlineUpdatePayload),
            });
            logger.info('[HYPERLINE_PROVIDER] Customer updated successfully', {
                customerId: request.subscriptionsProviderLocationId,
                malouRestaurantId: request.malouRestaurantId,
            });
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error updating customer', {
                customerId: request.subscriptionsProviderLocationId,
                error: error.message,
                response: error.response?.data,
            });
            throw error;
        }
    }

    async createCustomer(payload: HyperlineCreateCustomerPayload): Promise<HyperlineApiCustomer> {
        try {
            const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
                hostId: 'hyperline',
                requestId: 'api.customer.create',
                request: async () => this._axiosInstance.post<HyperlineApiCustomer>('/customers', payload),
            });
            logger.info('[HYPERLINE_PROVIDER] Customer created successfully', { payload, id: data.id });
            return data;
        } catch (error: any) {
            logger.error('[HYPERLINE_PROVIDER] Error creating customer', {
                error: error.message,
                response: error.response?.data,
                payload,
            });
            throw error;
        }
    }

    private async _getCustomer(customerId: string): Promise<HyperlineApiCustomer> {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'hyperline',
            requestId: 'api.customer.get',
            request: async () => this._axiosInstance.get<HyperlineApiCustomer>(`/customers/${customerId}`),
        });
        return data;
    }

    private async _getOrganization(customerId: string): Promise<HyperlineApiOrganization> {
        const { data } = await this._providerMetricsService.callAndTrackExternalAPI({
            hostId: 'hyperline',
            requestId: 'api.organization.get',
            request: async () => this._axiosInstance.get<HyperlineApiOrganization>(`/organisations/${customerId}`),
        });
        return data;
    }

    private _mapUpdateSubscriptionsProviderLocationRequestToHyperlinePayload(
        request: UpdateSubscriptionsProviderLocationRequest
    ): HyperlineUpdateCustomerPayload {
        const payload = {
            external_id: request.malouRestaurantId ?? null,
        };
        return payload;
    }
}
