import 'reflect-metadata';

import { container } from 'tsyringe';

import { Config } from ':config';
import { InjectionToken } from ':helpers/injection';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import { Cache } from ':plugins/cache';
import { MongoCache } from ':plugins/mongo-cache';
import { RedisCache } from ':plugins/redis-cache';
import { HyperlineProvider } from ':providers/hyperline/hyperline.provider';

container.register<Cache>(InjectionToken.Cache, {
    useClass: Config.cacheImplementation === 'mongo' ? MongoCache : RedisCache,
});

container.register<SubscriptionsProvider>(InjectionToken.SubscriptionsProvider, { useClass: HyperlineProvider });
