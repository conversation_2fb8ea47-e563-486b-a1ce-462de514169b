import { GoogleSpreadsheet } from 'google-spreadsheet';
import { chunk } from 'lodash';
import { singleton } from 'tsyringe';

import { isNotNil, TimeInMilliseconds, waitFor } from '@malou-io/package-utils';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { GoogleSpreadsheetService } from ':microservices/google-spread-sheet.service';
import { TheForkApiProvider } from ':modules/clients/provider-clients/providers/thefork/thefork-api-provider';
import { TheForkClientApiProviderErrorObject } from ':modules/clients/provider-clients/providers/thefork/thefork-client-api-provider.definitions';
import {
    TheForkClient,
    TheForkClientPagination,
    TheForkReservation,
    TheForkReservationPagination,
} from ':modules/clients/provider-clients/providers/thefork/thefork.interfaces';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class TheForkService {
    private _document: GoogleSpreadsheet | null = null;

    constructor(
        private readonly _theForkProvider: TheForkApiProvider,
        private readonly _slackService: SlackService,
        private readonly _googleSpreadsheetService: GoogleSpreadsheetService
    ) {}

    async fetchClient(providerClientId: string): Promise<TheForkClient | null> {
        const result = await this._theForkProvider.getClientById(providerClientId);
        if (result.isErr()) {
            this._handleError(result.error, 'fetchClient');
            return null;
        }
        return result.value;
    }

    async fetchClientList(startDate: Date, endDate: Date, restaurantId: string): Promise<TheForkClient[]> {
        const groupUuid = await this.mapRestaurantIdToTheForkGroupUuid(restaurantId);

        if (!groupUuid) {
            logger.error('[TheForkService] No groupUuid found for restaurant', { restaurantId });
            this._slackService.sendMessage({
                text: `No groupUuid found for restaurant ${restaurantId}`,
                channel: SlackChannel.CRM_ALERTS,
            });
            return [];
        }

        // TheFork clients / customers are grouped by groupUuid not by restaurantUuid
        const clientIds: string[] = [];
        let result: TheForkClientPagination = { totalCount: 0, limit: 0, page: 0, data: [] };
        const limit = 100;
        let page = 1;
        do {
            const theForkResult = await this._theForkProvider.getClientIds({ startDate, endDate, groupUuid, limit, page });
            if (theForkResult.isErr()) {
                this._handleError(theForkResult.error, 'fetchClientList', restaurantId);
                break;
            }
            result = theForkResult.value;
            page++;
            clientIds.push(...result.data);
            // eslint-disable-next-line no-mixed-operators
        } while ((page - 2) * limit + result.data.length < result.totalCount); // First check is with page=2

        const CHUNK_SIZE = 10;
        const chunks = chunk(clientIds, CHUNK_SIZE);
        const ONE_SECOND = 1 * TimeInMilliseconds.SECOND;

        const clients: TheForkClient[] = [];

        for (const clientIdsChunk of chunks) {
            await waitFor(ONE_SECOND);
            const newClients = await Promise.all(clientIdsChunk.map((clientId) => this.fetchClient(clientId)));
            clients.push(...newClients.filter(isNotNil));
        }

        return clients;
    }

    async fetchReservation(reservationId: string): Promise<TheForkReservation | null> {
        const result = await this._theForkProvider.getReservation(reservationId);
        if (result.isErr()) {
            this._handleError(result.error, 'fetchReservation');
            return null;
        }
        return result.value;
    }

    async fetchReservationList(startDate: Date, endDate: Date, restaurantId: string): Promise<TheForkReservation[]> {
        const restaurantUuid = await this.mapRestaurantIdToTheForkRestaurantUuid(restaurantId);

        if (!restaurantUuid) {
            logger.error('[TheForkService] No restaurantUuid found for restaurant', { restaurantId });
            this._slackService.sendMessage({
                text: `No restaurantUuid found for restaurant ${restaurantId}`,
                channel: SlackChannel.CRM_ALERTS,
            });
            return [];
        }

        const limit = 100;
        let page = 1;
        const reservationIds: string[] = [];
        let result: TheForkReservationPagination = { totalCount: 0, limit: 0, page: 0, data: [] };
        do {
            const theForkResult = await this._theForkProvider.getReservationIds({
                startDate,
                endDate,
                limit,
                page,
                restaurantUuid,
                groupUuid: undefined,
            });
            if (theForkResult.isErr()) {
                this._handleError(theForkResult.error, 'fetchReservationList', restaurantId);
                break;
            }
            result = theForkResult.value;
            reservationIds.push(...result.data);
            page++;
            // eslint-disable-next-line no-mixed-operators
        } while ((page - 2) * limit + result.data.length < result.totalCount); // First check is with page=2

        const ONE_SECOND = 1 * TimeInMilliseconds.SECOND;
        const CHUNK_SIZE = 10;
        const chunks = chunk(reservationIds, CHUNK_SIZE);

        const reservations: TheForkReservation[] = [];

        for (const reservationIdsChunk of chunks) {
            await waitFor(ONE_SECOND);
            const newReservations = await Promise.all(reservationIdsChunk.map((reservationId) => this.fetchReservation(reservationId)));
            reservations.push(...newReservations.filter(isNotNil));
        }

        return reservations;
    }

    async mapRestaurantIdToTheForkGroupUuid(restaurantId: string): Promise<string | null> {
        const data = await this._fetchGoogleSpreadSheetMappingData();
        const restaurantData = data.find((d) => d.restaurantId === restaurantId);
        return restaurantData?.theForkGroupUuid ?? null;
    }

    async mapRestaurantIdToTheForkRestaurantUuid(restaurantId: string): Promise<string | null> {
        const data = await this._fetchGoogleSpreadSheetMappingData();
        const restaurantData = data.find((d) => d.restaurantId === restaurantId);
        return restaurantData?.theForkRestaurantUuid ?? null;
    }

    async mapTheForkRestaurantUuidToRestaurantIds(restaurantUuid: string): Promise<string[]> {
        const data = await this._fetchGoogleSpreadSheetMappingData();
        const restaurantData = data.filter((d) => d.theForkRestaurantUuid === restaurantUuid);
        return restaurantData.map((d) => d.restaurantId);
    }

    private _handleError(error: TheForkClientApiProviderErrorObject, method: string, restaurantId?: string): void {
        logger.error('[TheForkService] Error', { error, restaurantId, method });
        const line1 = `THEFORK restaurantId: ${restaurantId}`;
        const line2 = `ErrorCode: ${error.code}  Method: ${method}`;
        const line3 = `\`\`\`${error.stringifiedRawError}\`\`\``;
        const text = `${line1}\n${line2}\n${line3}`;
        this._slackService.sendMessage({ text, channel: SlackChannel.CRM_ALERTS });
    }

    private async _fetchGoogleSpreadSheetMappingData(): Promise<
        { restaurantId: string; theForkRestaurantUuid: string; theForkGroupUuid: string }[]
    > {
        logger.info('[_fetchGoogleSpreadSheetMappingData] Try to access Google Spreadsheet document');
        const privateKeys = Config.businessNotificationsAccountKey;
        const spreadSheetAuth = {
            client_email: privateKeys.client_email as string,
            private_key: privateKeys.private_key?.split('||n||').join('\n'),
        };

        if (this._document) {
            await this._googleSpreadsheetService.reloadGoogleSpreadsheet(this._document, spreadSheetAuth);
        } else {
            this._document = await this._googleSpreadsheetService.getGoogleSpreadsheet(
                '1LCgHMZeq2Ox4MKpskUkb7O0GQhGBLHbenC9Ua6xy_AA',
                spreadSheetAuth
            );
        }

        const sheet = this._document.sheetsByIndex[0];
        logger.info('[_fetchGoogleSpreadSheetMappingData] Google sheet ready');

        const rows = await sheet.getRows();

        const data = rows
            .filter((row) => row['Malou id'] && row['Rs UUID'] && row['Group uuid'])
            .map((row) => ({
                restaurantId: row['Malou id'],
                theForkRestaurantUuid: row['Rs UUID'],
                theForkGroupUuid: row['Group uuid'],
            }));
        return data;
    }
}
