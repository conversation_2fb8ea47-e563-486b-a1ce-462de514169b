import z from 'zod';

export interface TheForkOptins {
    restaurantNewsletter: boolean;
}

// on the website it's only "Homme", "Femme" and "N/A" but the test API returns more values
export enum TheForkCivility {
    MR = 'mr',
    MS = 'ms',
    MRS = 'mrs',
    MX = 'mx',
    COMPANY = 'company',
    HIGHNESS = 'highness',
    AMBASSADOR = 'ambassador',
    MADAM_PRESIDENT = 'madam_president',
    MADAM_AMBASSADOR = 'madam_ambassador',
    XX = 'xx',
    MR_AND_MRS = 'mr_and_mrs',
}

enum TheForkDietaryRestriction {
    GLUTEN_FREE = 'gluten_free',
    KOSHER = 'kosher',
    DAIRY_FREE = 'dairy_free',
    PESCATARIAN = 'pescatarian',
}

const theForkCustomFieldValidator = z.object({
    labelUuid: z.string(),
    label: z.string(),
    type: z.enum(['boolean', 'string', 'integer']),
    value: z.union([z.string(), z.number(), z.boolean()]),
    valueUuid: z.string().nullish(),
});

// https://docs.thefork.io/B2B-API/API%20specifications/get-v-1-customers-id
export const theForkClientValidator = z.object({
    customerUuid: z.string(),
    email: z.string().email().nullable(), // supposed to be required but not the case in the test API
    firstName: z.string().nullable(), // supposed to be required but not the case in the test API
    lastName: z.string().nullable(), // supposed to be required but not the case in the test API
    phone: z.string().nullable(),
    birthDate: z.string().nullable(),
    locale: z.string(), // E.g: nl_NL, en_US, fr_FR, ...
    civility: z.nativeEnum(TheForkCivility).nullable(),
    rank: z.string().nullable(),
    computedRank: z.string().nullable(),
    isVip: z.boolean(),
    address: z.string().nullable(),
    allergiesAndIntolerances: z.array(z.string()).nullable(),
    dietaryRestrictions: z.array(z.nativeEnum(TheForkDietaryRestriction)).nullable(),
    favFood: z.string().nullable(),
    favDrinks: z.string().nullable(),
    favSeating: z.string().nullable(),
    notes: z.string().nullable(),
    originRestaurantUuid: z.string().nullable(),
    originRestaurantName: z.string().nullable(),
    creationDate: z.string(),
    lastUpdateAt: z.string().nullish(),
    isPromoter: z.boolean(),
    secondaryPhone: z.string().nullable(),
    country: z.string().nullable(),
    city: z.string().nullable(),
    zipcode: z.string().nullable(),
    optins: z.object({
        restaurantNewsletter: z.boolean(),
    }),
    customFields: z.array(theForkCustomFieldValidator).nullable(),
});
export type TheForkClient = z.infer<typeof theForkClientValidator>;

export const theForkClientPaginationValidator = z.object({
    totalCount: z.number(),
    limit: z.number(),
    page: z.number(),
    data: z.array(z.string()),
});
export type TheForkClientPagination = z.infer<typeof theForkClientPaginationValidator>;

enum TheForkReservationStatus {
    RECORDER = 'RECORDED',
    CONFIRMED = 'CONFIRMED',
    CANCELED = 'CANCELED',
    NO_SHOW = 'NO_SHOW',
    REQUESTED = 'REQUESTED',
    REFUSED = 'REFUSED',
}

const theForkOfferDetailsValidator = z.object({
    offerType: z.enum(['promotion', 'presetMenu']),
    name: z.any().nullable(), // TODO improve validation, it is an object but neither the documentation nor the test API give more details
    discountPercentage: z.number().nullish(),
    presetMenuType: z.enum(['generic', 'of_the_day', 'brunch', 'group', 'experience']).nullish(),
    price: z.number().nullable(),
    currency: z.string().nullable(),
});

// https://docs.thefork.io/B2B-API/API%20specifications/get-v-1-reservations-id
export const theForkReservationValidator = z.object({
    reservationUuid: z.string(),
    restaurantUuid: z.string(),
    mealDate: z.string().datetime(),
    partySize: z.number(),
    status: z.nativeEnum(TheForkReservationStatus),
    offerUuid: z.string().nullable(),
    customerNote: z.string().nullable(),
    customerUuid: z.string().nullable(),
    customFields: z.array(theForkCustomFieldValidator).nullable(),
    offerDetails: theForkOfferDetailsValidator.nullable(),
    utmTrackingInformation: z.array(z.object({ key: z.string(), value: z.string() })).nullable(),
    billAmount: z
        .object({
            totalPrice: z.number(),
            currency: z.string(),
        })
        .nullable(),
    reservationChannel: z.enum([
        'TripAdvisor',
        'Michelin',
        'Booking Module',
        'Offline',
        'TheFork',
        'Walk-in',
        'Google',
        'Cross Sell',
        'TheFork Manager API',
    ]), // TheFork Manager API is not in the documentation but is returned by the test API
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime().nullable(),
});
export type TheForkReservation = z.infer<typeof theForkReservationValidator>;

// https://docs.thefork.io/B2B-API/API%20specifications/get-v-1-reservations
export const theForkReservationPaginationValidator = z.object({
    totalCount: z.number(),
    limit: z.number(),
    page: z.number(),
    data: z.array(z.string()),
});
export type TheForkReservationPagination = z.infer<typeof theForkReservationPaginationValidator>;
