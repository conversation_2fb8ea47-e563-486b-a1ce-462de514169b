import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import { ProviderVisit } from ':modules/clients/provider-clients/entities/provider-visit.entity';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkEventRequestBody } from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class HandleTheForkUpsertedReservationUseCase {
    constructor(
        private readonly _theForkService: TheForkService,
        private readonly _providerClientsRepository: ProviderClientsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute({ body }: { body: TheForkEventRequestBody }): Promise<void> {
        const theForkReservation = await this._theForkService.fetchReservation(body.uuid);
        if (!theForkReservation) {
            logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedReservationUseCase] - Reservation not found', {
                reservationUuid: body.uuid,
            });
            return;
        }

        if (!theForkReservation.customerUuid) {
            logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedReservationUseCase] - Reservation has no customer', {
                reservationUuid: body.uuid,
            });
            return;
        }

        let providerClients = await this._providerClientsRepository.getProviderClientsByProviderClientId(theForkReservation.customerUuid);
        if (!providerClients.length) {
            const theForkClient = await this._theForkService.fetchClient(theForkReservation.customerUuid);
            if (!theForkClient) {
                logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedReservationUseCase] - Customer not found', {
                    customerUuid: theForkReservation.customerUuid,
                });
                return;
            }
            if (!theForkClient.originRestaurantUuid) {
                logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedReservationUseCase] - Customer has no origin restaurant', {
                    customerUuid: theForkReservation.customerUuid,
                });
                return;
            }
            const restaurantIds = await this._theForkService.mapTheForkRestaurantUuidToRestaurantIds(theForkClient.originRestaurantUuid);

            if (!restaurantIds.length) {
                logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedReservationUseCase] - No restaurant found for customer', {
                    customerUuid: theForkReservation.customerUuid,
                });
                this._slackService.sendMessage({
                    text: `No restaurant found for customer ${body.uuid} with origin restaurant ${theForkClient.originRestaurantUuid}`,
                    channel: SlackChannel.CRM_ALERTS,
                });
                return;
            }

            providerClients = restaurantIds.map((restaurantId) => ProviderClient.fromTheForkClient(theForkClient, restaurantId));
        }

        for (const providerClient of providerClients) {
            const providerVisit = ProviderVisit.fromTheForkVisit(theForkReservation, providerClient.restaurantId);
            providerClient.upsertProviderVisit(providerVisit);
            await this._providerClientsRepository.upsertProviderClient(providerClient);
        }
    }
}
