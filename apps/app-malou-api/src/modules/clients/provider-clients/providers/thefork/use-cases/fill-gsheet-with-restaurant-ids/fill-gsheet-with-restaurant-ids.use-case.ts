import { singleton } from 'tsyringe';

import { Config } from ':config';
import { logger } from ':helpers/logger';
import { GoogleSpreadsheetService } from ':microservices/google-spread-sheet.service';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

@singleton()
export class FillGsheetWithRestaurantIdsUseCase {
    constructor(
        private readonly _googleSpreadsheetService: GoogleSpreadsheetService,
        private readonly _restaurantsRepository: RestaurantsRepository
    ) {}

    async execute(): Promise<void> {
        logger.info('[FillGsheetWithRestaurantIdsUseCase] Starting');
        const doc = await this._getGoogleSpreadsheet();
        const sheet = doc.sheetsByIndex[0];

        const rows = await sheet.getRows();
        const rowsWithoutMalouId = rows.filter((row) => !row['Malou id'] && row['Rs UUID'] && row['Group uuid']);

        logger.info('[FillGsheetWithRestaurantIdsUseCase] Found rows without Malou id', { count: rowsWithoutMalouId.length });

        for (const row of rowsWithoutMalouId) {
            const restaurant = await this._restaurantsRepository.findOne({
                filter: { name: row['Name'] },
                projection: { _id: 1 },
                options: { lean: true },
            });

            if (!restaurant) {
                logger.error('[FillGsheetWithRestaurantIdsUseCase] No restaurant found for', { name: row['Name'] });
                continue;
            }

            row['Malou id'] = restaurant._id.toString();
        }

        await sheet.saveUpdatedCells();

        logger.info('[FillGsheetWithRestaurantIdsUseCase] Done');
    }

    private async _getGoogleSpreadsheet() {
        const privateKeys = Config.businessNotificationsAccountKey;
        const spreadSheetAuth = {
            client_email: privateKeys.client_email as string,
            private_key: privateKeys.private_key?.split('||n||').join('\n'),
        };
        const googleSheet = await this._googleSpreadsheetService.getGoogleSpreadsheet(
            '1LCgHMZeq2Ox4MKpskUkb7O0GQhGBLHbenC9Ua6xy_AA',
            spreadSheetAuth
        );
        return googleSheet;
    }
}
