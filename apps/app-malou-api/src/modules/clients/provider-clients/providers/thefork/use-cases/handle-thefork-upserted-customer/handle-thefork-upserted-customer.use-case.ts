import { singleton } from 'tsyringe';

import { logger } from ':helpers/logger';
import { ProviderClient } from ':modules/clients/provider-clients/entities/provider-client.entity';
import ProviderClientsRepository from ':modules/clients/provider-clients/provider-clients.repository';
import { TheForkService } from ':modules/clients/provider-clients/providers/thefork/service/thefork.service';
import { TheForkEventRequestBody } from ':modules/webhooks/platforms/thefork/validators/webhook-events.validators';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class HandleTheForkUpsertedCustomerUseCase {
    constructor(
        private readonly _theForkService: TheForkService,
        private readonly _providerClientsRepository: ProviderClientsRepository,
        private readonly _slackService: SlackService
    ) {}

    async execute({ body }: { body: TheForkEventRequestBody }): Promise<void> {
        const theForkClient = await this._theForkService.fetchClient(body.uuid);
        if (!theForkClient) {
            logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedCustomerUseCase] - Customer not found', {
                customerUuid: body.uuid,
            });
            return;
        }
        if (!theForkClient.originRestaurantUuid) {
            logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedCustomerUseCase] - Customer has no origin restaurant', {
                customerUuid: body.uuid,
            });
            return;
        }

        const restaurantIds = await this._theForkService.mapTheForkRestaurantUuidToRestaurantIds(theForkClient.originRestaurantUuid);

        if (!restaurantIds.length) {
            logger.error('[WEBHOOKS] [THEFORK] [HandleTheForkUpsertedCustomerUseCase] - No restaurant found for customer', {
                customerUuid: body.uuid,
            });
            this._slackService.sendMessage({
                text: `No restaurant found for customer ${body.uuid} with origin restaurant ${theForkClient.originRestaurantUuid}`,
                channel: SlackChannel.CRM_ALERTS,
            });
            return;
        }

        const upsertedProviderClientsPromises = restaurantIds.map((restaurantId) => {
            const providerClient = ProviderClient.fromTheForkClient(theForkClient, restaurantId);
            return this._providerClientsRepository.upsertProviderClient(providerClient);
        });

        await Promise.all(upsertedProviderClientsPromises);
    }
}
