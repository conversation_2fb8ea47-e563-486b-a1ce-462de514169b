import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    CreateStoryV2BodyDto,
    createStoryV2BodyValidator,
    DeleteStoriesBodyDto,
    deleteStoriesBodyValidator,
    DuplicateStoriesBodyDto,
    duplicateStoriesBodyValidator,
    DuplicateStoriesParamsDto,
    duplicateStoriesParamsValidator,
    DuplicateStoriesResponseDto,
    GetPublishedStoriesCountDto,
    GetPublishedStoriesCountParamsDto,
    getPublishedStoriesCountParamsValidator,
    GetPublishedStoriesCountQueryDto,
    getPublishedStoriesCountQueryValidator,
    GetStoriesByIdsQueryDto,
    getStoriesByIdsQueryValidator,
    GetStoriesCountsDto,
    GetStoriesCountsParamsDto,
    getStoriesCountsParamsValidator,
    GetStoriesParamsDto,
    getStoriesParamsValidator,
    GetStoriesQueryDto,
    getStoriesQueryValidator,
    GetStoriesToDuplicateQueryDto,
    getStoriesToDuplicateQueryValidator,
    GetStoryParamsDto,
    getStoryParamsValidator,
    PollingPostStatusResponseDto,
    PollingStoriesStatusesBodyDto,
    pollingStoriesStatusesBodyValidator,
    StoryDto,
    StoryItemDto,
    StoryToDuplicateDto,
    updateStoryBodyValidator,
    UpdateStoryDto,
    UpdateStoryPlannedPublicationDateBodyDto,
    updateStoryPlannedPublicationDateBodyValidator,
    UpdateStoryPlannedPublicationDateParamsDto,
    updateStoryPlannedPublicationDateParamsValidator,
} from '@malou-io/package-dto';
import { ApiResultV2 } from '@malou-io/package-utils';

import { StoreMetadata } from ':helpers/decorators/store-metadata';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { RequestWithPermissions } from ':helpers/utils.types';
import { getUpdateSocialPostMetadata } from ':modules/posts/v2/helpers/get-async-local-storage-metadata.helper';
import { CreateStoryUseCase } from ':modules/stories/use-cases/create-story/create-story.use-case';
import { DeleteStoriesUseCase } from ':modules/stories/use-cases/delete-stories/delete-stories.use-case';
import { DuplicateStoriesUseCase } from ':modules/stories/use-cases/duplicate-stories/duplicate-stories.use-case';
import { GetPublishedStoriesCountUseCase } from ':modules/stories/use-cases/get-published-stories-count/get-published-stories-count.use-case';
import { GetStoriesByIdsUseCase } from ':modules/stories/use-cases/get-stories-by-ids/get-stories-by-ids.use-case';
import { GetStoriesCountsUseCase } from ':modules/stories/use-cases/get-stories-counts/get-stories-counts.use-case';
import { GetStoriesToDuplicateUseCase } from ':modules/stories/use-cases/get-stories-to-duplicate/get-stories-to-duplicate.use-case';
import { GetStoriesUseCase } from ':modules/stories/use-cases/get-stories/get-stories.use-case';
import { GetStoryByIdUseCase } from ':modules/stories/use-cases/get-story-by-id/get-story-by-id.use-case';
import { PollingStoriesStatusesUseCase } from ':modules/stories/use-cases/polling-stories-statuses/polling-stories-statuses.use-case';
import { UpdateStoryPlannedPublicationDateUseCase } from ':modules/stories/use-cases/update-story-planned-publication-date/update-story-planned-publication-date.use-case';
import { UpdateStoryUseCase } from ':modules/stories/use-cases/update-story/update-story.use-case';

@singleton()
export default class StoriesController {
    constructor(
        private readonly _getStoriesUseCase: GetStoriesUseCase,
        private readonly _getStoriesCountsUseCase: GetStoriesCountsUseCase,
        private readonly _getPublishedStoriesCountUseCase: GetPublishedStoriesCountUseCase,
        private readonly _createStoryUseCase: CreateStoryUseCase,
        private readonly _updateStoryUseCase: UpdateStoryUseCase,
        private readonly _getStoryByIdUseCase: GetStoryByIdUseCase,
        private readonly _deleteStoriesUseCase: DeleteStoriesUseCase,
        private readonly _duplicateStoriesUseCase: DuplicateStoriesUseCase,
        private readonly _getStoriesToDuplicateUseCase: GetStoriesToDuplicateUseCase,
        private readonly _getStoriesByIdsUseCase: GetStoriesByIdsUseCase,
        private readonly _pollingStoriesStatusesUseCase: PollingStoriesStatusesUseCase,
        private readonly _updateStoryPlannedPublicationDateUseCase: UpdateStoryPlannedPublicationDateUseCase
    ) {}

    @Query(getStoriesByIdsQueryValidator)
    async handleGetStoriesByIds(
        req: Request<never, never, never, GetStoriesByIdsQueryDto>,
        res: Response<ApiResultV2<StoryItemDto[]>>,
        next: NextFunction
    ) {
        try {
            const { storyIds } = req.query;
            const stories = await this._getStoriesByIdsUseCase.execute(storyIds);
            return res.json({ data: stories });
        } catch (err) {
            next(err);
        }
    }

    @Query(getStoriesQueryValidator)
    @Params(getStoriesParamsValidator)
    async handleGetStories(
        req: Request<GetStoriesParamsDto, never, never, GetStoriesQueryDto>,
        res: Response<ApiResultV2<{ storiesItems: StoryItemDto[]; nextCursor: null | Date }>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { cursor, limit, filter } = req.query;

            const results = await this._getStoriesUseCase.execute(restaurantId, cursor, limit, filter);

            res.json({ data: results });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoriesCountsParamsValidator)
    async handleGetStoriesCounts(
        req: Request<GetStoriesCountsParamsDto>,
        res: Response<ApiResultV2<GetStoriesCountsDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;

            const socialPostsCounts = await this._getStoriesCountsUseCase.execute(restaurantId);

            return res.json({ data: socialPostsCounts });
        } catch (err) {
            next(err);
        }
    }

    @Query(getPublishedStoriesCountQueryValidator)
    @Params(getPublishedStoriesCountParamsValidator)
    async handleGetPublishedStoriesCount(
        req: Request<GetPublishedStoriesCountParamsDto, never, never, GetPublishedStoriesCountQueryDto>,
        res: Response<ApiResultV2<GetPublishedStoriesCountDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, platformKeys } = req.query;

            const result = await this._getPublishedStoriesCountUseCase.execute({ restaurantId, startDate, endDate, platformKeys });

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(getStoryParamsValidator)
    async handleGetStory(req: Request<GetStoryParamsDto>, res: Response<ApiResultV2<StoryDto>>, next: NextFunction) {
        try {
            const { storyId } = req.params;

            const result = await this._getStoryByIdUseCase.execute(storyId);

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Query(getStoriesToDuplicateQueryValidator)
    async handleGetStoriesToDuplicate(
        req: Request<never, never, never, GetStoriesToDuplicateQueryDto>,
        res: Response<ApiResultV2<StoryToDuplicateDto[]>>,
        next: NextFunction
    ) {
        try {
            const { storyIds, storyBindingIds } = req.query;

            const result = await this._getStoriesToDuplicateUseCase.execute(storyIds, storyBindingIds);

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(createStoryV2BodyValidator)
    async handleCreateStory(req: Request<any, any, CreateStoryV2BodyDto>, res: Response<ApiResultV2<StoryDto>>, next: NextFunction) {
        try {
            const { user } = req;
            assert(user, 'User not found');
            const { restaurantId, date, createdFromDeviceType } = req.body;

            const result = await this._createStoryUseCase.execute({
                restaurantId,
                author: {
                    id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                },
                date,
                createdFromDeviceType,
            });

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateStoryPlannedPublicationDateParamsValidator)
    @Body(updateStoryPlannedPublicationDateBodyValidator)
    async handleUpdateStoryPlannedPublicationDate(
        req: RequestWithPermissions<UpdateStoryPlannedPublicationDateParamsDto, never, never, UpdateStoryPlannedPublicationDateBodyDto>,
        res: Response<ApiResultV2<StoryDto>>,
        next: NextFunction
    ) {
        try {
            assert(req.user, 'User not found');
            const { storyId } = req.params;
            const { plannedPublicationDate, recurrentStoryFrequency } = req.body;
            const author = {
                id: req.user._id.toString(),
                name: req.user.name,
                lastname: req.user.lastname,
            };
            const result = await this._updateStoryPlannedPublicationDateUseCase.execute(
                storyId,
                plannedPublicationDate,
                recurrentStoryFrequency,
                author
            );

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(updateStoryBodyValidator)
    @StoreMetadata(getUpdateSocialPostMetadata)
    async handleUpdateStory(req: Request<any, any, UpdateStoryDto>, res: Response<ApiResultV2<StoryDto>>, next: NextFunction) {
        try {
            const { user } = req;
            assert(user, 'User not found');

            const result = await this._updateStoryUseCase.execute({
                story: req.body,
                author: {
                    id: user._id.toString(),
                    name: user.name,
                    lastname: user.lastname,
                },
            });

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(deleteStoriesBodyValidator)
    async handleDeleteStories(
        req: Request<any, any, DeleteStoriesBodyDto>,
        res: Response<ApiResultV2<{ storyId: string; success: boolean }[]>>,
        next: NextFunction
    ) {
        try {
            const { storyIds } = req.body;

            const result = await this._deleteStoriesUseCase.execute(storyIds);

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Params(duplicateStoriesParamsValidator)
    @Body(duplicateStoriesBodyValidator)
    async handleDuplicateStories(
        req: RequestWithPermissions<DuplicateStoriesParamsDto, any, DuplicateStoriesBodyDto>,
        res: Response<ApiResultV2<DuplicateStoriesResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { restaurantIds, postRefsToDuplicate, customFields, createdFromDeviceType } = req.body;

            const userRestaurantsAbility = req.userRestaurantsAbility;
            assert(userRestaurantsAbility, 'User restaurants ability not found');
            assert(req.user, 'User not found');

            const result = await this._duplicateStoriesUseCase.execute({
                restaurantIds,
                postRefsToDuplicate,
                author: {
                    id: req.user._id.toString(),
                    name: req.user.name,
                    lastname: req.user.lastname,
                },
                userRestaurantsAbility,
                fromRestaurantId: restaurantId,
                customFields,
                createdFromDeviceType: createdFromDeviceType ?? undefined,
            });

            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }

    @Body(pollingStoriesStatusesBodyValidator)
    async handlePollingStoriesStatuses(
        req: Request<never, never, PollingStoriesStatusesBodyDto>,
        res: Response<ApiResultV2<PollingPostStatusResponseDto[]>>,
        next: NextFunction
    ) {
        try {
            const { bindingIds } = req.body;
            const result = await this._pollingStoriesStatusesUseCase.execute(bindingIds);
            return res.json({ data: result });
        } catch (err) {
            next(err);
        }
    }
}
