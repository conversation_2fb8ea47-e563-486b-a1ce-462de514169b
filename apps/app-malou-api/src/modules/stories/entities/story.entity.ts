import { DateTime } from 'luxon';
import { v4 as uuidv4 } from 'uuid';

import { StoryDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import {
    DeviceType,
    PlatformKey,
    PostFeedbacks,
    PostPublicationStatus,
    PostUserTag,
    PublicationErrorCode,
    RecurrentStoryFrequency,
    RemoveMethodsFromClass,
    roundUpToTheNext15MinuteInterval,
} from '@malou-io/package-utils';

import { PostAuthor } from ':modules/posts/v2/entities/author.entity';
import { SocialPostMedia } from ':modules/posts/v2/entities/social-post.entity';

export type StoryProps = RemoveMethodsFromClass<Story>;

export class Story {
    id: string;
    platformKeys: PlatformKey[];
    published: PostPublicationStatus;
    isPublishing: boolean;
    plannedPublicationDate?: Date;
    medias: { uploadedMedia: SocialPostMedia; editedMedia?: SocialPostMedia }[];
    feedbacks?: PostFeedbacks;
    userTagsList: (PostUserTag[] | null)[];
    author?: PostAuthor;
    mostRecentPublicationErrorCode?: PublicationErrorCode;
    bindingId?: string;
    createdFromDeviceType?: DeviceType;
    socialLink?: string;
    socialCreatedAt?: Date;
    recurrentStoryFrequency?: RecurrentStoryFrequency;
    // TODO stories-v2 remove when v1 is removed
    malouStoryId?: string;

    constructor(data: StoryProps) {
        this.id = data.id;
        this.platformKeys = data.platformKeys;
        this.published = data.published;
        this.isPublishing = data.isPublishing;
        this.plannedPublicationDate = data.plannedPublicationDate;
        this.medias = data.medias;
        this.feedbacks = data.feedbacks;
        this.userTagsList = data.userTagsList;
        this.author = data.author;
        this.mostRecentPublicationErrorCode = data.mostRecentPublicationErrorCode;
        this.bindingId = data.bindingId;
        this.createdFromDeviceType = data.createdFromDeviceType;
        this.socialLink = data.socialLink;
        this.socialCreatedAt = data.socialCreatedAt;
        this.malouStoryId = data.malouStoryId;
        this.recurrentStoryFrequency = data.recurrentStoryFrequency;
    }

    static createEmpty({
        platformKeys,
        author,
        date,
        createdFromDeviceType,
    }: {
        platformKeys: PlatformKey[];
        author: PostAuthor;
        date?: Date;
        createdFromDeviceType: DeviceType;
    }): Story {
        const tomorrow = DateTime.now().plus({ days: 1 });
        const initialDate = date ? DateTime.fromJSDate(date) : tomorrow;
        const plannedPublicationDate = roundUpToTheNext15MinuteInterval(initialDate.toJSDate());

        return new Story({
            id: newDbId().toString(),
            platformKeys,
            published: PostPublicationStatus.DRAFT,
            isPublishing: false,
            plannedPublicationDate,
            medias: [],
            userTagsList: [],
            author,
            createdFromDeviceType,
            bindingId: uuidv4(),
        });
    }

    toDto(): StoryDto {
        return {
            id: this.id,
            platformKeys: this.platformKeys,
            published: this.published,
            isPublishing: this.isPublishing,
            plannedPublicationDate: this.plannedPublicationDate?.toISOString() ?? new Date().toISOString(),
            medias: this.medias,
            feedbacks: this.feedbacks
                ? {
                      id: this.feedbacks.id,
                      isOpen: this.feedbacks.isOpen,
                      participants: this.feedbacks.participants,
                      feedbackMessages: this.feedbacks.feedbackMessages.map((message) => ({
                          id: message.id,
                          type: message.type,
                          visibility: message.visibility,
                          text: message.text,
                          author: message.author,
                          createdAt: message.createdAt.toISOString(),
                          updatedAt: message.updatedAt.toISOString(),
                          publishedAt: message.publishedAt?.toISOString(),
                          lastUpdatedAt: message.lastUpdatedAt?.toISOString(),
                      })),
                      createdAt: this.feedbacks.createdAt.toISOString(),
                      updatedAt: this.feedbacks.updatedAt.toISOString(),
                  }
                : null,
            userTagsList: this.userTagsList,
            author: this.author ? this.author.toDto() : undefined,
            mostRecentPublicationErrorCode: this.mostRecentPublicationErrorCode,
            bindingId: this.bindingId,
            createdFromDeviceType: this.createdFromDeviceType,
            socialLink: this.socialLink,
            socialCreatedAt: this.socialCreatedAt?.toISOString(),
            recurrentStoryFrequency: this.recurrentStoryFrequency,
        };
    }

    copyWith(data: Partial<StoryProps>): Story {
        return new Story({
            id: data.id ?? this.id,
            platformKeys: data.platformKeys ?? this.platformKeys,
            published: data.published ?? this.published,
            isPublishing: false,
            plannedPublicationDate: data.plannedPublicationDate ?? this.plannedPublicationDate,
            medias: data.medias ?? this.medias,
            feedbacks: data.feedbacks ?? this.feedbacks,
            userTagsList: data.userTagsList ?? this.userTagsList,
            socialLink: data.socialLink ?? this.socialLink,
            socialCreatedAt: data.socialCreatedAt ?? this.socialCreatedAt,
            author: data.author ?? this.author,
            bindingId: data.bindingId ?? this.bindingId,
            mostRecentPublicationErrorCode: data.mostRecentPublicationErrorCode ?? this.mostRecentPublicationErrorCode,
            createdFromDeviceType: data.createdFromDeviceType ?? this.createdFromDeviceType,
            malouStoryId: data.malouStoryId ?? this.malouStoryId,
            recurrentStoryFrequency: data.recurrentStoryFrequency ?? this.recurrentStoryFrequency,
        });
    }
}
