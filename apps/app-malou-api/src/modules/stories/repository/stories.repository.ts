import { compact } from 'lodash';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { UpdateStoryDto } from '@malou-io/package-dto';
import {
    DbId,
    EntityRepository,
    IFeedback,
    IMedia,
    IPost,
    IPostPublicationError,
    OverwriteOrAssign,
    PostModel,
    ReadPreferenceMode,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import {
    MalouErrorCode,
    MediaType,
    PlatformKey,
    PostFeedbacks,
    PostPublicationStatus,
    PostSource,
    PostType,
    PublicationType,
    RecurrentStoryFrequency,
    Role,
    SeoPostTopic,
    SocialAttachmentsMediaTypes,
    StoriesListFilter,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { GetMediaInfoService } from ':modules/media/services/get-media-thumbnail/get-media-thumbnail.service';
import { GetMediaForEditionService } from ':modules/media/use-cases/get-media-for-edition/get-media-for-edition.service';
import { PostAuthor } from ':modules/posts/v2/entities/author.entity';
import { SocialPostMedia } from ':modules/posts/v2/entities/social-post-media.entity';
import {
    errorStage,
    feedbackMessageCountProjectionStages,
    limitStage,
    platformsKeysProjectionStage,
    sortStage,
} from ':modules/posts/v2/repository/posts.pipeline';
import { StoryItem } from ':modules/stories/entities/story-item.entity';
import { StoryToDuplicate } from ':modules/stories/entities/story-to-duplicate.entity';
import { Story } from ':modules/stories/entities/story.entity';
import {
    countStoriesStages,
    lookupAttachmentsStage,
    lookupMediasStage,
    projectStoryItemStage,
} from ':modules/stories/repository/stories.pipeline';
import { IStoryItem, IStoryWithAttachmentsAndFeedbacks, upsertStoryIKeys } from ':modules/stories/repository/story-repository.types';

const SORT_DATE_COMPUTATION = () => ({
    sortDate: { $ifNull: ['$socialCreatedAt', { $ifNull: ['$plannedPublicationDate', new Date()] }] },
});
const SET_SORT_DATE = () => ({ $set: SORT_DATE_COMPUTATION() });

/**
 * !!! README !!!
 * This repository is linked to the POSTS collection in MongoDB
 * You should always filter the stories with the `isStory: true` condition
 * in order not to impact the other types of posts.
 */
@singleton()
export class StoriesRepository extends EntityRepository<IPost> {
    constructor(
        private readonly _getMediaInfoService: GetMediaInfoService,
        private readonly _getMediaForEditionService: GetMediaForEditionService
    ) {
        super(PostModel);
    }

    findById(id: string): Promise<IPost | null> {
        return this.findOne({ filter: { _id: toDbId(id), isStory: true }, options: { lean: true } });
    }

    async getStoriesByIds(storyIds: string[]): Promise<StoryItem[]> {
        const matchStage = {
            $match: { _id: { $in: toDbIds(storyIds) }, isStory: true },
        };

        const pipeline = [
            matchStage,
            ...feedbackMessageCountProjectionStages,
            platformsKeysProjectionStage,
            ...lookupAttachmentsStage,
            ...lookupMediasStage,
            errorStage,
            projectStoryItemStage,
        ];
        const stories = (await this.aggregate(pipeline)) as IStoryItem[];

        const storyItems = await Promise.all(stories.map((story) => this._toStoryItemEntity(story)));
        return storyItems;
    }

    async getStories(restaurantId: string, cursor: null | Date, limit: number, filter: StoriesListFilter | null): Promise<StoryItem[]> {
        if (filter === StoriesListFilter.FEEDBACK) {
            return this._getStoriesWithFeedback(restaurantId, cursor, limit);
        }
        const dbFilter = this._getDbFilterFromStoriesListFilter(filter);
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
                ...(dbFilter && { ...dbFilter }),
                ...(cursor && { sortDate: { $lt: cursor } }),
            },
        };

        const pipeline = [
            matchStage,
            sortStage,
            limitStage(limit),
            ...feedbackMessageCountProjectionStages,
            platformsKeysProjectionStage,
            ...lookupAttachmentsStage,
            ...lookupMediasStage,
            errorStage,
            projectStoryItemStage,
        ];

        const stories = (await this.aggregate(pipeline)) as IStoryItem[];

        const results: StoryItem[] = await Promise.all(stories.map((story) => this._toStoryItemEntity(story)));
        return results;
    }

    async getStoriesCounts(
        restaurantId: string
    ): Promise<{ total?: number; draft?: number; error?: number; feedbacks?: number; recurrent?: number }> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
            },
        };

        const pipeline = [matchStage, ...feedbackMessageCountProjectionStages, ...countStoriesStages()];

        const result = (await this.aggregate(pipeline, { readPreference: ReadPreferenceMode.SECONDARY_PREFERRED }))[0] as {
            total?: number;
            draft?: number;
            error?: number;
            feedbacks?: number;
            recurrent?: number;
        };
        return result;
    }

    async getPublishedStoriesCount({
        restaurantId,
        startDate,
        endDate,
        platformKeys,
    }: {
        restaurantId: string;
        startDate: Date;
        endDate: Date;
        platformKeys: PlatformKey[];
    }): Promise<number> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                key: { $in: platformKeys },
                source: PostSource.SOCIAL,
                isStory: true,
                published: PostPublicationStatus.PUBLISHED,
                socialCreatedAt: {
                    $gte: startDate,
                    $lte: endDate,
                },
            },
        };

        const pipeline = [
            matchStage,
            {
                $count: 'count',
            },
        ];

        const result = (await this.aggregate(pipeline, { readPreference: ReadPreferenceMode.SECONDARY_PREFERRED }))[0] as {
            count?: number;
        };
        return result?.count ?? 0;
    }

    async getStoryById(storyId: string): Promise<Story | null> {
        const story = await this.findOne({
            filter: { _id: toDbId(storyId), isStory: true },
            projection: upsertStoryIKeys,
            options: { lean: true, populate: [{ path: 'feedback' }, { path: 'attachments' }] },
        });
        if (!story) {
            return null;
        }

        return this._toStoryEntity(story);
    }

    async getStoriesToDuplicate(storyIds: string[], storyBindingIds: string[]): Promise<StoryToDuplicate[]> {
        const stories = await this.find({
            filter: { isStory: true, $or: [{ _id: { $in: toDbIds(storyIds) } }, { bindingId: { $in: storyBindingIds } }] },
            options: { lean: true, populate: [{ path: 'attachments' }] },
        });
        const storiesToDuplicate: StoryToDuplicate[] = [];
        for (const story of stories) {
            const storyToDuplicate = await this._toStoryToDuplicateEntity(story);
            storiesToDuplicate.push(storyToDuplicate);
        }
        return storiesToDuplicate;
    }

    async createStory(data: Story, restaurantId: string): Promise<Story> {
        const story = await this.create({
            data: this._mapStoryToPostDocumentForCreation(data, { restaurantId: toDbId(restaurantId) }),
            options: { lean: true, projection: upsertStoryIKeys, populate: [{ path: 'feedback' }, { path: 'attachments' }] },
        });

        if (!story) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { metadata: { data }, message: 'Creation failed' });
        }

        return this._toStoryEntity(story as unknown as IStoryWithAttachmentsAndFeedbacks);
    }

    async createMultipleStories(data: { story: Story; restaurantId: string; duplicatedFromRestaurantId?: string }[]): Promise<void> {
        await this.createMany({
            data: data.map(({ story, restaurantId, duplicatedFromRestaurantId }) =>
                this._mapStoryToPostDocumentForCreation(story, {
                    restaurantId: toDbId(restaurantId),
                    duplicatedFromRestaurantId: duplicatedFromRestaurantId ? toDbId(duplicatedFromRestaurantId) : undefined,
                })
            ),
            options: { lean: true },
        });
    }

    async updateStory(id: string, storyToUpdate: UpdateStoryDto, author: PostAuthor, plannedPublicationDate: Date): Promise<Story | null> {
        const update = this._storyToUpdateDocument(storyToUpdate, plannedPublicationDate);
        const addAuthorToAuthors = {
            authors: {
                $concatArrays: [
                    {
                        $ifNull: ['$authors', []],
                    },
                    [
                        {
                            _id: toDbId(author.id),
                            name: author.name,
                            lastname: author.lastname,
                            picture: author.picture,
                        },
                    ],
                ],
            },
        };
        const updatePostStage = {
            $set: {
                ...update,
                ...addAuthorToAuthors,
            },
        };

        const setSortDateStage = SET_SORT_DATE();

        const story = await this.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: [updatePostStage, setSortDateStage],
            projection: upsertStoryIKeys,
            options: {
                lean: true,
                populate: [{ path: 'feedback' }, { path: 'attachments' }],
            },
        });

        if (!story) {
            return null;
        }

        return this._toStoryEntity(story);
    }

    async updateStoryPlannedPublicationDate({
        storyId,
        plannedPublicationDate,
        recurrentStoryFrequency,
        published,
        author,
    }: {
        storyId: string;
        plannedPublicationDate: Date;
        recurrentStoryFrequency: RecurrentStoryFrequency;
        published: PostPublicationStatus;
        author: PostAuthor;
    }): Promise<Story | null> {
        const update = {
            _id: toDbId(storyId),
            plannedPublicationDate,
            recurrentStoryFrequency,
            published,
        };
        const addAuthorToAuthors = {
            authors: {
                $concatArrays: [
                    {
                        $ifNull: ['$authors', []],
                    },
                    [
                        {
                            _id: toDbId(author.id),
                            name: author.name,
                            lastname: author.lastname,
                            picture: author.picture,
                        },
                    ],
                ],
            },
        };
        const updatePostStage = {
            $set: {
                ...update,
                ...addAuthorToAuthors,
            },
        };

        const setSortDateStage = SET_SORT_DATE();

        const story = await this.findOneAndUpdate({
            filter: { _id: toDbId(storyId) },
            update: [updatePostStage, setSortDateStage],
            projection: upsertStoryIKeys,
            options: {
                lean: true,
                populate: [{ path: 'feedback' }, { path: 'attachments' }],
            },
        });

        if (!story) {
            return null;
        }

        return this._toStoryEntity(story);
    }

    deleteStoryById(id: string): Promise<{ acknowledged: boolean; deletedCount: number }> {
        return this.deleteOne({ filter: { source: PostSource.SOCIAL, isStory: true, _id: toDbId(id) } });
    }

    async incrementTries(postId: string): Promise<void> {
        await this.updateOne({
            filter: { _id: toDbId(postId) },
            update: [
                {
                    $set: {
                        tries: {
                            $add: [{ $ifNull: ['$tries', 0] }, 1],
                        },
                    },
                },
            ],
        });
    }

    async updatePublicationStatus(postId: string, published: PostPublicationStatus): Promise<void> {
        const update: Partial<IPost> = { published };
        if (published !== PostPublicationStatus.PENDING) {
            update.isPublishing = false;
        }
        await this.updateOne({ filter: { _id: toDbId(postId) }, update });
    }

    async pushPublicationError(postId: string, error: IPostPublicationError): Promise<void> {
        await this.updateOne({
            filter: { _id: toDbId(postId) },
            update: [
                {
                    $set: {
                        publicationErrors: { $concatArrays: ['$publicationErrors', [error]] },
                        errorStage: 'publish post', // retro compat
                        errorData: error.code ?? error.data ?? '', // retro compat
                    },
                },
            ],
        });
    }

    private _getDbFilterFromStoriesListFilter(
        filter: StoriesListFilter | null
    ):
        | { published: PostPublicationStatus; createdAt?: { $gt: Date } }
        | { recurrentStoryFrequency: { $exists: true; $ne: RecurrentStoryFrequency } }
        | null {
        switch (filter) {
            case StoriesListFilter.DRAFT:
                return { published: PostPublicationStatus.DRAFT };
            case StoriesListFilter.ERROR:
                return { published: PostPublicationStatus.ERROR, createdAt: { $gt: DateTime.now().minus({ months: 6 }).toJSDate() } };
            case StoriesListFilter.RECURRENT:
                return { recurrentStoryFrequency: { $exists: true, $ne: RecurrentStoryFrequency.NONE } };
            default:
                return null;
        }
    }

    private async _toStoryEntity(story: IStoryWithAttachmentsAndFeedbacks): Promise<Story> {
        let medias: Story['medias'] = [];
        if (story.medias && story.medias.length > 0) {
            medias = await Promise.all(
                story.medias.map(async (media) => ({
                    uploadedMedia: await this._getMediaForEditionService.getMedia(media.uploadedMediaId.toString(), PublicationType.STORY),
                    editedMedia: media.editedMediaId
                        ? await this._getMediaForEditionService.getMedia(media.editedMediaId.toString(), PublicationType.STORY)
                        : undefined,
                }))
            );
        } else {
            medias = await Promise.all(
                story.attachments.map(async (attachment) => ({
                    uploadedMedia: await this._getMediaForEditionService.getMedia(attachment._id.toString(), PublicationType.STORY),
                    editedMedia: undefined,
                }))
            );
        }

        return new Story({
            id: story._id.toString(),
            platformKeys: story.key ? [story.key] : story.keys,
            published: story.published,
            isPublishing: story.isPublishing ?? false,
            plannedPublicationDate: story.plannedPublicationDate ?? new Date(),
            medias,
            feedbacks: this._mapFeedbacksToPostFeedbacks(story.feedback),
            userTagsList: story.userTagsList ?? medias.map(() => null),
            socialLink: story.socialLink,
            socialCreatedAt: story.socialCreatedAt,
            author: story.author
                ? new PostAuthor({
                      id: story.author._id.toString(),
                      name: story.author.name,
                      lastname: story.author.lastname,
                      picture: story.author.picture ?? undefined,
                  })
                : undefined,
            bindingId: story.bindingId ?? undefined,
            createdFromDeviceType: story.createdFromDeviceType,
            mostRecentPublicationErrorCode: story.publicationErrors
                ? story.publicationErrors[story.publicationErrors.length - 1]?.code
                : undefined,
            recurrentStoryFrequency: story.recurrentStoryFrequency ?? undefined,
        });
    }

    private async _getStoriesWithFeedback(restaurantId: string, cursor: null | Date, limit: number): Promise<StoryItem[]> {
        const matchStage = {
            $match: {
                restaurantId: toDbId(restaurantId),
                source: PostSource.SOCIAL,
                isStory: true,
                feedbackId: { $ne: null },
                ...(cursor && { sortDate: { $lt: cursor } }),
            },
        };
        const secondMatchStage = {
            $match: {
                feedbackMessageCount: { $gt: 0 },
            },
        };

        const pipeline = [
            matchStage,
            ...feedbackMessageCountProjectionStages,
            secondMatchStage,
            sortStage,
            limitStage(limit),
            platformsKeysProjectionStage,
            ...lookupAttachmentsStage,
            ...lookupMediasStage,
            errorStage,
            projectStoryItemStage,
        ];
        const stories = (await this.aggregate(pipeline)) as IStoryItem[];

        const results: StoryItem[] = [];
        for (const story of stories) {
            results.push(await this._toStoryItemEntity(story));
        }
        return results;
    }

    private async _toStoryToDuplicateEntity(story: OverwriteOrAssign<IPost, { attachments: IMedia[] }>): Promise<StoryToDuplicate> {
        const medias = await Promise.all(story.attachments.map((attachment) => this._IMediaToSocialPostMedia(attachment)));
        assert(story.bindingId);
        return new StoryToDuplicate({
            id: story._id.toString(),
            bindingId: story.bindingId,
            platformKeys: story.key ? [story.key] : story.keys,
            published: story.published,
            plannedPublicationDate: story.plannedPublicationDate ?? null,
            medias: compact(medias),
            socialCreatedAt: story.socialCreatedAt ?? null,
            recurrentStoryFrequency: story.recurrentStoryFrequency ?? undefined,
        });
    }

    private async _toStoryItemEntity(story: IStoryItem): Promise<StoryItem> {
        return new StoryItem({
            id: story._id.toString(),
            platformKeys: story.platformKeys,
            published: story.published,
            isPublishing: story.isPublishing ?? false,
            feedbackMessageCount: story.feedbackMessageCount,
            plannedPublicationDate: story.plannedPublicationDate ?? undefined,
            medias: await this._getMediasFromIStoryItem(story),
            socialLink: story.socialLink,
            socialCreatedAt: story.socialCreatedAt,
            sortDate: story.sortDate ?? story.socialCreatedAt ?? story.plannedPublicationDate ?? new Date(),
            author: story.author
                ? new PostAuthor({
                      id: story.author._id.toString(),
                      name: story.author.name,
                      lastname: story.author.lastname,
                      picture: story.author.picture ?? undefined,
                  })
                : undefined,
            mostRecentPublicationErrorCode: story.mostRecentPublicationErrorCode,
            bindingId: story.bindingId,
            createdFromDeviceType: story.createdFromDeviceType,
            recurrentStoryFrequency: story.recurrentStoryFrequency ?? RecurrentStoryFrequency.NONE,
        });
    }

    private async _getMediasFromIStoryItem(item: {
        attachments?: IMedia[];
        medias?: { uploadedMedia: IMedia; editedMedia?: IMedia }[];
        socialAttachments?: IPost['socialAttachments'];
        platformKeys?: PlatformKey[];
    }): Promise<SocialPostMedia[]> {
        if (item.medias && item.medias.length > 0) {
            const mediaPromises = item.medias.map((media) => this._IMediaToSocialPostMedia(media.editedMedia ?? media.uploadedMedia));
            const medias = await Promise.all(mediaPromises);
            return compact(medias);
        }
        if (item.attachments && item.attachments.length > 0) {
            const mediaPromises = item.attachments.map((attachment) => this._IMediaToSocialPostMedia(attachment));
            const medias = await Promise.all(mediaPromises);
            return compact(medias);
        }
        if (item.socialAttachments && item.socialAttachments.length > 0) {
            const mediaPromises = item.socialAttachments.map(
                (attachment) =>
                    new SocialPostMedia({
                        url: attachment.urls.original,
                        dimensions: undefined,
                        type: this._socialAttachmentsMediaTypesToMediaType(attachment.type),
                        thumbnailUrl: attachment.thumbnailUrl ?? undefined,
                        thumbnailDimensions: undefined,
                        transformData: undefined,
                        duration: undefined,
                        aspectRatio: undefined,
                    })
            );
            const medias = await Promise.all(mediaPromises);
            return compact(medias);
        }
        return [];
    }

    private async _IMediaToSocialPostMedia(media: IMedia): Promise<SocialPostMedia | null> {
        const urlAndDimensions = this._getMediaInfoService.getUrlAndDimensions(media);
        const thumbnail256OutsideUrlAndDimensions = await this._getMediaInfoService.getThumbnail256OutsideUrlAndDimensions(media);

        if (!urlAndDimensions || !thumbnail256OutsideUrlAndDimensions) {
            return null;
        }

        return new SocialPostMedia({
            url: urlAndDimensions.url,
            dimensions: urlAndDimensions.dimensions,
            type: media.type,
            thumbnailUrl: thumbnail256OutsideUrlAndDimensions.url,
            thumbnailDimensions: thumbnail256OutsideUrlAndDimensions.dimensions,
            transformData: media.transformData,
            duration: media.duration ?? undefined,
            aspectRatio: media.aspectRatio ?? undefined,
        });
    }

    private _socialAttachmentsMediaTypesToMediaType(socialAttachmentsMediaType: SocialAttachmentsMediaTypes): MediaType {
        switch (socialAttachmentsMediaType) {
            case SocialAttachmentsMediaTypes.IMAGE:
                return MediaType.PHOTO;
            case SocialAttachmentsMediaTypes.VIDEO:
                return MediaType.VIDEO;
        }
    }

    private _mapStoryToPostDocumentForCreation(story: Story, partialData: Partial<IPost> & { restaurantId: DbId }): IPost {
        return {
            _id: toDbId(story.id),
            keys: story.platformKeys,
            postType: PostType.IMAGE,
            published: story.published,
            source: PostSource.SOCIAL,
            title: undefined,
            text: undefined,
            isStory: true,
            plannedPublicationDate: story.plannedPublicationDate,
            shouldDuplicateInOtherPlatforms: false,
            postTopic: SeoPostTopic.STANDARD,
            feedbackId: story.feedbacks ? toDbId(story.feedbacks.id) : undefined,
            attachments: toDbIds(story.medias.map((media) => media.uploadedMedia.id)),
            medias: story.medias.map((media) => ({
                uploadedMediaId: toDbId(media.uploadedMedia.id),
                editedMediaId: media.editedMedia ? toDbId(media.editedMedia.id) : undefined,
            })),
            location: null,
            callToAction: null,
            userTagsList: story.userTagsList ?? story.medias.map(() => null),
            hashtags: undefined,
            ...(story.author && {
                author: {
                    _id: toDbId(story.author.id),
                    name: story.author.name,
                    lastname: story.author.lastname,
                    picture: story.author.picture,
                },
            }),
            createdAt: new Date(),
            updatedAt: new Date(),
            sortDate: story.plannedPublicationDate ?? new Date(),
            reelThumbnailFromMedia: undefined,
            reelThumbnailFromFrame: undefined,
            createdFromDeviceType: story.createdFromDeviceType,
            bindingId: story.bindingId,
            recurrentStoryFrequency: story.recurrentStoryFrequency ?? undefined,
            // TODO stories-v2 remove when v1 is removed
            malouStoryId: story.malouStoryId,
            ...partialData,
        };
    }

    private _mapFeedbacksToPostFeedbacks(feedbacks: IFeedback | null): PostFeedbacks | undefined {
        if (!feedbacks) {
            return undefined;
        }

        return {
            id: feedbacks._id.toString(),
            isOpen: feedbacks.isOpen,
            feedbackMessages: feedbacks.feedbackMessages.map((feedbackMessage) => ({
                id: feedbackMessage._id.toString(),
                text: feedbackMessage.text,
                type: feedbackMessage.type,
                visibility: feedbackMessage.visibility,
                author: feedbackMessage.author
                    ? {
                          id: feedbackMessage.author._id.toString(),
                          name: feedbackMessage.author.name,
                          lastname: feedbackMessage.author.lastname,
                          profilePictureUrl: feedbackMessage.author.profilePictureUrl,
                          email: feedbackMessage.author.email,
                          role: this._mapRoleStringToRole(feedbackMessage.author.role) ?? Role.MALOU_BASIC,
                      }
                    : {
                          id: '',
                          name: '',
                          lastname: '',
                          profilePictureUrl: undefined,
                          email: '',
                          role: Role.MALOU_BASIC,
                      },
                createdAt: feedbackMessage.createdAt,
                updatedAt: feedbackMessage.updatedAt ?? feedbackMessage.createdAt, // retro compatibility, some feedback messages have no updatedAt in db
                publishedAt: feedbackMessage.publishedAt,
                lastUpdatedAt: feedbackMessage.lastUpdatedAt,
            })),
            participants: feedbacks.participants.map((participant) => ({
                participant: {
                    id: participant.participant._id.toString(),
                    name: participant.participant.name,
                    lastname: participant.participant.lastname,
                    email: participant.participant.email,
                    role: this._mapRoleStringToRole(participant.participant.role),
                },
                types: participant.types,
            })),
            createdAt: feedbacks.createdAt,
            updatedAt: feedbacks.updatedAt,
        };
    }

    private _mapRoleStringToRole(role?: string): Role | undefined {
        if (Object.values(Role).includes(role as any)) {
            return role as Role;
        }
        return undefined;
    }

    /**
     * Only keep the fields that need to be updated in the document
     */
    private _storyToUpdateDocument(
        story: UpdateStoryDto,
        plannedPublicationDate: Date
    ): Omit<
        IPost,
        | 'createdAt'
        | 'updatedAt'
        | 'hashtags'
        | 'restaurantId'
        | 'source'
        | 'postType'
        | 'postTopic'
        | 'isStory'
        | 'author'
        | 'shouldDuplicateInOtherPlatforms'
    > {
        return {
            _id: toDbId(story.id),
            keys: story.platformKeys,
            published: story.published,
            plannedPublicationDate,
            attachments: toDbIds(story.medias.map((media) => media.uploadedMedia.id)),
            feedbackId: story.feedbackId ? toDbId(story.feedbackId) : null,
            isPublishing: story.isPublishing,
            userTagsList: story.userTagsList,
            recurrentStoryFrequency: story.recurrentStoryFrequency ?? undefined,
            medias: story.medias.map((media) => ({
                uploadedMediaId: toDbId(media.uploadedMedia.id),
                editedMediaId: media.editedMedia ? toDbId(media.editedMedia.id) : undefined,
            })),
        };
    }
}
