import { IMedia, IPost, PopulateBuilderHelper } from '@malou-io/package-models';
import { PlatformKey, PublicationErrorCode } from '@malou-io/package-utils';

export const upsertStoryIKeys = [
    '_id',
    'key',
    'keys',
    'published',
    'isPublishing',
    'plannedPublicationDate',
    'attachments',
    'medias',
    'feedback',
    'feedbackId',
    'errorData',
    'errorStage',
    'socialLink',
    'socialCreatedAt',
    'author',
    'userTagsList',
    'bindingId',
    'isStory',
    'createdFromDeviceType',
    'publicationErrors',
    'recurrentStoryFrequency',
] as const;
type IPostKeysForStory = (typeof upsertStoryIKeys)[number];

export type IStoryWithAttachmentsAndFeedbacks = Pick<
    PopulateBuilderHelper<
        IPost,
        [
            {
                path: 'attachments';
            },
            {
                path: 'feedback';
            },
        ]
    >,
    IPostKeysForStory
>;

export interface IStoryItem {
    _id: IPost['_id'];
    title: IPost['title'];
    text: IPost['text'];
    platformKeys: PlatformKey[];
    published: IPost['published'];
    isPublishing: IPost['isPublishing'];
    postType: IPost['postType'];
    feedbackMessageCount: number;
    plannedPublicationDate: IPost['plannedPublicationDate'];
    attachments?: IMedia[];
    socialAttachments?: IPost['socialAttachments'];
    hashtags?: IPost['hashtags'];
    socialLink?: IPost['socialLink'];
    socialCreatedAt?: IPost['socialCreatedAt'];
    sortDate?: IPost['sortDate'];
    author?: IPost['author'];
    mostRecentPublicationErrorCode?: PublicationErrorCode;
    bindingId?: IPost['bindingId'];
    thumbnail?: IMedia;
    createdFromDeviceType?: IPost['createdFromDeviceType'];
    recurrentStoryFrequency?: IPost['recurrentStoryFrequency'];
}
