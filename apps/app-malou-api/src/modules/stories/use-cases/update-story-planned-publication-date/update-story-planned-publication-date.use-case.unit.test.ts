import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { MalouErrorCode, PostPublicationStatus, PostSource, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { UpdateStoryPlannedPublicationDateUseCase } from ':modules/stories/use-cases/update-story-planned-publication-date/update-story-planned-publication-date.use-case';

describe('UpdateStoryPlannedPublicationDateUseCase', () => {
    beforeAll(() => {
        registerRepositories(['PostsRepository']);
    });

    it('should update the planned publication date of a story', async () => {
        const inTwoDays = DateTime.now().plus({ days: 2 }).toJSDate();
        const inThreeDays = DateTime.now().plus({ days: 3 }).toJSDate();

        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [getDefaultPost().source(PostSource.SOCIAL).isStory(true).plannedPublicationDate(inTwoDays).build()];
                    },
                },
            },
            expectedResult(): string {
                return inThreeDays.toISOString();
            },
        });

        await testCase.build();

        const useCase = container.resolve(UpdateStoryPlannedPublicationDateUseCase);

        const seededObjects = testCase.getSeededObjects();

        const storyId = seededObjects.posts[0]._id.toString();
        const date = inThreeDays;
        const author = {
            id: newDbId().toString(),
            name: 'John',
            lastname: 'Doe',
        };

        const expectedResult = testCase.getExpectedResult();
        const result = await useCase.execute(storyId, date, RecurrentStoryFrequency.NONE, author);

        expect(result.plannedPublicationDate).toEqual(expectedResult);
    });

    it('should update the published status of a story in error to pending', async () => {
        const inTwoDays = DateTime.now().plus({ days: 2 }).toJSDate();
        const inThreeDays = DateTime.now().plus({ days: 3 }).toJSDate();

        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [
                            getDefaultPost()
                                .source(PostSource.SOCIAL)
                                .isStory(true)
                                .published(PostPublicationStatus.ERROR)
                                .plannedPublicationDate(inTwoDays)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(): PostPublicationStatus {
                return PostPublicationStatus.PENDING;
            },
        });

        await testCase.build();

        const useCase = container.resolve(UpdateStoryPlannedPublicationDateUseCase);

        const seededObjects = testCase.getSeededObjects();

        const storyId = seededObjects.posts[0]._id.toString();
        const date = inThreeDays;
        const author = {
            id: newDbId().toString(),
            name: 'John',
            lastname: 'Doe',
        };

        const expectedResult = testCase.getExpectedResult();
        const result = await useCase.execute(storyId, date, RecurrentStoryFrequency.NONE, author);

        expect(result.published).toEqual(expectedResult);
    });

    it('should throw an error if the story is not found', async () => {
        const testCase = new TestCaseBuilderV2<'posts'>({
            seeds: {
                posts: {
                    data() {
                        return [];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.POST_NOT_FOUND,
        });

        await testCase.build();

        const useCase = container.resolve(UpdateStoryPlannedPublicationDateUseCase);

        const storyId = newDbId().toString();
        const date = new Date();
        const author = {
            id: newDbId().toString(),
            name: 'John',
            lastname: 'Doe',
        };

        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(useCase.execute(storyId, date, RecurrentStoryFrequency.NONE, author)).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });
});
