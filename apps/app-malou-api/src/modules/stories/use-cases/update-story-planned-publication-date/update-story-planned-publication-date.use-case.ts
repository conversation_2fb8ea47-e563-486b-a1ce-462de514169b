import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { StoryDto } from '@malou-io/package-dto';
import { MalouErrorCode, PostPublicationStatus, RecurrentStoryFrequency } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { ScheduleStoryPublicationService } from ':modules/stories/services/schedule-story-publication.service';

@singleton()
export class UpdateStoryPlannedPublicationDateUseCase {
    constructor(
        private readonly _storiesRepository: StoriesRepository,
        private readonly _scheduleStoryPublicationService: ScheduleStoryPublicationService
    ) {}

    async execute(
        storyId: string,
        plannedPublicationDate: Date,
        recurrentStoryFrequency: RecurrentStoryFrequency,
        author: PostAuthorProps
    ): Promise<StoryDto> {
        const story = await this._storiesRepository.getStoryById(storyId);
        if (!story) {
            throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { metadata: { storyId } });
        }
        // Updating the planned publication date is like re-scheduling the story
        // Meaning that if the story was in error, it is now pending again
        const status = story.published === PostPublicationStatus.ERROR ? PostPublicationStatus.PENDING : story.published;
        const updatedStory = await this._storiesRepository.updateStoryPlannedPublicationDate({
            storyId,
            plannedPublicationDate,
            published: status,
            recurrentStoryFrequency,
            author: new PostAuthor(author),
        });
        assert(updatedStory);

        if (updatedStory.published === PostPublicationStatus.PENDING && updatedStory.plannedPublicationDate) {
            await this._scheduleStoryPublicationService.scheduleStoryPublication(updatedStory.id, updatedStory.plannedPublicationDate);
        } else {
            await this._scheduleStoryPublicationService.cancelStoryPublication(updatedStory.id);
        }

        return updatedStory.toDto();
    }
}
