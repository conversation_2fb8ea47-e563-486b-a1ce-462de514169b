import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { StoryDto, UpdateStoryDto } from '@malou-io/package-dto';
import { MalouErrorCode, PostPublicationStatus } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { UpdateMediaPostIdsService } from ':modules/media/services/update-media-post-ids.service';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { StoriesRepository } from ':modules/stories/repository/stories.repository';
import { ScheduleStoryPublicationService } from ':modules/stories/services/schedule-story-publication.service';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class UpdateStoryUseCase {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        private readonly _storiesRepository: StoriesRepository,
        private readonly _scheduleStoryPublicationService: ScheduleStoryPublicationService,
        private readonly _slackService: SlackService,
        private readonly _updateMediaPostIdsService: UpdateMediaPostIdsService
    ) {}

    async execute({ story, author }: { story: UpdateStoryDto; author: PostAuthorProps }): Promise<StoryDto> {
        try {
            logger.info('[STORY PUBLICATION] [UPDATE] Started', { story, author });

            const storyBeforeUpdate = await this._storiesRepository.findOne({ filter: { _id: story.id }, options: { lean: true } });

            if (!storyBeforeUpdate) {
                throw new MalouError(MalouErrorCode.POST_NOT_FOUND, { metadata: { storyId: story.id } });
            }

            if (storyBeforeUpdate.published === PostPublicationStatus.PENDING && storyBeforeUpdate.isPublishing) {
                throw new MalouError(MalouErrorCode.POST_IS_PUBLISHING, { metadata: { storyId: story.id } });
            }

            const plannedPublicationDate = story.plannedPublicationDate ? new Date(story.plannedPublicationDate) : new Date();

            for (const media of story.medias) {
                await this._mediasRepository.updateTransformData(media.uploadedMedia.id, media.uploadedMedia.transformData);
            }

            const storyAuthor = new PostAuthor({ id: author.id, name: author.name, lastname: author.lastname });
            const newPost = await this._storiesRepository.updateStory(story.id, story, storyAuthor, plannedPublicationDate);
            assert(newPost);
            const dto = newPost.toDto();

            if (dto.published === PostPublicationStatus.DRAFT) {
                // TODO posts-v2 This code needs to be removed/refactored when post v1 is removed
                await this._updateMediaPostIdsService.updateMediaPostIds(story.id, { $pull: { postIds: story.id } });
            } else {
                // TODO posts-v2 This code needs to be removed/refactored when post v1 is removed
                await this._updateMediaPostIdsService.updateMediaPostIds(story.id, { $addToSet: { postIds: story.id } });
            }

            if (dto.published === PostPublicationStatus.PENDING) {
                await this._scheduleStoryPublicationService.scheduleStoryPublication(dto.id, plannedPublicationDate);
            } else {
                await this._scheduleStoryPublicationService.cancelStoryPublication(dto.id);
            }

            logger.info('[STORY PUBLICATION] [UPDATE] Finished', story);
            return dto;
        } catch (err: unknown) {
            logger.error('[STORY PUBLICATION] [UPDATE] Error', { story, author });
            if (!(err instanceof MalouError)) {
                this._slackService.sendAlert({ data: { err }, channel: SlackChannel.POSTS_V2_ALERTS });
            }
            throw err;
        }
    }
}
