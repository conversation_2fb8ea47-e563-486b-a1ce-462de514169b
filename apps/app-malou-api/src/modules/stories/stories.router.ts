import { Router } from 'express';
import { singleton } from 'tsyringe';

import { UpdateStoryPlannedPublicationDateBodyDto, UpdateStoryPlannedPublicationDateParamsDto } from '@malou-io/package-dto';

import { casl } from ':helpers/casl/middlewares';
import { RequestWithPermissions } from ':helpers/utils.types';
import StoriesController from ':modules/stories/stories.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class StoriesRouter {
    private readonly _prefix = '/stories';

    constructor(private readonly _storiesController: StoriesController) {}

    init(router: Router): void {
        router.get(`${this._prefix}`, authorize(), (req, res, next) => this._storiesController.handleGetStoriesByIds(req, res, next));

        router.get(`${this._prefix}/restaurants/:restaurant_id/stories`, authorize(), (req, res, next) =>
            this._storiesController.handleGetStories(req, res, next)
        );

        router.get(`${this._prefix}/restaurants/:restaurant_id/stories-counts`, authorize(), (req, res, next) =>
            this._storiesController.handleGetStoriesCounts(req, res, next)
        );

        router.get(`${this._prefix}/restaurants/:restaurant_id/published-stories-count`, authorize(), (req, res, next) =>
            this._storiesController.handleGetPublishedStoriesCount(req, res, next)
        );

        router.get(`${this._prefix}/to-duplicate`, authorize(), (req, res, next) =>
            this._storiesController.handleGetStoriesToDuplicate(req, res, next)
        );

        router.get(`${this._prefix}/:story_id`, authorize(), (req, res, next) => this._storiesController.handleGetStory(req, res, next));

        router.post(`${this._prefix}`, authorize(), (req, res, next) => this._storiesController.handleCreateStory(req, res, next));

        router.put(
            `${this._prefix}/:story_id/planned-publication-date`,
            authorize(),
            casl(),
            (
                req: RequestWithPermissions<
                    UpdateStoryPlannedPublicationDateParamsDto,
                    never,
                    never,
                    UpdateStoryPlannedPublicationDateBodyDto
                >,
                res,
                next
            ) => this._storiesController.handleUpdateStoryPlannedPublicationDate(req, res, next)
        );

        router.put(`${this._prefix}`, authorize(), (req, res, next) => this._storiesController.handleUpdateStory(req, res, next));

        router.post(`${this._prefix}/delete`, authorize(), (req, res, next) => this._storiesController.handleDeleteStories(req, res, next));

        router.post(`${this._prefix}/poll-status`, authorize(), (req, res, next) =>
            this._storiesController.handlePollingStoriesStatuses(req, res, next)
        );

        router.post(`${this._prefix}/restaurants/:restaurant_id/duplicate`, authorize(), casl(), (req, res, next) =>
            this._storiesController.handleDuplicateStories(req, res, next)
        );
    }
}
