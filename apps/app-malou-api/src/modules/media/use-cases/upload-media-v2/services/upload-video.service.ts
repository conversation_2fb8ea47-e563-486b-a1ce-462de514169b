import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { Readable } from 'node:stream';
import { inject, singleton } from 'tsyringe';

import { IMedia, IMediaStoredObject, newDbId, toDbId } from '@malou-io/package-models';
import { AspectRatio, FileFormat, guessMimeTypeFromExtension, MediaCategory, MediaDimension, MediaType } from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { logger } from ':helpers/logger';
import { MultimediaStreamsInformationService } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service';
import { MultimediaStreamsInformationServiceFfprobeAdapter } from ':helpers/multimedia-streams-information-service/multimedia-streams-information-service-ffprobe-adapter';
import { MediasRepository } from ':modules/media/medias.repository';
import { AwsMediaConvertService } from ':modules/media/use-cases/upload-media-v2/aws-mediaconvert.service';
import { DistantStorageService } from ':services/distant-storage-service/distant-storage-service.interface';
import { AwsS3DistantStorageService } from ':services/distant-storage-service/implementations/aws-s3-distant-storage-service';

@singleton()
export class UploadVideoService {
    constructor(
        private readonly _mediasRepository: MediasRepository,
        @inject(AwsS3DistantStorageService) private readonly _distantStorageService: DistantStorageService,
        private readonly _awsMediaConvertService: AwsMediaConvertService,
        private readonly _agendaSingleton: AgendaSingleton,
        @inject(MultimediaStreamsInformationServiceFfprobeAdapter)
        private readonly _multimediaStreamsInformationService: MultimediaStreamsInformationService
    ) {}

    async execute(params: {
        source: Readable;
        uuid: string;
        restaurantId: string;
        folderId?: string;
        userFileName: string;
        originalMediaId?: string;
        additionalInfo?: {
            videoWidthInPixels: number;
            videoHeightInPixels: number;
            videoDurationInMilliseconds: number;
        };
    }): Promise<Result<{ mediaId: string }, void>> {
        const { source, uuid, restaurantId, folderId, userFileName, originalMediaId, additionalInfo } = params;
        logger.info('[UploadVideoService] Start', { uuid, restaurantId, userFileName });

        const userFileExtension = userFileName.split('.').at(-1);
        const s3SourceKey = `media-upload/${uuid}/source${userFileExtension ? `.${userFileExtension}` : ''}`;
        logger.info('[UploadVideoService] uploading', { s3SourceKey, uuid: uuid });
        const mimeType = guessMimeTypeFromExtension(userFileExtension ?? '');
        await this._distantStorageService.saveFromReadable(s3SourceKey, source, { contentType: mimeType ?? undefined });
        logger.info('[UploadVideoService] uploaded', { s3SourceKey, uuid: uuid });

        if (additionalInfo) {
            logger.info('[UploadVideoService] Got additional info', { additionalInfo });
            const thumbnailsGenerationRes = await this._generateThumbnails({
                uuid,
                s3SourceKey,
                videoWidthInPixels: additionalInfo.videoWidthInPixels,
                videoHeightInPixels: additionalInfo.videoHeightInPixels,
                videoDurationInMilliseconds: additionalInfo.videoDurationInMilliseconds,
            });
            logger.info('[UploadVideoService] Inserting media... (A)', { thumbnailsGenerationRes });
            const media = await this._insertMediaForVideo({
                restaurantId,
                folderId,
                uuid,
                userFileName,
                originalMediaId,
                s3Keys: {
                    source: s3SourceKey,
                    normalized: s3SourceKey,
                    thumbnail1024Outside: thumbnailsGenerationRes.thumbnail1024OutsideKey,
                    thumbnail256Outside: thumbnailsGenerationRes.thumbnail256OutsideKey,
                },
                normalizedDimensions: { width: additionalInfo.videoWidthInPixels, height: additionalInfo.videoHeightInPixels },
                thumbnail1024OutsideDimensions: thumbnailsGenerationRes.thumbnail1024OutsideDimensions,
                thumbnail256OutsideDimensions: thumbnailsGenerationRes.thumbnail256OutsideDimensions,
                timelinePreviewFramesS3Keys: thumbnailsGenerationRes.timelinePreviewFramesKeys,
                durationInMs: additionalInfo.videoDurationInMilliseconds,
            });
            await this._agendaSingleton.now(AgendaJobName.GENERATE_NORMALIZED_VIDEO, { mediaId: media._id.toString() });
            return ok({ mediaId: media._id.toString() });
        }

        logger.info('[UploadVideoService] Empty additional info');

        const publicSourceUrl = await this._distantStorageService.getPublicAccessibleUrl(s3SourceKey);
        const isHdrVideo = await this._multimediaStreamsInformationService.isHdrVideo(publicSourceUrl);
        logger.info('[UploadVideoService] isHdr', { isHdrVideo });

        const normalizeVideoRes = await this.normalizeVideo({ uuid, s3SourceKey, isHdrVideo });
        if (normalizeVideoRes.isErr()) {
            return err(undefined);
        }
        const normalizedVideo = normalizeVideoRes.value;

        logger.info('[UploadVideoService] Generating thumbnails ...', { normalizeVideoRes });
        const thumbnailsGenerationRes = await this._generateThumbnails({
            uuid,
            s3SourceKey: normalizedVideo.normalizedObject.key,
            videoWidthInPixels: normalizedVideo.normalizedWidth,
            videoHeightInPixels: normalizedVideo.normalizedHeight,
            videoDurationInMilliseconds: normalizedVideo.durationInMs,
        });

        logger.info('[UploadVideoService] Inserting media... (B)', { thumbnailsGenerationRes });
        const media = await this._insertMediaForVideo({
            restaurantId,
            folderId,
            uuid,
            userFileName,
            originalMediaId,
            s3Keys: {
                source: s3SourceKey,
                normalized: normalizedVideo.normalizedObject.key,
                thumbnail1024Outside: thumbnailsGenerationRes.thumbnail1024OutsideKey,
                thumbnail256Outside: thumbnailsGenerationRes.thumbnail256OutsideKey,
            },
            normalizedDimensions: { width: normalizedVideo.normalizedWidth, height: normalizedVideo.normalizedHeight },
            thumbnail1024OutsideDimensions: thumbnailsGenerationRes.thumbnail1024OutsideDimensions,
            thumbnail256OutsideDimensions: thumbnailsGenerationRes.thumbnail256OutsideDimensions,
            timelinePreviewFramesS3Keys: thumbnailsGenerationRes.timelinePreviewFramesKeys,
            durationInMs: normalizedVideo.durationInMs,
            isVideoNormalized: true,
        });
        return ok({ mediaId: media._id.toString() });
    }

    async normalizeVideo(params: { uuid: string; s3SourceKey: string; isHdrVideo: boolean }): Promise<
        Result<
            {
                durationInMs: number;
                normalizedObject: IMediaStoredObject;
                normalizedWidth: number;
                normalizedHeight: number;
            },
            void
        >
    > {
        const { uuid, s3SourceKey, isHdrVideo } = params;
        const s3NormalizedKeyPrefix = `media-upload/${uuid}/normalized`;
        const result = await this._awsMediaConvertService.normalizeVideo({
            s3SourceKey,
            s3NormalizedKeyPrefix,
            isHdrVideo,
        });
        if (!result.success) {
            logger.info('[UploadVideoService] video normalization failure', { uuid: uuid, result });
            return err(undefined);
        }
        logger.info('[UploadVideoService] video normalization success', result.value);
        return ok({
            durationInMs: result.value.durationInMs,
            normalizedObject: result.value.storedObject,
            normalizedWidth: result.value.width,
            normalizedHeight: result.value.height,
        });
    }

    private async _generateThumbnails(params: {
        uuid: string;
        s3SourceKey: string;
        videoWidthInPixels: number;
        videoHeightInPixels: number;
        videoDurationInMilliseconds: number;
    }): Promise<{
        thumbnail1024OutsideKey: string;
        thumbnail1024OutsideDimensions: MediaDimension;
        thumbnail256OutsideKey: string;
        thumbnail256OutsideDimensions: MediaDimension;
        timelinePreviewFramesKeys: string[];
    }> {
        const { uuid, s3SourceKey, videoWidthInPixels, videoHeightInPixels, videoDurationInMilliseconds } = params;

        const result = await this._awsMediaConvertService.generateVideoThumbnails(
            {
                s3Key: s3SourceKey,
                durationInMilliseconds: videoDurationInMilliseconds,
                widthInPixels: videoWidthInPixels,
                heightInPixels: videoHeightInPixels,
            },
            [
                {
                    maxDimensionsPx: 1024,
                    s3OutputKeyPrefix: `media-upload/${uuid}/thumbnail-1024`,
                    capturesCount: 1,
                },
                {
                    maxDimensionsPx: 256,
                    s3OutputKeyPrefix: `media-upload/${uuid}/thumbnail-256`,
                    capturesCount: 1,
                },
                {
                    maxDimensionsPx: 256,
                    s3OutputKeyPrefix: `media-upload/${uuid}/timeline_preview_256`,
                    capturesCount: 5,
                },
            ]
        );
        assert(result.isOk());
        assert(result.value.length === 3);
        const thumbnail1024Outside = result.value[0];
        const thumbnail256Outside = result.value[1];
        const timelinePreviewFrames = result.value[2];

        const thumbnail1024OutsideKey = thumbnail1024Outside.s3keys[0];
        const thumbnail1024OutsideDimensions = { width: thumbnail1024Outside.width, height: thumbnail1024Outside.height };
        const thumbnail256OutsideKey = thumbnail256Outside.s3keys[0];
        const thumbnail256OutsideDimensions = { width: thumbnail256Outside.width, height: thumbnail256Outside.height };
        const timelinePreviewFramesKeys = timelinePreviewFrames.s3keys;

        // these variables have been set above
        assert(thumbnail1024OutsideKey);
        assert(thumbnail1024OutsideDimensions);
        assert(thumbnail256OutsideKey);
        assert(thumbnail256OutsideDimensions);
        assert(timelinePreviewFramesKeys);

        return {
            thumbnail1024OutsideKey,
            thumbnail1024OutsideDimensions,
            thumbnail256OutsideKey,
            thumbnail256OutsideDimensions,
            timelinePreviewFramesKeys,
        };
    }

    private async _insertMediaForVideo(params: {
        restaurantId: string;
        folderId?: string;
        uuid: string;
        userFileName: string;
        originalMediaId?: string;
        s3Keys: {
            source: string;
            normalized: string;
            thumbnail1024Outside: string;
            thumbnail256Outside: string;
        };
        normalizedDimensions: MediaDimension;
        thumbnail1024OutsideDimensions: MediaDimension;
        thumbnail256OutsideDimensions: MediaDimension;
        timelinePreviewFramesS3Keys: string[];
        durationInMs: number;
        isVideoNormalized?: boolean;
    }): Promise<IMedia> {
        const id = newDbId();
        logger.info('[UploadMediaV2UseCase] _insertMediaForVideo', { ...params, id });

        const s3PublicUrls = Object.fromEntries(
            await Promise.all(
                Object.entries(params.s3Keys).map(async ([name, s3Key]) => [
                    name,
                    await this._distantStorageService.getPublicAccessibleUrl(s3Key),
                ])
            )
        );

        const timelinePreviewFrames256h = await Promise.all(
            params.timelinePreviewFramesS3Keys.map(async (key) => ({
                key,
                publicUrl: await this._distantStorageService.getPublicAccessibleUrl(key),
                provider: 'S3' as const,
            }))
        );

        const sourceObjectAttributes = await this._distantStorageService.getObjectAttributes(params.s3Keys.source);
        assert(sourceObjectAttributes, `the object ${JSON.stringify(params.s3Keys.source)} must exist`);

        const aspectRatio = params.normalizedDimensions.width / params.normalizedDimensions.height;
        // Default values, as they will be overridden later when added to the post (and will depend of the publication type)
        const transformData = {
            aspectRatio: AspectRatio.ORIGINAL,
            rotationInDegrees: 0,
            left: 0,
            top: 0,
            width: 1,
            height: 1,
        };

        const mediaInput: IMedia = {
            type: MediaType.VIDEO,
            format: FileFormat.MP4,
            _id: id,
            category: MediaCategory.ADDITIONAL,
            socialId: id.toString(),
            createdAt: new Date(),
            updatedAt: new Date(),
            storedObjects: {
                original: {
                    key: params.s3Keys.source,
                    publicUrl: s3PublicUrls.source,
                    provider: 'S3',
                },
                normalized: {
                    key: params.s3Keys.normalized,
                    publicUrl: s3PublicUrls.normalized,
                    provider: 'S3',
                },
                thumbnail1024Outside: {
                    key: params.s3Keys.thumbnail1024Outside,
                    publicUrl: s3PublicUrls.thumbnail1024Outside,
                    provider: 'S3',
                },
                thumbnail256Outside: {
                    key: params.s3Keys.thumbnail256Outside,
                    publicUrl: s3PublicUrls.thumbnail256Outside,
                    provider: 'S3',
                },
            },
            dimensions: {
                normalized: params.normalizedDimensions,
                original: params.normalizedDimensions,
                thumbnail1024Outside: params.thumbnail1024OutsideDimensions,
                thumbnail256Outside: params.thumbnail256OutsideDimensions,
            },
            restaurantId: toDbId(params.restaurantId),
            name: params.userFileName,
            aspectRatio,
            transformData,
            isV2: true,
            urls: {
                original: s3PublicUrls.normalized,
            },
            sizes: {},
            timelinePreviewFrames256h,
            duration: params.durationInMs / 1000,
            isVideoNormalized: params.isVideoNormalized,
            folderId: params.folderId === undefined ? undefined : toDbId(params.folderId),

            originalMediaId: params.originalMediaId ? toDbId(params.originalMediaId) : undefined,
        };
        return await this._mediasRepository.create({ data: mediaInput, options: { lean: true } });
    }
}
