import { RemoveMethodsFromClass } from '@malou-io/package-utils';

export type OrganizationProps = RemoveMethodsFromClass<Organization>;

export class Organization {
    id: string;
    name: string;
    subscriptionsProviderId?: string;
    verifiedEmailsForCampaigns?: string[];
    createdAt?: Date;
    updatedAt?: Date;

    constructor(organization: OrganizationProps) {
        this.id = organization.id;
        this.name = organization.name;
        this.subscriptionsProviderId = organization.subscriptionsProviderId;
        this.verifiedEmailsForCampaigns = organization.verifiedEmailsForCampaigns;
        this.createdAt = organization.createdAt;
        this.updatedAt = organization.updatedAt;
    }

    toDto(): OrganizationProps {
        return {
            id: this.id,
            name: this.name,
            subscriptionsProviderId: this.subscriptionsProviderId,
            verifiedEmailsForCampaigns: this.verifiedEmailsForCampaigns,
            createdAt: this.createdAt,
            updatedAt: this.updatedAt,
        };
    }
}
