export interface SubscriptionProviderLocation {
    id: string;
    name: string;
    placeId: string | null;
    malouRestaurantId: string | null;
    isBrandAccount: boolean;
}

export interface GetLocationsByOrganizationRequest {
    organizationProviderId: string;
}

export interface GetLocationsByOrganizationResponse {
    locations: SubscriptionProviderLocation[];
}

export interface UpdateSubscriptionsProviderLocationRequest {
    subscriptionsProviderLocationId: string;
    malouRestaurantId?: string | null;
}
