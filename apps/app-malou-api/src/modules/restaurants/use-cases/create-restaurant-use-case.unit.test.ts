import 'reflect-metadata';

import { container } from 'tsyringe';

import { CreateRestaurantBodyDto } from '@malou-io/package-dto';
import { newDbId, toDbId } from '@malou-io/package-models';
import { BusinessCategory, MalouErrorCode, PlatformKey, Role } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import ':helpers/tests/testing-utils';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import * as utils from ':helpers/utils';
import LabelsUseCases from ':modules/labels/labels.use-cases';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import PlatformInsightsRepository from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import { KeywordSearchImpressionsService } from ':modules/platforms/services/keyword-search-impressions/keyword-search-impressions.service';
import ReportsRepository from ':modules/reports/reports.repository';
import { RestaurantAiSettingsRepository } from ':modules/restaurant-ai-settings/restaurant-ai-settings.repository';
import { RestaurantsDtoMapper } from ':modules/restaurants/mappers/restaurants.dto-mapper';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import RestaurantsUseCases from ':modules/restaurants/restaurants.use-cases';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import YextActivateLocationUseCase from ':modules/restaurants/use-cases/activate-yext-location.use-case';
import AddDefaultReviewAutomationsUseCase from ':modules/restaurants/use-cases/add-default-review-automations.use-case';
import { CreateRestaurantUseCase } from ':modules/restaurants/use-cases/create-restaurant.use-case';
import { getDefaultUser } from ':modules/users/tests/user.builder';
import UsersUseCases from ':modules/users/users.use-cases';

describe('CreateRestaurantUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'UsersRepository', 'OrganizationsRepository']);
    });

    // Mock classes for dependencies
    class MockPlatformsUseCases {
        async getLocationDataForPlatform() {
            return {
                name: 'Test Restaurant',
                socialId: 'test-social-id',
                address: { formattedAddress: 'Test Address' },
            };
        }

        async initializePlatform() {
            return {
                _id: newDbId(),
                rating: 4.5,
                socialId: 'test-social-id',
                key: PlatformKey.GMB,
            };
        }
    }

    class MockRestaurantsUseCases {
        async userCanCreate() {
            return true;
        }

        async upsertRestaurant() {
            return getDefaultRestaurant()._id(newDbId()).build();
        }

        async addRestaurantForUser() {
            return {};
        }

        async addRestaurantForAppAdmin() {
            return {};
        }
    }

    class MockLabelsUseCases {
        async initDefaultLabels() {
            return {};
        }
    }

    class MockUsersUseCases {
        async forceExpireAbilitySession() {
            return {};
        }
    }

    class MockYextActivateLocationUseCase {
        async execute() {
            return {};
        }
    }

    class MockAddDefaultReviewAutomationsUseCase {
        async execute() {
            return {};
        }
    }

    class MockRestaurantsDtoMapper {
        toCreateRestaurantDto(restaurant: any) {
            return {
                _id: restaurant._id.toString(),
                name: restaurant.name,
            };
        }
    }

    class MockKeywordSearchImpressionsService {
        createMessageQueueToFetchMonthlyKeywordSearchImpressions() {
            return Promise.resolve();
        }
    }

    // Mock repositories
    class MockPlatformInsightsRepository {
        async upsert() {
            return {};
        }
    }

    class MockReportsRepository {
        async addRestaurantForUser() {
            return {};
        }
    }

    class MockRestaurantAiSettingsRepository {
        async createDefaultRestaurantAiSettings() {
            return {};
        }
    }

    class MockPlatformsRepository {
        async upsert() {
            return {};
        }
    }

    class PlatformsUseCasesMock {
        async getLocationDataForPlatform() {
            return {
                name: 'locations/12762326605118745782',
                languageCode: 'fr',
                storeCode: '12434359678080288763',
                title: 'Les années folles',
                phoneNumbers: {
                    primaryPhone: '06 11 65 60 21',
                },
                categories: {
                    primaryCategory: {
                        name: 'categories/gcid:catering_service',
                        displayName: 'Traiteur',
                        serviceTypes: [
                            {
                                serviceTypeId: 'job_type_id:barbecue_catering',
                                displayName: 'Traiteur pour barbecues',
                            },
                            {
                                serviceTypeId: 'job_type_id:brunch_catering',
                                displayName: 'Traiteur pour brunch',
                            },
                            {
                                serviceTypeId: 'job_type_id:buffet_catering',
                                displayName: 'Traiteur pour buffets',
                            },
                            {
                                serviceTypeId: 'job_type_id:corporate_catering',
                                displayName: 'Traiteur pour entreprises',
                            },
                            {
                                serviceTypeId: 'job_type_id:dinner_catering',
                                displayName: 'Traiteur pour dîner',
                            },
                            {
                                serviceTypeId: 'job_type_id:event_catering',
                                displayName: 'Traiteur événementiel',
                            },
                            {
                                serviceTypeId: 'job_type_id:graduation_catering',
                                displayName: 'Traiteur pour fête de remise de diplômes',
                            },
                            {
                                serviceTypeId: 'job_type_id:party_catering',
                                displayName: 'Traiteur',
                            },
                            {
                                serviceTypeId: 'job_type_id:personal_chef_catering',
                                displayName: 'Chef traiteur personnel',
                            },
                            {
                                serviceTypeId: 'job_type_id:private_catering',
                                displayName: 'Traiteur privé',
                            },
                            {
                                serviceTypeId: 'job_type_id:wedding_catering',
                                displayName: 'Traiteur pour mariage',
                            },
                        ],
                        moreHoursTypes: [
                            {
                                hoursTypeId: 'ACCESS',
                                displayName: 'Access',
                                localizedDisplayName: 'Accès',
                            },
                            {
                                hoursTypeId: 'BREAKFAST',
                                displayName: 'Breakfast',
                                localizedDisplayName: 'Petit-déjeuner',
                            },
                            {
                                hoursTypeId: 'BRUNCH',
                                displayName: 'Brunch',
                                localizedDisplayName: 'Brunch',
                            },
                            {
                                hoursTypeId: 'DELIVERY',
                                displayName: 'Delivery',
                                localizedDisplayName: 'Livraison',
                            },
                            {
                                hoursTypeId: 'DINNER',
                                displayName: 'Dinner',
                                localizedDisplayName: 'Dîner',
                            },
                            {
                                hoursTypeId: 'DRIVE_THROUGH',
                                displayName: 'Drive through',
                                localizedDisplayName: 'Service de drive',
                            },
                            {
                                hoursTypeId: 'HAPPY_HOUR',
                                displayName: 'Happy hours',
                                localizedDisplayName: 'Happy hours',
                            },
                            {
                                hoursTypeId: 'KITCHEN',
                                displayName: 'Kitchen',
                                localizedDisplayName: 'Cuisine',
                            },
                            {
                                hoursTypeId: 'LUNCH',
                                displayName: 'Lunch',
                                localizedDisplayName: 'Déjeuner',
                            },
                            {
                                hoursTypeId: 'ONLINE_SERVICE_HOURS',
                                displayName: 'Online service hours',
                                localizedDisplayName: 'Service en ligne',
                            },
                            {
                                hoursTypeId: 'PICKUP',
                                displayName: 'Pickup',
                                localizedDisplayName: 'Retrait',
                            },
                            {
                                hoursTypeId: 'TAKEOUT',
                                displayName: 'Takeout',
                                localizedDisplayName: 'Vente à emporter',
                            },
                            {
                                hoursTypeId: 'SENIOR_HOURS',
                                displayName: 'Senior hours',
                                localizedDisplayName: 'Horaires pour les seniors',
                            },
                            {
                                hoursTypeId: 'PRAYER',
                                displayName: 'Prayer',
                                localizedDisplayName: 'Prière',
                            },
                            {
                                hoursTypeId: 'CATHOLIC_MASS',
                                displayName: 'Mass',
                                localizedDisplayName: 'Messe',
                            },
                            {
                                hoursTypeId: 'CATHOLIC_CONFESSION',
                                displayName: 'Confession',
                                localizedDisplayName: 'Confession',
                            },
                            {
                                hoursTypeId: 'CATHOLIC_ADORATION',
                                displayName: 'Adoration',
                                localizedDisplayName: 'Adoration',
                            },
                            {
                                hoursTypeId: 'CHRISTIAN_WORSHIP_SERVICE',
                                displayName: 'Worship service',
                                localizedDisplayName: 'Service de culte',
                            },
                            {
                                hoursTypeId: 'ISLAM_JUMMAH',
                                displayName: 'Jummah',
                                localizedDisplayName: 'Prière du vendredi',
                            },
                            {
                                hoursTypeId: 'JUDAISM_SABBATH',
                                displayName: 'Sabbath',
                                localizedDisplayName: 'Sabbat',
                            },
                        ],
                    },
                },
                storefrontAddress: {
                    regionCode: 'FR',
                    languageCode: 'fr',
                    postalCode: '94600',
                    locality: 'Choisy-le-Roi',
                    addressLines: ['8 Rue Ledru Rollin'],
                },
                regularHours: {
                    periods: [
                        {
                            openDay: 'MONDAY',
                            openTime: {
                                hours: 7,
                            },
                            closeDay: 'MONDAY',
                            closeTime: {
                                hours: 8,
                            },
                        },
                    ],
                },
                openInfo: {
                    status: 'OPEN',
                    canReopen: true,
                },
                metadata: {
                    canDelete: true,
                    canModifyServiceList: true,
                    canHaveFoodMenus: true,
                    placeId: 'ChIJkcDLti115kcRHo16Cz2vBWU',
                    mapsUri: 'https://maps.google.com/maps?cid=7279417049434721566',
                    newReviewUri: 'https://search.google.com/local/writereview?placeid=ChIJkcDLti115kcRHo16Cz2vBWU',
                    hasVoiceOfMerchant: true,
                },
                profile: {
                    description: 'Traiteur de père en fils depuis 1954, passionné de cuisine française',
                },
                serviceItems: [
                    {
                        structuredServiceItem: {
                            serviceTypeId: 'job_type_id:personal_chef_catering',
                        },
                    },
                    {
                        structuredServiceItem: {
                            serviceTypeId: 'job_type_id:party_catering',
                        },
                    },
                ],
            };
        }

        async initializePlatform() {
            return {
                rating: 5,
                socialId: 'ChIJkcDLti112kcRHo16Cz2vBWU',
                key: PlatformKey.GMB,
            };
        }
    }

    class YextActivateLocationUseCaseMock {
        async execute() {
            return {};
        }
    }

    beforeEach(() => {
        // Register all mock dependencies
        container.register(PlatformsUseCases, { useValue: new MockPlatformsUseCases() as any });
        container.register(RestaurantsUseCases, { useValue: new MockRestaurantsUseCases() as any });
        container.register(LabelsUseCases, { useValue: new MockLabelsUseCases() as any });
        container.register(UsersUseCases, { useValue: new MockUsersUseCases() as any });
        container.register(YextActivateLocationUseCase, { useValue: new MockYextActivateLocationUseCase() as any });
        container.register(AddDefaultReviewAutomationsUseCase, { useValue: new MockAddDefaultReviewAutomationsUseCase() as any });
        container.register(RestaurantsDtoMapper, { useValue: new MockRestaurantsDtoMapper() as any });
        container.register(KeywordSearchImpressionsService, { useValue: new MockKeywordSearchImpressionsService() as any });
        container.register(PlatformInsightsRepository, { useValue: new MockPlatformInsightsRepository() as any });
        container.register(ReportsRepository, { useValue: new MockReportsRepository() as any });
        container.register(RestaurantAiSettingsRepository, { useValue: new MockRestaurantAiSettingsRepository() as any });
        container.register(PlatformsRepository, { useValue: new MockPlatformsRepository() as any });
    });

    describe('Hyperline Integration', () => {
        it('should delete restaurant and throw error when subscriptionsProvider fails', async () => {
            // Mock subscriptionsProvider that fails
            const mockSubscriptionsProvider = {
                updateSubscriptionsProviderLocation: jest.fn().mockRejectedValue(new Error('Hyperline API failed')),
            };

            // Mock restaurants repository with spy methods
            const mockRestaurantsRepository = {
                updateOne: jest.fn().mockResolvedValue({}),
                deleteOne: jest.fn().mockResolvedValue({}),
                findOneAndUpdate: jest.fn().mockResolvedValue({}),
            };

            container.register(InjectionToken.SubscriptionsProvider, { useValue: mockSubscriptionsProvider });
            container.register(RestaurantsRepository, { useValue: mockRestaurantsRepository as any });

            const createRestaurantUseCase = container.resolve(CreateRestaurantUseCase);

            const testCase = new TestCaseBuilderV2<'users'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const userId = seededObjects.users[0]._id.toString();

            const payload = {
                userId,
                type: BusinessCategory.LOCAL_BUSINESS,
                name: 'Test Restaurant',
                socialId: 'test-place-id',
                credentialId: newDbId().toString(),
                organizationId: newDbId().toString(),
                fromForm: true,
                fromTests: true,
                subscriptionsProviderId: 'test-hyperline-id',
                access: { isValid: true, missing: [] },
                address: {
                    country: 'FR',
                    regionCode: 'FR',
                    formattedAddress: 'Test Address',
                    locality: 'Paris',
                    postalCode: '75001',
                },
                formattedAddress: 'Test Address',
                locationId: 'test-location-id',
                apiEndpointV2: 'https://api.test.com',
                socialUrl: 'https://test.com',
                accountId: 'test-account-id',
                accountName: 'Test Account',
            } as CreateRestaurantBodyDto & { userId: string };

            await expect(createRestaurantUseCase.execute(payload)).rejects.toThrow(
                expect.objectContaining({
                    malouErrorCode: MalouErrorCode.HYPERLINE_INTEGRATION_ERROR,
                    message: 'Hyperline integration failed',
                })
            );

            // Verify that updateSubscriptionsProviderLocation was called
            expect(mockSubscriptionsProvider.updateSubscriptionsProviderLocation).toHaveBeenCalledWith({
                subscriptionsProviderLocationId: 'test-hyperline-id',
                malouRestaurantId: expect.any(String),
            });

            // Verify that restaurant was deleted after failure
            expect(mockRestaurantsRepository.deleteOne).toHaveBeenCalledWith({
                filter: { _id: expect.any(Object) },
            });

            // Verify that updateOne was NOT called for subscriptionsProviderId (since the subscriptionsProvider failed first)
            expect(mockRestaurantsRepository.updateOne).not.toHaveBeenCalled();
        });

        it('should set subscriptionsProviderId when subscriptionsProvider succeeds', async () => {
            // Mock subscriptionsProvider that succeeds
            const mockSubscriptionsProvider = {
                updateSubscriptionsProviderLocation: jest.fn().mockResolvedValue(undefined),
            };

            // Mock restaurants repository with spy methods
            const mockRestaurantsRepository = {
                updateOne: jest.fn().mockResolvedValue({}),
                deleteOne: jest.fn().mockResolvedValue({}),
                findOneAndUpdate: jest.fn().mockResolvedValue({}),
            };

            container.register(InjectionToken.SubscriptionsProvider, { useValue: mockSubscriptionsProvider });
            container.register(RestaurantsRepository, { useValue: mockRestaurantsRepository as any });

            const createRestaurantUseCase = container.resolve(CreateRestaurantUseCase);

            const testCase = new TestCaseBuilderV2<'users'>({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().build()];
                        },
                    },
                },
            });
            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const userId = seededObjects.users[0]._id.toString();

            const payload = {
                userId,
                type: BusinessCategory.LOCAL_BUSINESS,
                name: 'Test Restaurant',
                socialId: 'test-place-id',
                credentialId: newDbId().toString(),
                organizationId: newDbId().toString(),
                fromForm: true,
                fromTests: true,
                subscriptionsProviderId: 'test-hyperline-id',
                access: { isValid: true, missing: [] },
                address: {
                    country: 'FR',
                    regionCode: 'FR',
                    formattedAddress: 'Test Address',
                    locality: 'Paris',
                    postalCode: '75001',
                },
                formattedAddress: 'Test Address',
                locationId: 'test-location-id',
                apiEndpointV2: 'https://api.test.com',
                socialUrl: 'https://test.com',
                accountId: 'test-account-id',
                accountName: 'Test Account',
            } as CreateRestaurantBodyDto & { userId: string };

            const result = await createRestaurantUseCase.execute(payload);

            // Verify that updateSubscriptionsProviderLocation was called
            expect(mockSubscriptionsProvider.updateSubscriptionsProviderLocation).toHaveBeenCalledWith({
                subscriptionsProviderLocationId: 'test-hyperline-id',
                malouRestaurantId: expect.any(String),
            });

            // Verify that restaurant was updated with subscriptionsProviderId
            expect(mockRestaurantsRepository.updateOne).toHaveBeenCalledWith({
                filter: { _id: expect.any(Object) },
                update: { subscriptionsProviderId: 'test-hyperline-id' },
            });

            // Verify that restaurant was NOT deleted (success scenario)
            expect(mockRestaurantsRepository.deleteOne).not.toHaveBeenCalled();

            // Verify that the result is returned
            expect(result).toBeDefined();
            expect(result).toHaveProperty('_id');
        });

        it('should create default restaurant ai settings', async () => {
            container.clearInstances();
            registerRepositories(['RestaurantsRepository', 'UsersRepository']);
            jest.spyOn(utils, 'getGeolocationForRestaurant').mockResolvedValue({
                latitude: 48.763,
                longitude: 2.414,
            });

            const testcase = new TestCaseBuilderV2({
                seeds: {
                    users: {
                        data() {
                            return [getDefaultUser().role(Role.ADMIN).build()];
                        },
                    },
                },
                expectedResult: undefined,
            });

            await testcase.build();

            const seeds = testcase.getSeededObjects();

            const userId = seeds.users[0]._id;

            const mockOrganizationsRepository = {
                findOneOrFail: jest.fn().mockResolvedValue({
                    _id: newDbId(),
                    name: 'Test Organization',
                    subscriptionsProviderId: 'test-hyperline-id',
                }),
            };
            container.register(OrganizationsRepository, {
                useValue: mockOrganizationsRepository as any,
            });
            container.register(PlatformsUseCases, {
                useValue: new PlatformsUseCasesMock() as any,
            });
            container.register(YextActivateLocationUseCase, {
                useClass: YextActivateLocationUseCaseMock as any,
            });

            const restaurantAiSettingsRepository = container.resolve(RestaurantAiSettingsRepository);
            const createRestaurantUseCase = container.resolve(CreateRestaurantUseCase);

            const response = await createRestaurantUseCase.execute({
                socialId: 'ChIJkcDLti115kcRHo16Cz2vBWU',
                name: 'Les années folles',
                formattedAddress: '8 Rue Ledru Rollin 94600 Choisy-le-Roi France',
                address: {
                    regionCode: 'FR',
                    country: 'France',
                    postalCode: '94600',
                    locality: 'Choisy-le-Roi',
                    formattedAddress: '8 Rue Ledru Rollin',
                },
                socialUrl: 'https://search.google.com/local/writereview?placeid=ChIJkcDLti115kcRHo16Cz2vBWU',
                accountId: 'accounts/116158410140145283722',
                accountName: 'Fantin Raimbault',
                access: {
                    isValid: true,
                    missing: [],
                },
                apiEndpointV2: 'locations/12762326605118745782',
                locationId: '12762326605118745782',
                type: BusinessCategory.LOCAL_BUSINESS,
                credentialId: '60c87b7203fd132f44c955d3',
                organizationId: '674733e2d5b8191b408d92b3',
                userId: userId.toString(),
            });

            const settings = await restaurantAiSettingsRepository.findOne({
                filter: {
                    restaurantId: toDbId(response.restaurantId),
                },
                options: {
                    lean: true,
                },
            });

            expect(settings).toBeDefined();
            expect(settings?.postSettings.seo.isDefaultPostSettings).toBe(true);
            expect(settings?.postSettings.social.isDefaultPostSettings).toBe(true);
        });
    });
});
