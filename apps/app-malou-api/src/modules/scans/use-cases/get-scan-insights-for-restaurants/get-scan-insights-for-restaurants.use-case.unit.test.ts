import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';
import { MalouComparisonPeriod, WheelOfFortuneRedirectionPlatformKey } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';
import { MOCKED_NOW_DATE_TIME } from ':modules/roi/tests/roi.constants';
import { getDefaultNfcSnapshot, getDefaultScan } from ':modules/scans/tests/scans.builder';
import { GetScanInsightsForRestaurantsUseCase } from ':modules/scans/use-cases/get-scan-insights-for-restaurants/get-scan-insights-for-restaurants.use-case';

describe('GetScanInsightsForRestaurantsUseCase', () => {
    let restaurantId1: DbId;
    let restaurantId2: DbId;
    let restaurantId3: DbId;
    let nfcId1: DbId;
    let nfcId2: DbId;
    let nfcId3: DbId;
    let reviewId1: DbId;
    let reviewId2: DbId;
    let reviewId3: DbId;
    let reviewId4: DbId;
    let reviewId5: DbId;

    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'NfcsRepository', 'ScansRepository', 'ReviewsRepository']);
        const mockDateNow = jest.fn(() => MOCKED_NOW_DATE_TIME);
        global.Date.now = mockDateNow;
    });

    beforeEach(() => {
        restaurantId1 = newDbId();
        restaurantId2 = newDbId();
        restaurantId3 = newDbId();
        nfcId1 = newDbId();
        nfcId2 = newDbId();
        nfcId3 = newDbId();
        reviewId1 = newDbId();
        reviewId2 = newDbId();
        reviewId3 = newDbId();
        reviewId4 = newDbId();
        reviewId5 = newDbId();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should return correct data format', async () => {
        const getScanInsightsUseCase = container.resolve(GetScanInsightsForRestaurantsUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'scans'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId3)
                                .name('restaurant_3')
                                .internalName('restaurant_3')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsUseCase.execute({
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),
            restaurantIds: [restaurantId1.toString(), restaurantId2.toString(), restaurantId3.toString()],
            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });

        expect(result).toMatchObject({
            scansEvolution: expect.any(Number),
            wofScansEvolution: expect.any(Number),
            scans: expect.arrayContaining([
                expect.objectContaining({
                    nfcSnapshot: expect.objectContaining({
                        restaurantId: expect.any(String),
                        chipName: expect.any(String),
                        platformKey: expect.any(String),
                        name: expect.any(String),
                        isRedirectingToWof: expect.any(Boolean),
                    }),
                }),
            ]),
            estimatedReviewCountPerRestaurant: expect.objectContaining({
                [restaurantId1.toString()]: {
                    estimatedReviewCount: expect.any(Number),
                    estimatedReviewCountDifferenceWithPreviousPeriod: expect.any(Number),
                },
                [restaurantId2.toString()]: {
                    estimatedReviewCount: expect.any(Number),
                    estimatedReviewCountDifferenceWithPreviousPeriod: expect.any(Number),
                },
                [restaurantId3.toString()]: {
                    estimatedReviewCount: expect.any(Number),
                    estimatedReviewCountDifferenceWithPreviousPeriod: expect.any(Number),
                },
            }),
            totemReviewsPerRestaurant: expect.objectContaining({
                reviewCountDifferenceWithPreviousPeriod: expect.any(Number),
                privateReviewCountDifferenceWithPreviousPeriod: expect.any(Number),
                reviewsPerPlatform: [],
                privateReviewsPerRating: [],
            }),
        });
    });

    it('should not contain wheel of fortune scans', async () => {
        const getScanInsightsUseCase = container.resolve(GetScanInsightsForRestaurantsUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'scans' | 'reviews'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId3)
                                .name('restaurant_3')
                                .internalName('restaurant_3')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()._id(reviewId1).restaurantId(restaurantId1).rating(5).build(),
                            getDefaultReview()._id(reviewId2).restaurantId(restaurantId1).rating(4).build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .matchedReviewId(reviewId1)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .matchedReviewId(reviewId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsUseCase.execute({
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),
            restaurantIds: [restaurantId1.toString(), restaurantId2.toString(), restaurantId3.toString()],
            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });

        expect(result.scansEvolution).toBe(3);
        expect(result.wofScansEvolution).toBe(0);
        expect(result.scans.length).toBe(3);
        expect(result.estimatedReviewCountPerRestaurant).toMatchObject({
            [restaurantId1.toString()]: {
                estimatedReviewCount: 0,
                estimatedReviewCountDifferenceWithPreviousPeriod: 0,
            },
            [restaurantId2.toString()]: {
                estimatedReviewCount: 0,
                estimatedReviewCountDifferenceWithPreviousPeriod: 0,
            },
        });
        expect(result.totemReviewsPerRestaurant).toMatchObject({
            reviewCountDifferenceWithPreviousPeriod: 2,
            privateReviewCountDifferenceWithPreviousPeriod: 0,
            reviewsPerPlatform: expect.arrayContaining([
                expect.objectContaining({
                    key: expect.any(String),
                    reviewsCount: expect.arrayContaining([
                        expect.objectContaining({
                            count: expect.any(Number),
                            restaurantId: expect.any(String),
                        }),
                    ]),
                }),
            ]),
            privateReviewsPerRating: [],
        });

        const lightReviews = testCase.getSeededObjects().reviews.map((review) => ({
            id: review._id.toString(),
            rating: review.rating,
            key: review.key,
            restaurantId: (review.restaurantId as DbId).toString(),
        }));

        // Check that reviews for restaurant1 are counted in the reviewsPerPlatform
        const restaurant1Reviews = lightReviews.filter((review) => review.restaurantId === restaurantId1.toString());
        const platformKeys = [...new Set(restaurant1Reviews.map((review) => review.key))];

        platformKeys.forEach((platformKey) => {
            const platformReviewsCount = result.totemReviewsPerRestaurant.reviewsPerPlatform
                .find((platform) => platform.key === platformKey)
                ?.reviewsCount.find((count) => count.restaurantId === restaurantId1.toString())?.count;

            const expectedCount = restaurant1Reviews.filter((review) => review.key === platformKey).length;
            expect(platformReviewsCount).toBe(expectedCount);
        });
    });

    it('should return previous period scans', async () => {
        const getScanInsightsUseCase = container.resolve(GetScanInsightsForRestaurantsUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'scans' | 'reviews'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId3)
                                .name('restaurant_3')
                                .internalName('restaurant_3')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()._id(reviewId1).restaurantId(restaurantId1).rating(5).build(),
                            getDefaultReview()._id(reviewId2).restaurantId(restaurantId2).rating(4).build(),
                            getDefaultReview()._id(reviewId3).restaurantId(restaurantId1).rating(5).build(),
                            getDefaultReview()._id(reviewId4).restaurantId(restaurantId1).rating(4).build(),
                            getDefaultReview()._id(reviewId5).restaurantId(restaurantId2).rating(5).build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .matchedReviewId(reviewId1)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .matchedReviewId(reviewId2)
                                .build(),
                            getDefaultScan()
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 15 }).toJSDate())
                                .matchedReviewId(reviewId3)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .matchedReviewId(reviewId4)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 5 }).toJSDate())
                                .matchedReviewId(reviewId5)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsUseCase.execute({
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),
            restaurantIds: [restaurantId1.toString(), restaurantId2.toString(), restaurantId3.toString()],
            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });

        expect(result.scansEvolution).toBe(-2);
        expect(result.wofScansEvolution).toBe(0);
        expect(result.scans.length).toBe(3);
        expect(result.estimatedReviewCountPerRestaurant).toMatchObject({
            [restaurantId1.toString()]: {
                estimatedReviewCount: 0,
                estimatedReviewCountDifferenceWithPreviousPeriod: 0,
            },
            [restaurantId2.toString()]: {
                estimatedReviewCount: 0,
                estimatedReviewCountDifferenceWithPreviousPeriod: 0,
            },
            [restaurantId3.toString()]: {
                estimatedReviewCount: 0,
                estimatedReviewCountDifferenceWithPreviousPeriod: 0,
            },
        });
        expect(result.totemReviewsPerRestaurant).toMatchObject({
            reviewCountDifferenceWithPreviousPeriod: -1,
            privateReviewCountDifferenceWithPreviousPeriod: 0,
            reviewsPerPlatform: expect.arrayContaining([
                expect.objectContaining({
                    key: expect.any(String),
                    reviewsCount: expect.arrayContaining([
                        expect.objectContaining({
                            count: expect.any(Number),
                            restaurantId: expect.any(String),
                        }),
                    ]),
                }),
            ]),
            privateReviewsPerRating: [],
        });

        const lightReviews = testCase.getSeededObjects().reviews.map((review) => ({
            id: review._id.toString(),
            rating: review.rating,
            key: review.key,
            restaurantId: (review.restaurantId as DbId).toString(),
        }));

        // Check that the correct reviews are counted in reviewsPerPlatform
        const restaurant1Review = lightReviews.find((review) => review.id === reviewId1.toString());
        const restaurant2Review = lightReviews.find((review) => review.id === reviewId2.toString());

        if (restaurant1Review) {
            const platformReviewsCount = result.totemReviewsPerRestaurant.reviewsPerPlatform
                .find((platform) => platform.key === restaurant1Review.key)
                ?.reviewsCount.find((count) => count.restaurantId === restaurantId1.toString())?.count;

            expect(platformReviewsCount).toBe(1);
        }

        if (restaurant2Review) {
            const platformReviewsCount = result.totemReviewsPerRestaurant.reviewsPerPlatform
                .find((platform) => platform.key === restaurant2Review.key)
                ?.reviewsCount.find((count) => count.restaurantId === restaurantId2.toString())?.count;

            expect(platformReviewsCount).toBe(1);
        }
    });

    it('should return WoF previous period scans', async () => {
        const getScanInsightsUseCase = container.resolve(GetScanInsightsForRestaurantsUseCase);

        const testCase = new TestCaseBuilderV2<'restaurants' | 'scans' | 'reviews'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant()
                                ._id(restaurantId1)
                                .name('restaurant_1')
                                .internalName('restaurant_1')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId2)
                                .name('restaurant_2')
                                .internalName('restaurant_2')
                                .active(true)
                                .build(),
                            getDefaultRestaurant()
                                ._id(restaurantId3)
                                .name('restaurant_3')
                                .internalName('restaurant_3')
                                .active(true)
                                .build(),
                        ];
                    },
                },
                reviews: {
                    data() {
                        return [
                            getDefaultReview()._id(reviewId1).restaurantId(restaurantId2).rating(5).build(),
                            getDefaultReview()._id(reviewId2).restaurantId(restaurantId2).rating(4).build(),
                        ];
                    },
                },
                scans: {
                    data() {
                        return [
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 15 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId3).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .matchedReviewId(reviewId1)
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3).redirectionLink('wheel-of-fortune').restaurantId(restaurantId2).build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3)
                                        .platformKey(WheelOfFortuneRedirectionPlatformKey.NO_REDIRECTION)
                                        .restaurantId(restaurantId2)
                                        .build()
                                )
                                .scannedAt(DateTime.now().minus({ days: 5 }).toJSDate())
                                .build(),

                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 15 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId1)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId1).restaurantId(restaurantId1).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 10 }).toJSDate())
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId2)
                                .nfcSnapshot(getDefaultNfcSnapshot(nfcId2).restaurantId(restaurantId2).build())
                                .scannedAt(DateTime.now().minus({ months: 1, days: 5 }).toJSDate())
                                .matchedReviewId(reviewId2)
                                .build(),
                            getDefaultScan()
                                .nfcId(nfcId3)
                                .nfcSnapshot(
                                    getDefaultNfcSnapshot(nfcId3).redirectionLink('wheel-of-fortune').restaurantId(restaurantId2).build()
                                )
                                .scannedAt(DateTime.now().minus({ months: 1, days: 5 }).toJSDate())
                                .build(),
                        ];
                    },
                },
            },
            expectedResult: undefined,
        });

        await testCase.build();

        const result = await getScanInsightsUseCase.execute({
            startScannedAt: DateTime.now().minus({ months: 1 }).toISO(),
            endScannedAt: DateTime.now().toISO(),
            restaurantIds: [restaurantId1.toString(), restaurantId2.toString(), restaurantId3.toString()],
            nfcIds: [nfcId1.toString(), nfcId2.toString(), nfcId3.toString()],
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        });

        expect(result.scansEvolution).toBe(0);
        expect(result.wofScansEvolution).toBe(2);
        expect(result.scans.length).toBe(6);
        expect(result.estimatedReviewCountPerRestaurant).toMatchObject({
            [restaurantId1.toString()]: {
                estimatedReviewCount: 0,
                estimatedReviewCountDifferenceWithPreviousPeriod: 0,
            },
            [restaurantId2.toString()]: {
                estimatedReviewCount: 1,
                estimatedReviewCountDifferenceWithPreviousPeriod: 1,
            },
            [restaurantId3.toString()]: {
                estimatedReviewCount: 0,
                estimatedReviewCountDifferenceWithPreviousPeriod: 0,
            },
        });
        expect(result.totemReviewsPerRestaurant).toMatchObject({
            reviewCountDifferenceWithPreviousPeriod: -1,
            privateReviewCountDifferenceWithPreviousPeriod: 0,
            reviewsPerPlatform: [],
            privateReviewsPerRating: [],
        });

        // Check that the correct reviews are counted in reviewsPerPlatform
        const restaurant1ReviewCount = result.totemReviewsPerRestaurant.reviewsPerPlatform
            .flatMap((platform) => platform.reviewsCount)
            .filter((count) => count.restaurantId === restaurantId1.toString())
            .reduce((sum, count) => sum + count.count, 0);

        const restaurant2ReviewCount = result.totemReviewsPerRestaurant.reviewsPerPlatform
            .flatMap((platform) => platform.reviewsCount)
            .filter((count) => count.restaurantId === restaurantId2.toString() && count.count > 0)
            .reduce((sum, count) => sum + count.count, 0);

        const restaurant3ReviewCount = result.totemReviewsPerRestaurant.reviewsPerPlatform
            .flatMap((platform) => platform.reviewsCount)
            .filter((count) => count.restaurantId === restaurantId3.toString())
            .reduce((sum, count) => sum + count.count, 0);

        expect(restaurant1ReviewCount).toBe(0);
        expect(restaurant2ReviewCount).toBe(0);
        expect(restaurant3ReviewCount).toBe(0);
    });
});
