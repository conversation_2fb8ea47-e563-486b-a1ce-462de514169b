import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    AggregatedPlatformInsightsDto,
    GetStoredInsightsAggregatedRequestBodyDto,
    StoredInsightsAggregatedResponseDto,
} from '@malou-io/package-dto';
import {
    AggregationType,
    getDateRangeFromMalouComparisonPeriod,
    isNotNil,
    MalouErrorCode,
    PartialRecord,
    PlatformKey,
    StoredInDBInsightsMetric,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { PlatformInsightsMetricMapper } from ':modules/platform-insights/mappers/platform-insights-metrics.mapper';
import PlatformInsightsRepository, { AggregatedPlatformInsights } from ':modules/platform-insights/platform-insights.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';

type PlatformsGroupedByRestaurantId = {
    [restaurantId: string]: { socialId?: string; key: <PERSON>Key }[];
};

@singleton()
export class GetStoredInsightsAggregatedUseCase {
    readonly SUPPORTED_AGGREGATION_TYPES = [AggregationType.TOTAL];

    constructor(
        private readonly _platformInsightsRepository: PlatformInsightsRepository,
        private readonly _platformInsightsMetricMapper: PlatformInsightsMetricMapper,
        private readonly _platformsRepository: PlatformsRepository
    ) {}

    async execute(requestBody: GetStoredInsightsAggregatedRequestBodyDto): Promise<StoredInsightsAggregatedResponseDto> {
        const { restaurantIds, metrics, platformKeys, aggregationType } = requestBody;

        assert(this.SUPPORTED_AGGREGATION_TYPES.includes(aggregationType), `Aggregation type ${aggregationType} is not supported`);

        const { startDate, endDate } = this._getPeriod(requestBody);

        const platformsGroupedByRestaurantId = await this._getPlatformsGroupedByRestaurantId(restaurantIds, platformKeys);
        const socialIds = Object.values(platformsGroupedByRestaurantId)
            .flat()
            .map((platform) => platform.socialId)
            .filter(isNotNil);

        const insights = await this._platformInsightsRepository.getAggregatedInsightsGroupedByPlatform({
            socialIds,
            metrics: metrics.map(this._platformInsightsMetricMapper.mapMalouMetricToStoredInDbMetric),
            platformKeys,
            startDate,
            endDate,
        });

        return this._buildStoredInsightsResponse(restaurantIds, platformKeys, insights, platformsGroupedByRestaurantId, startDate, endDate);
    }

    private _buildStoredInsightsResponse(
        restaurantIds: string[],
        platformKeys: PlatformKey[],
        insights: AggregatedPlatformInsights[],
        platformsGroupedByRestaurantId: PlatformsGroupedByRestaurantId,
        startDate: Date,
        endDate: Date
    ): StoredInsightsAggregatedResponseDto {
        return restaurantIds.reduce(
            (acc: StoredInsightsAggregatedResponseDto, restaurantId) => {
                acc[restaurantId] = platformKeys.reduce((acc2: PartialRecord<PlatformKey, AggregatedPlatformInsightsDto>, platformKey) => {
                    const platform = platformsGroupedByRestaurantId[restaurantId]?.find((pl) => pl.key === platformKey);
                    if (!platform) {
                        acc2[platformKey] = { malouErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND, hasData: false };
                        return acc2;
                    }

                    const platformInsightsData = insights.find(
                        (data) => data.platformKey === platformKey && data.socialId === platform.socialId
                    );

                    if (!platformInsightsData?.insights) {
                        acc2[platformKey] = { malouErrorCode: MalouErrorCode.INSIGHTS_NOT_FOUND, hasData: false };
                        return acc2;
                    }

                    acc2[platformKey] = {
                        hasData: true,
                        insights: this._buildPlatformInsightsData(platformInsightsData),
                    };

                    return acc2;
                }, {});

                return acc;
            },
            { startDate: startDate.toISOString(), endDate: endDate.toISOString() }
        );
    }

    private async _getPlatformsGroupedByRestaurantId(
        restaurantIds: string[],
        platformKeys: PlatformKey[]
    ): Promise<PlatformsGroupedByRestaurantId> {
        const platforms = await this._platformsRepository.getPlatformsByRestaurantIdsAndPlatformKeys(restaurantIds, platformKeys);

        return restaurantIds.reduce((acc: PlatformsGroupedByRestaurantId, restaurantId) => {
            const platformsForRestaurant = platforms.filter((platform) => platform.restaurantId.toString() === restaurantId);
            acc[restaurantId] = platformsForRestaurant.map((platform) => ({
                socialId: platform.socialId,
                key: platform.key,
            }));
            return acc;
        }, {});
    }

    private _buildPlatformInsightsData(platformInsights: AggregatedPlatformInsights): AggregatedPlatformInsightsDto['insights'] {
        return Object.keys(platformInsights.insights).reduce(
            (acc, key) => {
                const metric = this._platformInsightsMetricMapper.mapStoredInDbMetricToMalouMetric(key as StoredInDBInsightsMetric);
                acc[metric] = (platformInsights.insights as Record<string, any>)[key];
                return acc;
            },
            {} as NonNullable<AggregatedPlatformInsightsDto['insights']>
        );
    }

    private _getPeriod(requestBody: GetStoredInsightsAggregatedRequestBodyDto): {
        startDate: Date;
        endDate: Date;
    } {
        const { startDate, endDate, comparisonPeriod } = requestBody;
        if (!comparisonPeriod) {
            return { startDate, endDate };
        }
        const previousPeriodData = getDateRangeFromMalouComparisonPeriod({
            comparisonPeriod,
            dateFilters: { startDate, endDate },
        });
        if (!previousPeriodData.startDate || !previousPeriodData.endDate) {
            throw new MalouError(MalouErrorCode.INVALID_COMPARISON_PERIOD, {
                message: 'Could not compute previous period dates',
                metadata: {
                    comparisonPeriod,
                    startDate,
                    endDate,
                },
            });
        }
        return {
            startDate: previousPeriodData.startDate,
            endDate: previousPeriodData.endDate,
        };
    }
}
