import { container } from 'tsyringe';

import { StoredInsightsAggregatedResponseDto } from '@malou-io/package-dto';
import { AggregationType, MalouErrorCode, MalouMetric, PlatformKey, StoredInDBInsightsMetric } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultPlatformInsight } from ':modules/platform-insights/tests/platform-insight.builder';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

import { GetStoredInsightsAggregatedUseCase } from './get-stored-insights-aggregated.use-case';

describe('GetStoredInsightsAggregatedUseCase', () => {
    beforeAll(() => {
        registerRepositories(['RestaurantsRepository', 'PlatformInsightsRepository', 'PlatformsRepository']);
    });

    describe('execute', () => {
        it('should return insights for restaurants', async () => {
            const useCase = container.resolve(GetStoredInsightsAggregatedUseCase);

            const testCase = new TestCaseBuilderV2<'restaurants' | 'platforms' | 'platformInsights'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [
                                getDefaultRestaurant().name('restaurant_0').build(),
                                getDefaultRestaurant().name('restaurant_1').build(),
                                // has not connected platforms
                                getDefaultRestaurant().name('restaurant_2').build(),
                            ];
                        },
                    },
                    platforms: {
                        data(dependencies) {
                            return [
                                // restaurant 0 platforms
                                getDefaultPlatform()
                                    .key(PlatformKey.GMB)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId('social_id0_p0')
                                    .build(),
                                getDefaultPlatform()
                                    .key(PlatformKey.FACEBOOK)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId('social_id1_p0')
                                    .build(),
                                getDefaultPlatform()
                                    .key(PlatformKey.INSTAGRAM)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId('social_id2_p0')
                                    .build(),
                                getDefaultPlatform()
                                    .key(PlatformKey.LAFOURCHETTE)
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .socialId('social_id3_p0')
                                    .build(),

                                // restaurant 1 platforms
                                getDefaultPlatform()
                                    .key(PlatformKey.GMB)
                                    .restaurantId(dependencies.restaurants()[1]._id)
                                    .socialId('social_id2_p1')
                                    .build(),
                            ];
                        },
                    },
                    platformInsights: {
                        data(dependencies) {
                            return [
                                /**** platform 0 insights *****/
                                // out of date range
                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.WEBSITE_CLICKS)
                                    .platformKey(PlatformKey.GMB)
                                    .socialId(dependencies.platforms()[0].socialId ?? '')
                                    .date(new Date('2022-03-01'))
                                    .day(1)
                                    .month(3)
                                    .year(2022)
                                    .value(145)
                                    .build(),
                                // not in the requested metrics
                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.BUSINESS_IMPRESSIONS_DESKTOP_MAPS)
                                    .platformKey(PlatformKey.GMB)
                                    .socialId(dependencies.platforms()[0].socialId ?? '')
                                    .date(new Date('2024-06-01'))
                                    .day(1)
                                    .month(6)
                                    .year(2024)
                                    .build(),

                                // not in the requested platforms
                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.PLATFORM_RATING)
                                    .platformKey(PlatformKey.LAFOURCHETTE)
                                    .socialId(dependencies.platforms()[3].socialId ?? '')
                                    .date(new Date('2024-06-01'))
                                    .day(1)
                                    .month(6)
                                    .year(2024)
                                    .build(),

                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.WEBSITE_CLICKS)
                                    .platformKey(PlatformKey.GMB)
                                    .value(123)
                                    .socialId(dependencies.platforms()[0].socialId ?? '')
                                    .date(new Date('2024-06-01'))
                                    .day(1)
                                    .month(6)
                                    .year(2024)
                                    .build(),

                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.WEBSITE_CLICKS)
                                    .platformKey(PlatformKey.GMB)
                                    .date(new Date('2024-06-02'))
                                    .value(124)
                                    .socialId(dependencies.platforms()[0].socialId ?? '')
                                    .date(new Date('2024-06-02'))
                                    .day(2)
                                    .month(6)
                                    .year(2024)
                                    .build(),

                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.BUSINESS_FOOD_MENU_CLICKS)
                                    .platformKey(PlatformKey.GMB)
                                    .value(125)
                                    .socialId(dependencies.platforms()[0].socialId ?? '')
                                    .date(new Date('2024-06-01'))
                                    .day(1)
                                    .month(6)
                                    .year(2024)
                                    .build(),

                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.FOLLOWERS)
                                    .platformKey(PlatformKey.FACEBOOK)
                                    .value(126)
                                    .socialId(dependencies.platforms()[1].socialId ?? '')
                                    .date(new Date('2024-07-01'))
                                    .day(1)
                                    .month(7)
                                    .year(2024)
                                    .build(),

                                /**** platform 1 insights *****/
                                getDefaultPlatformInsight()
                                    .metric(StoredInDBInsightsMetric.WEBSITE_CLICKS)
                                    .platformKey(PlatformKey.GMB)
                                    .socialId(dependencies.platforms()[4].socialId ?? '')
                                    .date(new Date('2024-03-01'))
                                    .day(1)
                                    .month(3)
                                    .year(2024)
                                    .value(145)
                                    .build(),
                            ];
                        },
                    },
                },
                expectedResult(dependencies) {
                    const restaurant0Id = dependencies.restaurants[0]._id.toString();
                    const restaurant1Id = dependencies.restaurants[1]._id.toString();
                    const restaurant2Id = dependencies.restaurants[2]._id.toString();
                    return {
                        // restaurant 0
                        [restaurant0Id]: {
                            [PlatformKey.GMB]: {
                                hasData: true,
                                insights: {
                                    // 123 + 124
                                    [MalouMetric.ACTIONS_WEBSITE]: 247,
                                    [MalouMetric.ACTIONS_MENU_CLICK]: 125,
                                },
                            },
                            [PlatformKey.FACEBOOK]: {
                                hasData: true,
                                insights: {
                                    [MalouMetric.FOLLOWERS]: 126,
                                },
                            },
                            [PlatformKey.INSTAGRAM]: {
                                hasData: false,
                                malouErrorCode: MalouErrorCode.INSIGHTS_NOT_FOUND,
                            },
                        },
                        // restaurant 1
                        [restaurant1Id]: {
                            [PlatformKey.GMB]: {
                                hasData: true,
                                insights: {
                                    [MalouMetric.ACTIONS_WEBSITE]: 145,
                                },
                            },
                            [PlatformKey.FACEBOOK]: {
                                hasData: false,
                                malouErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
                            },
                            [PlatformKey.INSTAGRAM]: {
                                hasData: false,
                                malouErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
                            },
                        },
                        // restaurant 2
                        [restaurant2Id]: {
                            [PlatformKey.GMB]: {
                                hasData: false,
                                malouErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
                            },
                            [PlatformKey.FACEBOOK]: {
                                hasData: false,
                                malouErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
                            },
                            [PlatformKey.INSTAGRAM]: {
                                hasData: false,
                                malouErrorCode: MalouErrorCode.PLATFORM_NOT_FOUND,
                            },
                        },
                    };
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();

            const startDate = new Date('2024-01-01');
            const endDate = new Date('2024-12-31');

            const expectedResults: StoredInsightsAggregatedResponseDto = {
                ...testCase.getExpectedResult(),
                startDate: startDate.toISOString(),
                endDate: endDate.toISOString(),
            };

            const restaurantIds = [
                seededObjects.restaurants[0]._id.toString(),
                seededObjects.restaurants[1]._id.toString(),
                seededObjects.restaurants[2]._id.toString(),
            ];

            const result: StoredInsightsAggregatedResponseDto = await useCase.execute({
                restaurantIds,
                startDate,
                endDate,
                metrics: [MalouMetric.ACTIONS_WEBSITE, MalouMetric.ACTIONS_MENU_CLICK, MalouMetric.FOLLOWERS],
                platformKeys: [PlatformKey.GMB, PlatformKey.FACEBOOK, PlatformKey.INSTAGRAM],
                aggregationType: AggregationType.TOTAL,
            });

            expect(expectedResults).toEqual(result);
        });
    });
});
