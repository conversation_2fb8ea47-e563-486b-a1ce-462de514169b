import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    GiftDrawDto,
    GiftDrawInsightPerGiftDto,
    GiftDrawInsightPerRestaurantDto,
    GiftDrawPopulatedDto,
    GiftDto,
    WheelOfFortuneDto,
} from '@malou-io/package-dto';
import { IClient, IGiftDraw, PopulateBuilderHelper, toDbId } from '@malou-io/package-models';
import {
    EmailCategory,
    EmailType,
    getDateRangeFromMalouComparisonPeriod,
    GiftClaimStartDateOption,
    MalouComparisonPeriod,
    MalouErrorCode,
    NextDrawEnabledDelay,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { logger } from ':helpers/logger';
import { DateProviderPort } from ':helpers/providers/date/date.provider';
import { IndexProviderPort } from ':helpers/providers/index/index.provider';
import ClientsRepository from ':modules/clients/clients.repository';
import { GiftDrawDtoMapper } from ':modules/gift-draws/gift-draws.dto-mapper';
import { GiftDrawsRepository } from ':modules/gift-draws/gift-draws.repository';
import { GiftStocksRepository } from ':modules/gifts/stocks/gift-stocks.repository';
import MailingUseCases from ':modules/mailing/use-cases';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { WheelsOfFortuneDtoMapper } from ':modules/wheels-of-fortune/wheels-of-fortune.dto-mapper';
import { WheelsOfFortuneRepository } from ':modules/wheels-of-fortune/wheels-of-fortune.repository';

@singleton()
export class GiftDrawsUseCases {
    constructor(
        private readonly _clientsRepository: ClientsRepository,
        private readonly _giftDrawsRepository: GiftDrawsRepository,
        private readonly _giftStocksRepository: GiftStocksRepository,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        private readonly _wheelsOfFortuneRepository: WheelsOfFortuneRepository,
        private readonly _mailingUseCases: MailingUseCases,
        private readonly _wheelsOfFortuneDtoMapper: WheelsOfFortuneDtoMapper,
        private readonly _giftDrawsDtoMapper: GiftDrawDtoMapper,
        private readonly _indexProvider: IndexProviderPort,
        private readonly _dateProvider: DateProviderPort
    ) {}

    async getGiftDrawById(giftDrawId: string): Promise<GiftDrawPopulatedDto> {
        const giftDraw: PopulateBuilderHelper<
            IGiftDraw,
            [
                { path: 'gift' },
                { path: 'client' },
                { path: 'wheelOfFortune' },
                { path: 'restaurant'; populate: [{ path: 'logoPopulated' }] },
            ]
        > = (await this._giftDrawsRepository.findOne({
            filter: { _id: giftDrawId },
            options: {
                lean: true,
                populate: [
                    { path: 'gift' },
                    { path: 'client' },
                    { path: 'wheelOfFortune' },
                    { path: 'restaurant', populate: [{ path: 'logoPopulated' }] },
                ],
            },
        })) as any;
        if (!giftDraw) {
            logger.error('[GiftDrawsUseCases][getGiftDrawById] Gif draws not found', { giftDrawId });
            throw new MalouError(MalouErrorCode.GIFT_DRAW_NOT_FOUND, { message: 'Gift draw not found', metadata: { giftDraw } });
        }
        return this._giftDrawsDtoMapper.toGiftDrawPopulatedDto(giftDraw);
    }

    async createGiftDraw(restaurantId: string, wheelOfFortuneId: string, lang: string, clientId: string): Promise<GiftDrawDto> {
        const client = await this._clientsRepository.findOne({
            filter: { _id: clientId },
        });
        if (!client) {
            logger.error('[GiftDrawsUseCases][createGiftDraw] Client not found', { clientId });
            throw new MalouError(MalouErrorCode.CLIENT_NOT_FOUND, { message: 'Client not found', metadata: { clientId } });
        }

        const wheelOfFortuneFromRepo = await this._wheelsOfFortuneRepository.findWheelById(wheelOfFortuneId);
        assert(wheelOfFortuneFromRepo, 'Wheel of fortune not found');
        const wheelOfFortune = this._wheelsOfFortuneDtoMapper.toWheelOfFortuneDto(wheelOfFortuneFromRepo);
        if (!wheelOfFortune || !wheelOfFortune.startDate) {
            logger.warn('[GiftDrawsUseCases][createGiftDraw] Wheel of fortune not found', { restaurantId, wheelOfFortuneId });
            throw new MalouError(MalouErrorCode.WHEEL_OF_FORTUNE_NOT_FOUND, { message: 'No active wheel of fortune' });
        }
        if (new Date(wheelOfFortune.startDate) > this._dateProvider.provideTodayDate()) {
            logger.error('[GiftDrawsUseCases][createGiftDraw] Wheel of fortune not started', { wheelOfFortune });
            throw new MalouError(MalouErrorCode.WHEEL_OF_FORTUNE_NOT_STARTED, { message: 'Wheel of fortune not started' });
        }

        const canClientPlayNow = await this._canClientPlayNow(restaurantId, wheelOfFortune, client);
        if (!canClientPlayNow) {
            throw new MalouError(MalouErrorCode.CLIENT_ALREADY_PLAYED, { metadata: { wheelOfFortune, client, date: new Date() } });
        }
        const giftsThatCanBeWon = await this._getGiftsThatCanBeWon(wheelOfFortune, restaurantId);
        if (giftsThatCanBeWon.length === 0) {
            throw new MalouError(MalouErrorCode.NO_AVAILABLE_GIFTS, { message: 'No gifts can be won' });
        }

        const giftWon = this._drawGift(giftsThatCanBeWon);

        const retrievalStartDate = this._computeRetrievalStartDate(wheelOfFortune, giftWon);
        const retrievalEndDate = this._computeRetrievalEndDate(wheelOfFortune, giftWon);

        if (!retrievalStartDate || !retrievalEndDate) {
            throw new MalouError(MalouErrorCode.WRONG_RETRIEVAL_DATES, { message: 'Wrong retrieval dates' });
        }
        assert(giftWon.id, 'Missing gift id');
        assert(wheelOfFortune.id, 'Missing wheel of fortune id');

        const partialGiftDraw: Partial<IGiftDraw> = {
            restaurantId: toDbId(restaurantId),
            giftId: toDbId(giftWon.id),
            wheelOfFortuneId: toDbId(wheelOfFortune.id),
            retrievalStartDate,
            retrievalEndDate,
            lang,
            clientId: toDbId(clientId),
        };
        const giftDraw = await this._giftDrawsRepository.createAndPopulate(partialGiftDraw);

        try {
            const newGiftStock = await this._giftStocksRepository.decrementStock(restaurantId, giftWon.id);
            if (newGiftStock?.quantity === 0) {
                this._sendEmptyStockEmails(giftDraw, wheelOfFortune.id).catch((error) => {
                    logger.error('[GiftDrawsUseCases][createGiftDraw] Error when sending empty stock emails', {
                        gift: giftWon,
                        restaurantId,
                        error,
                    });
                });
            }
        } catch (e) {
            logger.error('[GiftDrawsUseCases][createGiftDraw] Error when decrementing giftStock', { gift: giftWon, restaurantId });
            throw e;
        }

        return this._giftDrawsDtoMapper.toGiftDrawDto(giftDraw);
    }

    async sendRetrievalEmail(giftDrawId: string): Promise<void> {
        const draw = await this._giftDrawsRepository.findOne({
            filter: { _id: giftDrawId },
            options: {
                lean: true,
                populate: [{ path: 'gift' }, { path: 'restaurant' }, { path: 'client' }],
            },
        });

        if (!draw) {
            logger.error('[GiftDrawsUseCases][sendRetrievalEmail] Gift draw not found to send client email', { giftDrawId });
            throw new MalouError(MalouErrorCode.GIFT_DRAW_NOT_FOUND, { message: 'Gift draw not found when sending retrieval email' });
        }

        if (!draw.client?.email) {
            logger.error('[GiftDrawsUseCases][sendRetrievalEmail] Client email not found for client when sending retrieval email', {
                clientId: draw.client?._id,
                giftDrawId,
            });
            throw new MalouError(MalouErrorCode.CLIENT_EMAIL_NOT_FOUND, {
                message: 'Client email for client not found when sending retrieval email',
            });
        }

        await this._mailingUseCases.sendEmail(EmailCategory.WHEEL_OF_FORTUNE_NOTIFICATION, EmailType.RETRIEVE_GIFT, {
            draw,
        });
    }

    async assignClientToGiftDraw(giftDrawId: string, clientId: string): Promise<GiftDrawDto> {
        const giftDraw = await this._giftDrawsRepository.findOne({
            filter: {
                _id: giftDrawId,
            },
        });
        if (!giftDraw) {
            throw new MalouError(MalouErrorCode.GIFT_DRAW_NOT_FOUND, { message: 'Gift draw not found', metadata: { giftDrawId } });
        }
        const existingDrawForClient = await this._giftDrawsRepository.findOne({
            filter: {
                restaurantId: giftDraw.restaurantId,
                wheelOfFortuneId: giftDraw.wheelOfFortuneId,
                clientId,
            },
            options: {
                populate: [{ path: 'wheelOfFortune', select: ['startDate', 'endDate'] }],
                lean: true,
            },
        });
        const clientHasPreviousDrawNotExpired =
            existingDrawForClient && DateTime.fromJSDate(existingDrawForClient.retrievalEndDate) > DateTime.now();
        if (clientHasPreviousDrawNotExpired) {
            logger.error('[GiftDrawsUseCases][assignClientToGiftDraw] Client tried to play twice', {
                clientId,
                giftDrawId,
                restaurantId: giftDraw.restaurantId,
                wheelOfFortuneId: giftDraw.wheelOfFortuneId,
            });
            throw new MalouError(MalouErrorCode.CLIENT_ALREADY_PLAYED, {
                message: 'Client already played at this wheel',
                metadata: { WheelOfFortuneId: giftDraw.wheelOfFortuneId, restaurantId: giftDraw.restaurantId },
            });
        }
        const giftDrawUpdated = await this._giftDrawsRepository.findOneAndUpdate({
            filter: { _id: giftDrawId },
            update: { clientId },
            options: { lean: true },
        });
        assert(giftDrawUpdated, 'Gift draw not found');
        return this._giftDrawsDtoMapper.toGiftDrawDto(giftDrawUpdated);
    }

    async setGiftDrawRetrieved(giftDrawId: string): Promise<GiftDrawPopulatedDto> {
        const retrievedAt = this._dateProvider.provideTodayDate();

        const giftDraw: PopulateBuilderHelper<
            IGiftDraw,
            [
                { path: 'gift' },
                { path: 'client' },
                { path: 'wheelOfFortune' },
                { path: 'restaurant'; populate: [{ path: 'logoPopulated' }] },
            ]
        > = (await this._giftDrawsRepository.findOneAndUpdate({
            filter: { _id: giftDrawId },
            update: { retrievedAt },
            options: {
                lean: true,
                populate: [
                    { path: 'gift' },
                    { path: 'client' },
                    { path: 'wheelOfFortune' },
                    { path: 'restaurant', populate: [{ path: 'logoPopulated' }] },
                ],
            },
        })) as any;
        if (!giftDraw) {
            logger.error('[GiftDrawsUseCases][setGiftDrawRetrieved] Gif draws not found', { giftDrawId });
            throw new MalouError(MalouErrorCode.GIFT_DRAW_NOT_FOUND, {
                message: 'Gift draw not found',
                metadata: { giftDrawId },
            });
        }
        return this._giftDrawsDtoMapper.toGiftDrawPopulatedDto(giftDraw);
    }

    getSoonExpiredDraws() {
        return this._giftDrawsRepository.findSoonExpiredGiftDraws();
    }

    async cancelGiftDraw(giftDrawId: string): Promise<void> {
        const giftDraw = await this._giftDrawsRepository.findOne({
            filter: { _id: giftDrawId },
            options: { lean: true },
        });
        if (!giftDraw) {
            throw new MalouError(MalouErrorCode.GIFT_DRAW_NOT_FOUND, { message: 'Gift draw not found', metadata: { giftDrawId } });
        }
        const { restaurantId, giftId } = giftDraw;
        return this._giftStocksRepository.incrementStock(restaurantId.toString(), giftId.toString());
    }

    async getGiftDrawsInsightsPerGift(restaurantId: string, startPeriod: Date, endPeriod: Date): Promise<GiftDrawInsightPerGiftDto[]> {
        const diffBetweenStartAndEnd = DateTime.fromJSDate(endPeriod).diff(DateTime.fromJSDate(startPeriod), 'days').days;
        const startPreviousPeriod = DateTime.fromJSDate(startPeriod).minus({ days: diffBetweenStartAndEnd }).toJSDate();
        const giftDrawInsightsPerGift = await this._giftDrawsRepository.findGiftDrawsInsightsPerGift(
            restaurantId,
            startPeriod,
            endPeriod,
            startPreviousPeriod
        );
        return this._giftDrawsDtoMapper.toGiftDrawInsightsPerGiftDto(giftDrawInsightsPerGift);
    }

    async getGiftDrawsInsightsPerRestaurant({
        restaurantIds,
        endDate,
        startDate,
        comparisonPeriod,
    }: {
        restaurantIds: string[];
        startDate: Date;
        endDate: Date;
        comparisonPeriod: MalouComparisonPeriod;
    }): Promise<GiftDrawInsightPerRestaurantDto[]> {
        const diffBetweenStartAndEnd = DateTime.fromJSDate(endDate).diff(DateTime.fromJSDate(startDate), 'days').days;
        const startPreviousPeriod = DateTime.fromJSDate(startDate).minus({ days: diffBetweenStartAndEnd }).toJSDate();
        const previousComparisonPeriod = getDateRangeFromMalouComparisonPeriod({
            comparisonPeriod,
            dateFilters: {
                startDate,
                endDate,
            },
        });

        let previousPeriod = {
            startDate: startPreviousPeriod,
            endDate: startDate,
        };

        if (previousComparisonPeriod.startDate && previousComparisonPeriod.endDate) {
            previousPeriod = {
                startDate: previousComparisonPeriod.startDate,
                endDate: previousComparisonPeriod.endDate,
            };
        }

        const giftDrawInsightsPerGift = await this._giftDrawsRepository.findGiftDrawsInsightsPerRestaurants({
            restaurantIds,
            currentPeriod: { startDate, endDate },
            previousPeriod,
        });

        return this._giftDrawsDtoMapper.toGiftDrawInsightsPerRestaurantDto(giftDrawInsightsPerGift);
    }

    private async _canClientPlayNow(restaurantId: string, wheelOfFortune: WheelOfFortuneDto, client: IClient): Promise<boolean> {
        assert(wheelOfFortune.id, 'Missing wheel of fortune id');
        const existingDrawsForClient = await this._giftDrawsRepository.findGiftDrawsForExistingClientForRestaurantAndWheelOfFortune(
            restaurantId,
            wheelOfFortune.id,
            client._id.toString()
        );

        if (existingDrawsForClient.length === 0) {
            return true;
        }

        const lastDrawForClient = existingDrawsForClient.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())[0];
        const lastCreatedAt = DateTime.fromJSDate(lastDrawForClient.createdAt);
        const now = DateTime.now();
        switch (wheelOfFortune.parameters.redirectionSettings.nextDrawEnabledDelay) {
            case NextDrawEnabledDelay.ALWAYS:
                return true;
            case NextDrawEnabledDelay.AFTER_24_HOURS:
                return lastCreatedAt.plus({ days: 1 }) < now;
            case NextDrawEnabledDelay.AFTER_48_HOURS:
                return lastCreatedAt.plus({ days: 2 }) < now;
            case NextDrawEnabledDelay.AFTER_ONE_WEEK:
                return lastCreatedAt.plus({ weeks: 1 }) < now;
            case NextDrawEnabledDelay.NEVER:
            default:
                return false;
        }
    }

    private async _sendEmptyStockEmails(
        giftDraw: PopulateBuilderHelper<
            IGiftDraw,
            [
                { path: 'gift' },
                { path: 'client' },
                {
                    path: 'restaurant';
                    populate: [
                        {
                            path: 'logoPopulated';
                        },
                    ];
                },
            ]
        >,
        wheelOfFortuneId: string
    ): Promise<void> {
        const users = await this._userRestaurantsRepository.getRestaurantUsers(giftDraw.restaurant._id.toString());

        for (const user of users) {
            await this._mailingUseCases.sendEmail(EmailCategory.WHEEL_OF_FORTUNE_NOTIFICATION, EmailType.EMPTY_STOCK, {
                emptyStockData: {
                    draw: giftDraw,
                    wheelOfFortuneId: wheelOfFortuneId,
                    receiver: user,
                },
            });
        }
    }

    private _drawGift(gifts: GiftDto[]): GiftDto {
        const totalWeight = gifts.reduce((acc, curr) => acc + curr.weight, 0);
        const randomIndex = this._indexProvider.provideFloat(totalWeight);
        let accumulatedWeight = 0;

        // sort gifts by weight to know which gift is drawn in unit tests
        // ask Baptiste or Tanguy for more details
        gifts.sort((giftA, giftB) => giftA.weight - giftB.weight);

        for (const gift of gifts) {
            accumulatedWeight += gift.weight;
            if (randomIndex <= accumulatedWeight) {
                return gift;
            }
        }

        throw new MalouError(MalouErrorCode.DRAW_GIFT_WENT_WRONG, { metadata: { gifts } });
    }

    private _computeRetrievalStartDate(wheelOfFortune: WheelOfFortuneDto, giftWon: GiftDto): Date | null {
        const today = DateTime.now().set({ hour: 0, minute: 0, second: 0, millisecond: 0 });

        switch (giftWon.giftClaimStartDateOption ?? wheelOfFortune.parameters.giftClaimStartDateOption) {
            case GiftClaimStartDateOption.NOW:
                return today.toJSDate();
            case GiftClaimStartDateOption.TOMORROW:
                return today.plus({ days: 1 }).toJSDate();
            default:
                return null;
        }
    }

    private _computeRetrievalEndDate(wheelOfFortune: WheelOfFortuneDto, giftWon: GiftDto): Date | null {
        const retrievalStartDate = this._computeRetrievalStartDate(wheelOfFortune, giftWon);

        const dateTimeAddOn: { days?: number; months?: number } = {};
        switch (wheelOfFortune.parameters.giftClaimDurationInDays) {
            // values of GiftClaimDurationInDaysOption
            case 2:
            case 7:
            case 15:
                dateTimeAddOn.days = wheelOfFortune.parameters.giftClaimDurationInDays;
                break;
            case 30:
                dateTimeAddOn.months = 1;
                break;
            case 60:
                dateTimeAddOn.months = 2;
                break;
            case 182:
                dateTimeAddOn.months = 6;
                break;
            default:
                dateTimeAddOn.days = 7;
        }
        return retrievalStartDate ? DateTime.fromJSDate(retrievalStartDate).plus(dateTimeAddOn).toJSDate() : null;
    }

    private async _getGiftsThatCanBeWon(wheelOfFortune: WheelOfFortuneDto, restaurantId: string): Promise<GiftDto[]> {
        return wheelOfFortune.gifts.filter((gift) => {
            const giftStockForRestaurant = gift.stocks.find((stock) => stock.restaurant.id.toString() === restaurantId);
            return giftStockForRestaurant && giftStockForRestaurant.quantity !== 0;
        });
    }
}
