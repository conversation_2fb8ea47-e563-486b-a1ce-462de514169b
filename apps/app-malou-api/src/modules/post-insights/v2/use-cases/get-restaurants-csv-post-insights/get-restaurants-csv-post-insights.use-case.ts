import { groupBy } from 'lodash';
import { singleton } from 'tsyringe';

import { RestaurantCsvPostInsights } from '@malou-io/package-dto';
import { isNotNil, PlatformKey } from '@malou-io/package-utils';

import PlatformsRepository from ':modules/platforms/platforms.repository';
import { PostInsightRepository } from ':modules/post-insights/v2/repositories/post-insight.repository';
import { PlatformWithRestaurantDetails } from ':modules/posts/posts.interface';

@singleton()
export class GetRestaurantsCsvPostInsightsUseCase {
    constructor(
        private readonly _postInsightRepository: PostInsightRepository,
        private readonly _platformRepository: PlatformsRepository
    ) {}

    async execute({
        restaurantIds,
        platformKeys,
        startDate,
        endDate,
    }: {
        restaurantIds: string[];
        platformKeys: PlatformKey[];
        startDate: Date;
        endDate: Date;
    }): Promise<RestaurantCsvPostInsights[]> {
        const platformsWithRestaurants: PlatformWithRestaurantDetails[] =
            await this._platformRepository.getPlatformsWithRestaurantDetailsByRestaurantIdsAndPlatformKeys(restaurantIds, platformKeys);

        const platformSocialIds = platformsWithRestaurants.map((platform) => platform.socialId).filter(isNotNil);

        const platformsCsvPostInsights = await this._postInsightRepository.getPlatformsCsvPostInsights({
            platformSocialIds: platformSocialIds,
            startDate: startDate,
            endDate: endDate,
        });

        const platformWithRestaurantGoupedByRestaurantId = groupBy(platformsWithRestaurants, (p) => p.restaurantId);

        return Object.entries(platformWithRestaurantGoupedByRestaurantId).map(([restaurantId, platforms]) => {
            const restaurantPostInsights = platforms
                .map((platform) => {
                    const platformSocialId = platform.socialId;
                    if (!platformSocialId) {
                        return [];
                    }
                    return platformsCsvPostInsights[platformSocialId];
                })
                .flat()
                .filter(isNotNil);
            return {
                restaurantId,
                restaurantName: platforms[0]?.restaurant?.name ?? '',
                restaurantInternalName: platforms[0]?.restaurant?.internalName ?? '',
                restaurantAddress: platforms[0]?.restaurant?.address?.formattedAddress ?? '',
                postInsights: restaurantPostInsights,
            };
        });
    }
}
