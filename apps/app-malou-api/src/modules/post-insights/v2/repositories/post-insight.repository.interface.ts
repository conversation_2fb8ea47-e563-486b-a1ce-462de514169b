import { PlatformKey, PostType } from '@malou-io/package-utils';

export type PlatformTotalPageInsights = {
    [platformSocialId: string]: {
        totalImpressions: number | null;
        totalEngagements: number | null;
    };
};

export type PlatformTotalPostsCount = {
    [platformSocialId: string]: {
        totalPosts: number;
    };
};

export type PlatformTotalFollowersCount = {
    [platformSocialId: string]: {
        totalFollowers: number | null;
    };
};

export type PlatformsCsvPostInsights = {
    [platformSocialId: string]: {
        platformKey: PlatformKey;
        platformSocialId: string;
        postCreatedAt: Date;
        postType: PostType;
        caption: string;
        impressions: number;
        reach: number | null;
        likes: number;
        comments: number;
        shares: number;
        saved: number | null;
        engagementRate: number;
    }[];
};
