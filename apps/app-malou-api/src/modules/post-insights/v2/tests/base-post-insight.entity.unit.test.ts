import { DateTime } from 'luxon';

import { PlatformKey, PostInsightEntityType, PostType } from '@malou-io/package-utils';

import { BasePostInsight, BasePostInsightProps } from '../entities/base-post-insight.entity';

describe('BasePostInsight', () => {
    const mockBaseProps: Omit<BasePostInsightProps, 'postSocialCreatedAt' | 'lastFetchedAt'> = {
        id: 'test-id',
        platformKey: PlatformKey.INSTAGRAM,
        socialId: 'social-123',
        entityType: PostInsightEntityType.POST,
        platformSocialId: 'platform-social-123',
        post: {
            id: 'post-123',
            text: 'This is a test post',
            postType: PostType.IMAGE,
            socialLink: 'https://instagram.com/p/test',
            attachments: [],
        },
        createdAt: new Date(),
        updatedAt: new Date(),
    };

    const createPostInsight = (postSocialCreatedAt: DateTime, lastFetchedAt: DateTime): BasePostInsight => {
        const props: BasePostInsightProps = {
            ...mockBaseProps,
            postSocialCreatedAt: postSocialCreatedAt.toJSDate(),
            lastFetchedAt: lastFetchedAt.toJSDate(),
        };
        return new BasePostInsight(props);
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('shouldBeRefreshed', () => {
        let mockNow: DateTime;

        beforeEach(() => {
            // Mock current time to a fixed date for consistent testing
            mockNow = DateTime.fromISO('2024-01-30T10:00:00.000Z');
            jest.spyOn(DateTime, 'now').mockReturnValue(mockNow);
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        describe('Recent posts (less than 15 days old)', () => {
            it('should always refresh posts created today', () => {
                const postCreatedToday = mockNow;
                const lastFetchedYesterday = mockNow.minus({ days: 1 });

                const postInsight = createPostInsight(postCreatedToday, lastFetchedYesterday);

                expect(postInsight.shouldBeRefreshed()).toBe(true);
            });

            it('should always refresh posts created 1 day ago', () => {
                const postCreated1DayAgo = mockNow.minus({ days: 1 });
                const lastFetchedToday = mockNow;

                const postInsight = createPostInsight(postCreated1DayAgo, lastFetchedToday);

                expect(postInsight.shouldBeRefreshed()).toBe(true);
            });

            it('should always refresh posts created 6 days ago', () => {
                const postCreated7DaysAgo = mockNow.minus({ days: 6 });
                const lastFetchedToday = mockNow;

                const postInsight = createPostInsight(postCreated7DaysAgo, lastFetchedToday);

                expect(postInsight.shouldBeRefreshed()).toBe(true);
            });
        });

        describe('Older posts (15+ days old) - Adaptive refresh logic', () => {
            describe('Posts exactly at the threshold (15 days)', () => {
                it('should refresh if not fetched for calculated interval', () => {
                    const postCreated15DaysAgo = mockNow.minus({ days: 15 });
                    // For 15 days old post: Math.sqrt(15) / 0.2 = ~19.36 days
                    const lastFetched20DaysAgo = mockNow.minus({ days: 20 });

                    const postInsight = createPostInsight(postCreated15DaysAgo, lastFetched20DaysAgo);

                    expect(postInsight.shouldBeRefreshed()).toBe(true);
                });

                it('should not refresh if fetched recently within calculated interval', () => {
                    const postCreated15DaysAgo = mockNow.minus({ days: 15 });
                    // For 15 days old post: Math.sqrt(15) / 0.2 = ~19.36 days
                    const lastFetched10DaysAgo = mockNow.minus({ days: 10 });

                    const postInsight = createPostInsight(postCreated15DaysAgo, lastFetched10DaysAgo);

                    expect(postInsight.shouldBeRefreshed()).toBe(false);
                });
            });

            describe('1 month old posts (~30 days)', () => {
                it('should refresh if not fetched for more than calculated interval', () => {
                    const postCreated30DaysAgo = mockNow.minus({ days: 30 });
                    // For 30 days old post: Math.sqrt(30) / 0.2 = ~27.39 days
                    const lastFetched28DaysAgo = mockNow.minus({ days: 28 });

                    const postInsight = createPostInsight(postCreated30DaysAgo, lastFetched28DaysAgo);

                    expect(postInsight.shouldBeRefreshed()).toBe(true);
                });

                it('should not refresh if fetched within calculated interval', () => {
                    const postCreated30DaysAgo = mockNow.minus({ days: 30 });
                    // For 30 days old post: Math.sqrt(30) / 0.2 = ~27.39 days
                    const lastFetched20DaysAgo = mockNow.minus({ days: 20 });

                    const postInsight = createPostInsight(postCreated30DaysAgo, lastFetched20DaysAgo);

                    expect(postInsight.shouldBeRefreshed()).toBe(false);
                });
            });
        });
    });

    describe('Constants', () => {
        it('should have correct constant values', () => {
            expect(BasePostInsight.REFRESH_RATIO_MULTIPLIER).toBe(0.2);
            expect(BasePostInsight.RECENT_POST_THRESHOLD_DAYS).toBe(7);
        });
    });
});
