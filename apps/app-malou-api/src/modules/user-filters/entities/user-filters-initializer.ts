import { DateTime } from 'luxon';

import {
    getNonFeatureFlaggedPlatformsWithReviews,
    MalouComparisonPeriod,
    MalouPeriod,
    MalouTimeScalePeriod,
    PlatformFilterPage,
} from '@malou-io/package-utils';

import { UserFilters } from ':modules/user-filters/entities/user-filters.entity';

export function initializeUserFilters(userId: string): UserFilters {
    return new UserFilters({
        userId,
        aggregatedStatisticsFilters: {
            dates: {
                startDate: DateTime.now().minus({ days: 30 }).toJSDate(),
                endDate: DateTime.now().toJSDate(),
                period: MalouPeriod.LAST_THIRTY_DAYS,
            },
            restaurantIds: [],
            roiRestaurantIds: [],
            totemIds: [],
            timeScale: MalouTimeScalePeriod.LAST_MONTH,
            platforms: {
                [PlatformFilterPage.BOOSTERS]: [],
                [PlatformFilterPage.E_REPUTATION]: [],
                [PlatformFilterPage.SEO]: [],
                [PlatformFilterPage.SOCIAL_NETWORKS]: [],
            },
            monthYearPeriod: {
                startMonthYear: {
                    month: DateTime.now().minus({ months: 3 }).startOf('month').month,
                    year: DateTime.now().minus({ months: 3 }).startOf('month').year,
                },
                endMonthYear: {
                    month: DateTime.now().startOf('month').month,
                    year: DateTime.now().startOf('month').year,
                },
            },
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        },
        statisticsFiltersPerRestaurant: [],
        aggregatedReviewsFilters: {
            filters: {
                period: MalouPeriod.ALL,
                platforms: [],
                text: '',
                ratings: [0, 1, 2, 3, 4, 5],
                answered: true,
                notAnswered: true,
                pending: true,
                notAnswerable: true,
                showPrivate: true,
                withText: true,
                withoutText: true,
                archived: false,
                unarchived: true,
            },
            restaurantIds: [],
        },
        reviewsFiltersPerRestaurant: [],
    });
}

export function initializeUserRestaurantStatisticsFilters(): UserFilters['statisticsFiltersPerRestaurant'][0]['filters'] {
    return {
        dates: {
            startDate: DateTime.now().minus({ days: 30 }).toJSDate(),
            endDate: DateTime.now().toJSDate(),
            period: MalouPeriod.LAST_THIRTY_DAYS,
        },
        platforms: {
            [PlatformFilterPage.E_REPUTATION]: [],
            [PlatformFilterPage.SOCIAL_NETWORKS]: [],
        },
        totemIds: [],
        timeScale: MalouTimeScalePeriod.LAST_SIX_MONTHS,
        comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        monthYearPeriod: {
            startMonthYear: {
                month: DateTime.now().minus({ months: 3 }).startOf('month').month,
                year: DateTime.now().minus({ months: 3 }).startOf('month').year,
            },
            endMonthYear: {
                month: DateTime.now().startOf('month').month,
                year: DateTime.now().startOf('month').year,
            },
        },
    };
}

export function initializeUserRestaurantReviewsFilters(): UserFilters['reviewsFiltersPerRestaurant'][0]['filters'] {
    return {
        period: MalouPeriod.ALL,
        platforms: getNonFeatureFlaggedPlatformsWithReviews(),
        text: '',
        ratings: [0, 1, 2, 3, 4, 5],
        answered: true,
        notAnswered: true,
        pending: true,
        notAnswerable: true,
        showPrivate: true,
        withText: true,
        withoutText: true,
        archived: false,
        unarchived: true,
    };
}
