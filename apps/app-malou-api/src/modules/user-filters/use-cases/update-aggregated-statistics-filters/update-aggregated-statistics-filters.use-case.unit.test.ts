import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { container } from 'tsyringe';
import { DeepRequired } from 'utility-types';

import { AggregatedStatisticsFiltersBodyDto } from '@malou-io/package-dto';
import { newDbId } from '@malou-io/package-models';
import {
    MalouComparisonPeriod,
    MalouErrorCode,
    MalouPeriod,
    MalouTimeScalePeriod,
    PlatformFilterPage,
    PlatformKey,
} from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { MOCKED_NOW_DATE_TIME } from ':modules/roi/tests/roi.constants';
import UserFiltersRepository from ':modules/user-filters/repositories/user-filters.repository';
import { getDefaultUserFilters } from ':modules/user-filters/tests/user-filters.builder';
import { UpdateAggregatedStatisticsFiltersUseCase } from ':modules/user-filters/use-cases/update-aggregated-statistics-filters/update-aggregated-statistics-filters.use-case';

describe('UpdateAggregatedStatisticsFiltersUseCase', () => {
    beforeAll(() => {
        registerRepositories(['UserFiltersRepository']);

        const mockDateNow = jest.fn(() => MOCKED_NOW_DATE_TIME);
        global.Date.now = mockDateNow;
    });

    it('should throw an error if user filters are not found', async () => {
        const testCases = new TestCaseBuilderV2<'userFilters'>({
            seeds: {
                userFilters: {
                    data() {
                        return [getDefaultUserFilters().build()];
                    },
                },
            },
            expectedResult: () => [],
            expectedErrorCode: MalouErrorCode.NOT_FOUND,
        });
        await testCases.build();

        const updateAggregatedStatisticsFiltersUseCase = container.resolve(UpdateAggregatedStatisticsFiltersUseCase);
        const newAggregatedStatisticsFilters: DeepRequired<AggregatedStatisticsFiltersBodyDto> = {
            dates: {
                endDate: DateTime.now().toISO(),
                startDate: DateTime.now().minus({ days: 30 }).toISO(),
                period: MalouPeriod.LAST_THIRTY_DAYS,
            },
            platforms: {
                [PlatformFilterPage.BOOSTERS]: [PlatformKey.GMB],
                [PlatformFilterPage.E_REPUTATION]: [PlatformKey.FACEBOOK],
                [PlatformFilterPage.SEO]: [],
                [PlatformFilterPage.SOCIAL_NETWORKS]: [PlatformKey.GMB],
            },
            restaurantIds: [newDbId().toString()],
            roiRestaurantIds: [newDbId().toString()],
            timeScale: MalouTimeScalePeriod.ALL,
            totemIds: [newDbId().toString()],
            monthYearPeriod: {
                startMonthYear: { month: 1, year: 2021 },
                endMonthYear: { month: 1, year: 2021 },
            },
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        };

        const userId = newDbId().toString();
        await expect(updateAggregatedStatisticsFiltersUseCase.execute(userId, newAggregatedStatisticsFilters)).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: testCases.getExpectedErrorCode(),
            })
        );
    });

    it('should update aggregated statistics filters', async () => {
        const userId = newDbId();
        const testCases = new TestCaseBuilderV2<'userFilters'>({
            seeds: {
                userFilters: {
                    data() {
                        return [getDefaultUserFilters().userId(userId).build()];
                    },
                },
            },
            expectedResult: () => [],
        });

        await testCases.build();

        const updateAggregatedStatisticsFiltersUseCase = container.resolve(UpdateAggregatedStatisticsFiltersUseCase);
        const userFiltersRepository = container.resolve(UserFiltersRepository);

        const newAggregatedStatisticsFilters: DeepRequired<AggregatedStatisticsFiltersBodyDto> = {
            dates: {
                endDate: DateTime.now().toISODate(),
                startDate: DateTime.now().minus({ days: 30 }).toISODate(),
                period: MalouPeriod.LAST_THIRTY_DAYS,
            },
            platforms: {
                [PlatformFilterPage.BOOSTERS]: [PlatformKey.GMB],
                [PlatformFilterPage.E_REPUTATION]: [PlatformKey.FACEBOOK],
                [PlatformFilterPage.SEO]: [],
                [PlatformFilterPage.SOCIAL_NETWORKS]: [PlatformKey.GMB],
            },
            restaurantIds: [newDbId().toString()],
            roiRestaurantIds: [newDbId().toString()],
            timeScale: MalouTimeScalePeriod.ALL,
            totemIds: [newDbId().toString()],
            monthYearPeriod: {
                startMonthYear: { month: 1, year: 2021 },
                endMonthYear: { month: 1, year: 2021 },
            },
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        };

        await updateAggregatedStatisticsFiltersUseCase.execute(userId.toString(), newAggregatedStatisticsFilters);

        const userFilters = await userFiltersRepository.getUserFiltersByUserId(userId.toString());
        assert(userFilters);

        expect(userFilters.aggregatedStatisticsFilters).toMatchObject({
            ...newAggregatedStatisticsFilters,
            dates: {
                ...newAggregatedStatisticsFilters.dates,
                endDate: DateTime.now().toJSDate(),
                startDate: DateTime.now().minus({ days: 30 }).toJSDate(),
            },
        });
    });
});
