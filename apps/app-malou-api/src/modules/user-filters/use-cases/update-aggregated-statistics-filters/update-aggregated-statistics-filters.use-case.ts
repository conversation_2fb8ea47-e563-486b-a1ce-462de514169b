import { autoInjectable } from 'tsyringe';

import { AggregatedStatisticsFiltersBodyDto } from '@malou-io/package-dto';
import { MalouErrorCode, MalouPeriod, MonthYearPeriod, PlatformFilterPage } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { UserFilters } from ':modules/user-filters/entities/user-filters.entity';
import UserFiltersRepository from ':modules/user-filters/repositories/user-filters.repository';

@autoInjectable()
export class UpdateAggregatedStatisticsFiltersUseCase {
    constructor(private readonly _userFiltersRepository: UserFiltersRepository) {}

    async execute(
        userId: string,
        newAggregatedStatisticsFilters: AggregatedStatisticsFiltersBodyDto & { monthYearPeriod: MonthYearPeriod }
    ): Promise<void> {
        const userFilters = await this._userFiltersRepository.getUserFiltersByUserId(userId);
        if (!userFilters) {
            throw new MalouError(MalouErrorCode.NOT_FOUND, { message: 'User filters not found', metadata: { userId } });
        }

        const updatedData: UserFilters['aggregatedStatisticsFilters'] = {
            dates: {
                startDate: newAggregatedStatisticsFilters.dates?.startDate
                    ? new Date(newAggregatedStatisticsFilters.dates.startDate)
                    : null,
                endDate: newAggregatedStatisticsFilters.dates?.endDate ? new Date(newAggregatedStatisticsFilters.dates.endDate) : null,
                period: newAggregatedStatisticsFilters.dates?.period ?? MalouPeriod.LAST_THIRTY_DAYS,
            },
            platforms: {
                [PlatformFilterPage.BOOSTERS]: newAggregatedStatisticsFilters.platforms[PlatformFilterPage.BOOSTERS],
                [PlatformFilterPage.E_REPUTATION]: newAggregatedStatisticsFilters.platforms[PlatformFilterPage.E_REPUTATION],
                [PlatformFilterPage.SEO]: newAggregatedStatisticsFilters.platforms[PlatformFilterPage.SEO],
                [PlatformFilterPage.SOCIAL_NETWORKS]: newAggregatedStatisticsFilters.platforms[PlatformFilterPage.SOCIAL_NETWORKS],
            },
            restaurantIds: newAggregatedStatisticsFilters.restaurantIds,
            roiRestaurantIds: newAggregatedStatisticsFilters.roiRestaurantIds,
            totemIds: newAggregatedStatisticsFilters.totemIds,
            timeScale: newAggregatedStatisticsFilters.timeScale,
            monthYearPeriod: newAggregatedStatisticsFilters.monthYearPeriod,
            comparisonPeriod: newAggregatedStatisticsFilters.comparisonPeriod,
        };

        await this._userFiltersRepository.updateAggregatedStatisticsFilters(userId, updatedData);
    }
}
