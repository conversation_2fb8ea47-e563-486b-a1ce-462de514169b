import { NextFunction, Request, Response } from 'express';
import { singleton } from 'tsyringe';
import { DeepRequired } from 'utility-types';

import {
    AggregatedReviewsFiltersBodyDto,
    aggregatedReviewsFiltersBodyValidator,
    AggregatedStatisticsFiltersBodyDto,
    aggregatedStatisticsFiltersBodyValidator,
    AggregatedUserFiltersResponseDto,
    restaurantFiltersByUserIdAndRestaurantIdParamsDto,
    restaurantFiltersByUserIdAndRestaurantIdParamsValidator,
    RestaurantReviewsFiltersBodyDto,
    restaurantReviewsFiltersBodyValidator,
    RestaurantStatisticsFiltersBodyDto,
    restaurantStatisticsFiltersBodyValidator,
    SuccessResponse,
    UserFiltersByUserIdParamsDto,
    userFiltersByUserIdParamsValidator,
    UserRestaurantFiltersResponseDto,
} from '@malou-io/package-dto';
import { ApiResultV2, MonthYearPeriod } from '@malou-io/package-utils';

import { Body, Params } from ':helpers/decorators/validators';
import { GetAggregatedUserFiltersByUserId } from ':modules/user-filters/use-cases/get-aggregated-user-filters-by-user-id/get-aggregated-user-filters-by-user-id.use-case';
import { GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase } from ':modules/user-filters/use-cases/get-user-restaurant-filters/get-user-restaurant-filters-by-user-and-restaurant-id.use-case';
import { UpdateAggregatedReviewsFiltersUseCase } from ':modules/user-filters/use-cases/update-aggregated-reviews-filters/update-aggregated-reviews-filters.use-case';
import { UpdateAggregatedStatisticsFiltersUseCase } from ':modules/user-filters/use-cases/update-aggregated-statistics-filters/update-aggregated-statistics-filters.use-case';
import { UpdateUserRestaurantReviewsFiltersUseCase } from ':modules/user-filters/use-cases/update-user-restaurant-reviews-filters/update-user-restaurant-reviews-filters.use-case';
import { UpdateUserRestaurantStatisticsFiltersUseCase } from ':modules/user-filters/use-cases/update-user-restaurant-statistics-filters/update-user-restaurant-statistics-filters.use-case';

@singleton()
export default class UserFiltersController {
    constructor(
        private readonly _getAggregatedUserFiltersByUserId: GetAggregatedUserFiltersByUserId,
        private readonly _updateAggregatedStatisticsFiltersUseCase: UpdateAggregatedStatisticsFiltersUseCase,
        private readonly _updateAggregatedReviewsFiltersUseCase: UpdateAggregatedReviewsFiltersUseCase,
        private readonly _updateRestaurantStatsFiltersUseCase: UpdateUserRestaurantStatisticsFiltersUseCase,
        private readonly _getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase: GetUserRestaurantFiltersByUserIdAndRestaurantIdUseCase,
        private readonly _updateRestaurantReviewsFiltersUseCase: UpdateUserRestaurantReviewsFiltersUseCase
    ) {}

    @Params(userFiltersByUserIdParamsValidator)
    async handleGetUserAggregatedFiltersByUserId(
        req: Request<UserFiltersByUserIdParamsDto>,
        res: Response<ApiResultV2<AggregatedUserFiltersResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { userId } = req.params;
            const userFilters = await this._getAggregatedUserFiltersByUserId.execute(userId);
            return res.status(200).json({
                data: userFilters,
            });
        } catch (e) {
            next(e);
        }
    }

    @Params(userFiltersByUserIdParamsValidator)
    @Body(aggregatedStatisticsFiltersBodyValidator)
    async handleUpdateAggregatedStatisticsFiltersByUserId(
        req: Request<UserFiltersByUserIdParamsDto, any, AggregatedStatisticsFiltersBodyDto>,
        res: Response<ApiResultV2<SuccessResponse>>,
        next: NextFunction
    ) {
        try {
            const { userId } = req.params;
            const { dates, platforms, restaurantIds, roiRestaurantIds, totemIds, timeScale, monthYearPeriod } = req.body;
            await this._updateAggregatedStatisticsFiltersUseCase.execute(userId, {
                dates,
                platforms,
                restaurantIds,
                roiRestaurantIds,
                totemIds,
                timeScale,
                monthYearPeriod: monthYearPeriod as MonthYearPeriod,
                comparisonPeriod: req.body.comparisonPeriod,
            });
            return res.status(200).json({ data: { success: true } });
        } catch (e) {
            next(e);
        }
    }

    @Params(userFiltersByUserIdParamsValidator)
    @Body(aggregatedReviewsFiltersBodyValidator)
    async handleUpdateAggregatedReviewsFiltersByUserId(
        req: Request<UserFiltersByUserIdParamsDto, any, AggregatedReviewsFiltersBodyDto>,
        res: Response<ApiResultV2<SuccessResponse>>,
        next: NextFunction
    ) {
        try {
            const { userId } = req.params;
            const { filters, restaurantIds } = req.body;
            await this._updateAggregatedReviewsFiltersUseCase.execute(userId, {
                filters,
                restaurantIds,
            });
            return res.status(200).json({ data: { success: true } });
        } catch (e) {
            next(e);
        }
    }

    @Params(restaurantFiltersByUserIdAndRestaurantIdParamsValidator)
    async handleGetUserRestaurantFiltersByUserIdAndRestaurantId(
        req: Request<restaurantFiltersByUserIdAndRestaurantIdParamsDto, any, RestaurantStatisticsFiltersBodyDto>,
        res: Response<ApiResultV2<UserRestaurantFiltersResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { userId, restaurantId } = req.params;
            const data = await this._getUserRestaurantFiltersByUserIdAndRestaurantIdUseCase.execute({
                userId,
                restaurantId,
            });
            return res.status(200).json({ data });
        } catch (e) {
            next(e);
        }
    }

    @Params(restaurantFiltersByUserIdAndRestaurantIdParamsValidator)
    @Body(restaurantStatisticsFiltersBodyValidator)
    async handleUpdateRestaurantStatsFiltersByUserIdAndRestaurantId(
        req: Request<restaurantFiltersByUserIdAndRestaurantIdParamsDto, any, DeepRequired<RestaurantStatisticsFiltersBodyDto>>,
        res: Response<ApiResultV2<SuccessResponse>>,
        next: NextFunction
    ) {
        try {
            const { userId, restaurantId } = req.params;
            const { dates, platforms, timeScale, totemIds, comparisonPeriod, monthYearPeriod } = req.body;
            await this._updateRestaurantStatsFiltersUseCase.execute({
                userId,
                restaurantId,
                newRestaurantStatisticsFilters: {
                    dates,
                    platforms,
                    timeScale,
                    totemIds,
                    comparisonPeriod,
                    monthYearPeriod,
                },
            });
            return res.status(200).json({ data: { success: true } });
        } catch (e) {
            next(e);
        }
    }

    @Params(restaurantFiltersByUserIdAndRestaurantIdParamsValidator)
    @Body(restaurantReviewsFiltersBodyValidator)
    async handleUpdateRestaurantReviewsFiltersByUserIdAndRestaurantId(
        req: Request<restaurantFiltersByUserIdAndRestaurantIdParamsDto, any, RestaurantReviewsFiltersBodyDto>,
        res: Response<ApiResultV2<SuccessResponse>>,
        next: NextFunction
    ) {
        try {
            const { userId, restaurantId } = req.params;

            await this._updateRestaurantReviewsFiltersUseCase.execute({
                userId,
                restaurantId,
                newRestaurantReviewsFilters: req.body,
            });
            return res.status(200).json({ data: { success: true } });
        } catch (e) {
            next(e);
        }
    }
}
