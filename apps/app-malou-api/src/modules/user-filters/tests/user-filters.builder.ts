import { Builder } from 'builder-pattern';
import { DateTime } from 'luxon';

import { IUserFilters, newDbId } from '@malou-io/package-models';
import { MalouComparisonPeriod, MalouPeriod, MalouTimeScalePeriod } from '@malou-io/package-utils';

const _buildUserFilters = (userFilters: IUserFilters) => Builder<IUserFilters>(userFilters);

export const getDefaultUserFilters = () =>
    _buildUserFilters({
        _id: newDbId(),
        userId: newDbId(),
        aggregatedStatistics: {
            dates: {
                period: MalouPeriod.LAST_THIRTY_DAYS,
            },
            platforms: {
                BOOSTERS: [],
                E_REPUTATION: [],
                SEO: [],
                SOCIAL_NETWORKS: [],
            },
            restaurantIds: [],
            roiRestaurantIds: [],
            timeScale: MalouTimeScalePeriod.LAST_THIRTY_DAYS,
            totemIds: [],
            monthYearPeriod: {
                startMonthYear: { month: 1, year: 2021 },
                endMonthYear: { month: 1, year: 2021 },
            },
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
        },
        statisticsPerRestaurant: [],
        aggregatedReviews: {
            filters: {
                period: MalouPeriod.ALL,
                answered: true,
                archived: false,
                notAnswerable: true,
                notAnswered: true,
                pending: true,
                platforms: [],
                ratings: [1, 2, 3, 4, 5, 0],
                showPrivate: true,
                text: '',
                unarchived: true,
                withoutText: true,
                withText: true,
            },
            restaurantIds: [],
        },
        reviewsPerRestaurant: [],
        createdAt: DateTime.now().toJSDate(),
        updatedAt: DateTime.now().toJSDate(),
    });

const _buildUserRestaurantsStatisticsFilters = (userRestaurantStatisticsFilters: IUserFilters['statisticsPerRestaurant'][0]) =>
    Builder<IUserFilters['statisticsPerRestaurant'][0]>(userRestaurantStatisticsFilters);

export const getDefaultUserRestaurantStatisticsFilters = () =>
    _buildUserRestaurantsStatisticsFilters({
        restaurantId: newDbId(),
        filters: {
            dates: {
                startDate: undefined,
                endDate: undefined,
                period: MalouPeriod.LAST_THIRTY_DAYS,
            },
            platforms: {
                E_REPUTATION: [],
                SOCIAL_NETWORKS: [],
            },
            timeScale: MalouTimeScalePeriod.LAST_THIRTY_DAYS,
            totemIds: [],
            comparisonPeriod: MalouComparisonPeriod.PREVIOUS_PERIOD,
            monthYearPeriod: {
                startMonthYear: { month: 1, year: 2021 },
                endMonthYear: { month: 1, year: 2021 },
            },
        },
    });

const _buildUserRestaurantsReviewsFilters = (userRestaurantStatisticsFilters: IUserFilters['reviewsPerRestaurant'][0]) =>
    Builder<IUserFilters['reviewsPerRestaurant'][0]>(userRestaurantStatisticsFilters);

export const getDefaultUserRestaurantReviewsFilters = () =>
    _buildUserRestaurantsReviewsFilters({
        restaurantId: newDbId(),
        filters: {
            answered: true,
            archived: false,
            notAnswerable: true,
            notAnswered: true,
            pending: true,
            period: MalouPeriod.ALL,
            platforms: [],
            ratings: [1, 2, 3, 4, 5, 0],
            showPrivate: true,
            unarchived: true,
            withoutText: true,
            withText: true,
            text: '',
        },
    });
