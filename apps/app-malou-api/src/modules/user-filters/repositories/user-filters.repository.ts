import { toString } from 'lodash';
import { singleton } from 'tsyringe';

import { EntityRepository, IUserFilters, toDbId, UserFiltersModel } from '@malou-io/package-models';

import { UserFilters } from ':modules/user-filters/entities/user-filters.entity';

@singleton()
export default class UserFiltersRepository extends EntityRepository<IUserFilters> {
    constructor() {
        super(UserFiltersModel);
    }

    async getUserFiltersByUserId(userId: string): Promise<UserFilters | null> {
        const doc = await this.findOne({
            filter: { userId: toDbId(userId) },
            options: {
                lean: true,
            },
        });
        if (!doc) return null;
        return this._toEntity(doc);
    }

    async createUserFilters(newUserFilters: UserFilters): Promise<UserFilters> {
        const newDocData = this.toDocument(newUserFilters);
        const doc = await this.create({ data: newDocData, options: { lean: true } });
        return this._toEntity(doc);
    }

    async updateAggregatedStatisticsFilters(
        userId: string,
        newAggregatedStatisticsFilters: UserFilters['aggregatedStatisticsFilters']
    ): Promise<void> {
        await this.findOneAndUpdate({
            filter: { userId: toDbId(userId) },
            update: { aggregatedStatistics: newAggregatedStatisticsFilters },
        });
    }

    async updateAggregatedReviewsFilters(
        userId: string,
        newAggregatedReviewsFilters: UserFilters['aggregatedReviewsFilters']
    ): Promise<void> {
        await this.findOneAndUpdate({
            filter: { userId: toDbId(userId) },
            update: { aggregatedReviews: newAggregatedReviewsFilters },
        });
    }

    async updateUserRestaurantStatisticsFilters(
        userId: string,
        restaurantId: string,
        newStatisticsFilters: UserFilters['statisticsFiltersPerRestaurant'][0]['filters']
    ): Promise<void> {
        await this.aggregate([
            {
                $match: { userId: toDbId(userId), statisticsPerRestaurant: { $ne: [] } },
            },
            {
                $set: {
                    statisticsPerRestaurant: {
                        $map: {
                            input: '$statisticsPerRestaurant',
                            as: 'restaurantStatisticsFilter',
                            in: {
                                $cond: {
                                    if: {
                                        $eq: ['$$restaurantStatisticsFilter.restaurantId', toDbId(restaurantId)],
                                    },
                                    then: { restaurantId: toDbId(restaurantId), filters: { ...newStatisticsFilters } },
                                    else: '$$restaurantStatisticsFilter',
                                },
                            },
                        },
                    },
                },
            },
            {
                $merge: {
                    into: 'userfilters',
                    whenMatched: 'merge',
                    whenNotMatched: 'discard',
                },
            },
        ]);
    }

    async updateUserRestaurantReviewsFilters(
        userId: string,
        restaurantId: string,
        newReviewsFilters: UserFilters['reviewsFiltersPerRestaurant'][0]['filters']
    ): Promise<void> {
        await this.aggregate([
            {
                $match: { userId: toDbId(userId), reviewsPerRestaurant: { $ne: [] } },
            },
            {
                $set: {
                    reviewsPerRestaurant: {
                        $map: {
                            input: '$reviewsPerRestaurant',
                            as: 'restaurantReviewsFilter',
                            in: {
                                $cond: {
                                    if: {
                                        $eq: ['$$restaurantReviewsFilter.restaurantId', toDbId(restaurantId)],
                                    },
                                    then: { restaurantId: toDbId(restaurantId), filters: { ...newReviewsFilters } },
                                    else: '$$restaurantReviewsFilter',
                                },
                            },
                        },
                    },
                },
            },
            {
                $merge: {
                    into: 'userfilters',
                    whenMatched: 'merge',
                    whenNotMatched: 'discard',
                },
            },
        ]);
    }

    async updateFiltersPerRestaurant({
        userId,
        restaurantId,
        newUserRestaurantReviewsFilters,
        newUserRestaurantStatisticsFilters,
    }: {
        userId: string;
        restaurantId: string;
        newUserRestaurantStatisticsFilters: UserFilters['statisticsFiltersPerRestaurant'][0]['filters'];
        newUserRestaurantReviewsFilters: UserFilters['reviewsFiltersPerRestaurant'][0]['filters'];
    }): Promise<void> {
        await this.findOneAndUpdate({
            filter: { userId: toDbId(userId) },
            update: {
                $push: {
                    statisticsPerRestaurant: {
                        restaurantId: toDbId(restaurantId),
                        filters: newUserRestaurantStatisticsFilters,
                    },
                    reviewsPerRestaurant: {
                        restaurantId: toDbId(restaurantId),
                        filters: newUserRestaurantReviewsFilters,
                    },
                },
            },
        });
    }

    private _toEntity(doc: IUserFilters): UserFilters {
        return new UserFilters({
            id: doc._id.toString(),
            userId: doc.userId.toString(),
            aggregatedStatisticsFilters: {
                dates: {
                    startDate: doc.aggregatedStatistics.dates.startDate ?? null,
                    endDate: doc.aggregatedStatistics.dates.endDate ?? null,
                    period: doc.aggregatedStatistics.dates.period,
                },
                restaurantIds: doc.aggregatedStatistics.restaurantIds.map(toString),
                roiRestaurantIds: doc.aggregatedStatistics.roiRestaurantIds.map(toString),
                totemIds: doc.aggregatedStatistics.totemIds.map(toString),
                timeScale: doc.aggregatedStatistics.timeScale,
                platforms: doc.aggregatedStatistics.platforms,
                monthYearPeriod: doc.aggregatedStatistics.monthYearPeriod,
                comparisonPeriod: doc.aggregatedStatistics.comparisonPeriod,
            },
            aggregatedReviewsFilters: {
                filters: { ...doc.aggregatedReviews.filters, text: doc.aggregatedReviews.filters.text ?? '' },
                restaurantIds: doc.aggregatedReviews.restaurantIds.map(toString),
            },
            reviewsFiltersPerRestaurant: doc.reviewsPerRestaurant.map((restaurantReviewsFilter) => ({
                restaurantId: restaurantReviewsFilter.restaurantId.toString(),
                filters: { ...restaurantReviewsFilter.filters, text: restaurantReviewsFilter.filters.text ?? '' },
            })),
            statisticsFiltersPerRestaurant: doc.statisticsPerRestaurant.map((restaurantStatisticsFilter) => ({
                restaurantId: restaurantStatisticsFilter.restaurantId.toString(),
                filters: {
                    dates: {
                        startDate: restaurantStatisticsFilter.filters.dates.startDate ?? null,
                        endDate: restaurantStatisticsFilter.filters.dates.endDate ?? null,
                        period: restaurantStatisticsFilter.filters.dates.period,
                    },
                    platforms: restaurantStatisticsFilter.filters.platforms,
                    totemIds: restaurantStatisticsFilter.filters.totemIds.map(toString),
                    timeScale: restaurantStatisticsFilter.filters.timeScale,
                    comparisonPeriod: restaurantStatisticsFilter.filters.comparisonPeriod,
                    monthYearPeriod: restaurantStatisticsFilter.filters.monthYearPeriod,
                },
            })),
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
        });
    }

    toDocument(entity: UserFilters): IUserFilters {
        return {
            _id: toDbId(entity.id),
            userId: toDbId(entity.userId),
            aggregatedStatistics: {
                dates: {
                    startDate: entity.aggregatedStatisticsFilters?.dates.startDate ?? undefined,
                    endDate: entity.aggregatedStatisticsFilters?.dates.endDate ?? undefined,
                    period: entity.aggregatedStatisticsFilters?.dates.period,
                },
                restaurantIds: entity.aggregatedStatisticsFilters?.restaurantIds.map(toDbId),
                roiRestaurantIds: entity.aggregatedStatisticsFilters?.roiRestaurantIds.map(toDbId),
                totemIds: entity.aggregatedStatisticsFilters?.totemIds.map(toDbId),
                timeScale: entity.aggregatedStatisticsFilters?.timeScale,
                platforms: entity.aggregatedStatisticsFilters?.platforms,
                monthYearPeriod: entity.aggregatedStatisticsFilters?.monthYearPeriod,
                comparisonPeriod: entity.aggregatedStatisticsFilters?.comparisonPeriod,
            },
            aggregatedReviews: {
                filters: entity.aggregatedReviewsFilters?.filters,
                restaurantIds: entity.aggregatedReviewsFilters?.restaurantIds.map(toDbId),
            },
            reviewsPerRestaurant: entity.reviewsFiltersPerRestaurant?.map((restaurantReviewsFilter) => ({
                restaurantId: toDbId(restaurantReviewsFilter.restaurantId),
                filters: restaurantReviewsFilter.filters,
            })),
            statisticsPerRestaurant: entity.statisticsFiltersPerRestaurant?.map((restaurantStatisticsFilter) => ({
                restaurantId: toDbId(restaurantStatisticsFilter.restaurantId),
                filters: {
                    dates: {
                        startDate: restaurantStatisticsFilter.filters.dates.startDate ?? undefined,
                        endDate: restaurantStatisticsFilter.filters.dates.endDate ?? undefined,
                        period: restaurantStatisticsFilter.filters.dates.period,
                    },
                    platforms: restaurantStatisticsFilter.filters.platforms,
                    totemIds: restaurantStatisticsFilter.filters.totemIds.map(toDbId),
                    timeScale: restaurantStatisticsFilter.filters.timeScale,
                    comparisonPeriod: restaurantStatisticsFilter.filters.comparisonPeriod,
                    monthYearPeriod: restaurantStatisticsFilter.filters.monthYearPeriod,
                },
            })),
            createdAt: new Date(),
            updatedAt: new Date(),
        };
    }
}
