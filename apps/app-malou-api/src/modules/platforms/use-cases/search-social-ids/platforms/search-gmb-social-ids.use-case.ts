import { singleton } from 'tsyringe';

import { isFulfilled, scopes } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { GmbCredentialsRepository } from ':modules/credentials/platforms/gmb/gmb.repository';
import { GmbApiProviderUseCases } from ':modules/credentials/platforms/gmb/gmb.use-cases';
import { Account, FetchedAccountLocationWithAccess } from ':modules/credentials/platforms/gmb/interfaces';
import { CredentialValidityStatus } from ':modules/credentials/platforms/interfaces';
import { GmbMapper } from ':modules/platforms/platforms/gmb/gmb-mapper';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';

@singleton()
export class SearchGmbSocialIdsUseCase {
    constructor(
        private readonly _gmbApiProviderUseCases: GmbApiProviderUseCases,
        private readonly _gmbCredentialsRepository: GmbCredentialsRepository,
        private readonly _gmbMapper: GmbMapper
    ) {}

    async execute({ credentialId }: { credentialId: string }): Promise<{ list: MalouRestaurantSearchResult[] }> {
        const accounts = await this._gmbApiProviderUseCases.fetchAccounts(credentialId);
        logger.info('[SEARCH_SOCIAL_IDS] gmb api response', { accounts });
        const credential = await this._gmbCredentialsRepository.getGmbCredentialById(credentialId);
        if (!credential) {
            return { list: [] };
        }
        const { scope: gmbScope } = credential;
        const access = this._gmbPermissionsState(accounts, gmbScope?.split(' ') ?? []);
        const allLocationsPromises: Promise<FetchedAccountLocationWithAccess[]>[] = [];
        for (const account of accounts) {
            const accountId = account.name.split('/')[1];
            const locationPromises: Promise<FetchedAccountLocationWithAccess[]> = this._gmbApiProviderUseCases
                .fetchAccountLocations(accountId, credentialId)
                .then((locs) => {
                    logger.info('[SEARCH_SOCIAL_IDS] locations for account', {
                        accountId,
                        locs: locs.map((l) => ({ name: l.name, title: l.title, placeId: l.metadata.placeId })),
                    });
                    return locs
                        .map((location) => ({
                            ...location,
                            access,
                            accountId: `accounts/${accountId}`,
                            accountName: account.accountName,
                        }))
                        .sort((locationA, locationB) => locationA.title.localeCompare(locationB.title));
                });
            allLocationsPromises.push(locationPromises);
        }
        const allLocations = await Promise.allSettled(allLocationsPromises);
        const successfulLocations = allLocations.filter(isFulfilled).map((location) => location.value);
        return { list: this._mapSearchResultsToMalou(successfulLocations.flat(1)) };
    }

    private _gmbPermissionsState(accounts: Account[], gmbScopes: string[]): CredentialValidityStatus {
        if (!accounts) {
            return { isValid: false, missing: [] };
        }
        const missing = scopes.GMB.reduce((acc, next) => (gmbScopes.find((s) => s === next) ? acc : [...acc, next]), [] as string[]);

        return { isValid: !missing.length, missing };
    }

    private _mapSearchResultsToMalou(accountLocations: FetchedAccountLocationWithAccess[]): MalouRestaurantSearchResult[] {
        try {
            const malouData = this._gmbMapper.toMalouMapperSearch(accountLocations);
            return malouData;
        } catch (e) {
            logger.warn('[GMB_MAPPER_ERROR] Error trying to map search results', e);
            return [];
        }
    }
}
