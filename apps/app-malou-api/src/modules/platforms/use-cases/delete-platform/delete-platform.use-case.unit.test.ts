import 'reflect-metadata';

import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { BusinessCategory, PlatformKey } from '@malou-io/package-utils';

import { InjectionToken } from ':helpers/injection';
import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { CommentsRepository } from ':modules/comments/comments.repository';
import MentionsRepository from ':modules/mentions/mentions.repository';
import { getDefaultPlatform } from ':modules/platforms/tests/platform.builder';
import { DeletePlatformUseCase } from ':modules/platforms/use-cases/delete-platform/delete-platform.use-case';
import PostsUseCases from ':modules/posts/posts.use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { SubscriptionsProvider } from ':modules/restaurants/services/subscriptions.provider.interface';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

describe('DeletePlatformUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['PlatformsRepository']);
    });

    describe('execute', () => {
        it('should call all delete side effects when deleting a platform', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();
            const platformKey = PlatformKey.FACEBOOK;

            const testCase = new TestCaseBuilderV2<'platforms'>({
                seeds: {
                    platforms: {
                        data() {
                            return [getDefaultPlatform()._id(platformId).key(platformKey).restaurantId(restaurantId).build()];
                        },
                    },
                },
            });

            await testCase.build();

            // Mock all the repositories and use cases
            const reviewsRepositoryMock = {
                deleteMany: jest.fn().mockResolvedValue({}),
            } as unknown as ReviewsRepository;

            const commentsRepositoryMock = {
                deleteMany: jest.fn().mockResolvedValue({}),
            } as unknown as CommentsRepository;

            const mentionsRepositoryMock = {
                deleteMany: jest.fn().mockResolvedValue({}),
            } as unknown as MentionsRepository;

            const postsUseCasesMock = {
                deleteManyPostsAndHandleSideEffects: jest.fn().mockResolvedValue({}),
            } as unknown as PostsUseCases;

            const restaurantsRepositoryMock = {
                deletePlatformAccess: jest.fn().mockResolvedValue({}),
                findOneOrFail: jest.fn().mockResolvedValue({
                    subscriptionsProviderId: null,
                    type: BusinessCategory.LOCAL_BUSINESS,
                }),
            } as unknown as RestaurantsRepository;

            const subscriptionsProviderMock = {
                updateSubscriptionsProviderLocation: jest.fn().mockResolvedValue({}),
            } as unknown as SubscriptionsProvider;

            // Register mocks
            container.registerInstance(ReviewsRepository, reviewsRepositoryMock);
            container.registerInstance(CommentsRepository, commentsRepositoryMock);
            container.registerInstance(MentionsRepository, mentionsRepositoryMock);
            container.registerInstance(PostsUseCases, postsUseCasesMock);
            container.registerInstance(RestaurantsRepository, restaurantsRepositoryMock);
            container.registerInstance(InjectionToken.SubscriptionsProvider, subscriptionsProviderMock);

            const deletePlatformUseCase = container.resolve(DeletePlatformUseCase);

            await deletePlatformUseCase.execute(platformId.toString());

            // Verify all delete side effects were called with correct parameters
            expect(reviewsRepositoryMock.deleteMany).toHaveBeenCalledWith({
                filter: { key: platformKey, restaurantId },
            });

            expect(commentsRepositoryMock.deleteMany).toHaveBeenCalledWith({
                filter: { platformKey, restaurantId },
            });

            expect(mentionsRepositoryMock.deleteMany).toHaveBeenCalledWith({
                filter: { platformKey, restaurantId },
            });

            expect(postsUseCasesMock.deleteManyPostsAndHandleSideEffects).toHaveBeenCalledWith({
                key: platformKey,
                restaurantId,
            });

            expect(restaurantsRepositoryMock.deletePlatformAccess).toHaveBeenCalledWith(restaurantId.toString(), platformKey);

            // Verify each method was called exactly once
            expect(reviewsRepositoryMock.deleteMany).toHaveBeenCalledTimes(1);
            expect(commentsRepositoryMock.deleteMany).toHaveBeenCalledTimes(1);
            expect(mentionsRepositoryMock.deleteMany).toHaveBeenCalledTimes(1);
            expect(postsUseCasesMock.deleteManyPostsAndHandleSideEffects).toHaveBeenCalledTimes(1);
            expect(restaurantsRepositoryMock.deletePlatformAccess).toHaveBeenCalledTimes(1);

            // Verify subscriptions provider was NOT called since restaurant has no subscriptionsProviderId
            expect(subscriptionsProviderMock.updateSubscriptionsProviderLocation).not.toHaveBeenCalled();
        });

        it('should call subscriptions provider when restaurant has subscriptionsProviderId and platform deletion requires it', async () => {
            const restaurantId = newDbId();
            const platformId = newDbId();
            const platformKey = PlatformKey.FACEBOOK;
            const subscriptionsProviderId = 'test-hyperline-id';

            const testCase = new TestCaseBuilderV2<'platforms'>({
                seeds: {
                    platforms: {
                        data() {
                            return [getDefaultPlatform()._id(platformId).key(platformKey).restaurantId(restaurantId).build()];
                        },
                    },
                },
            });

            await testCase.build();

            // Mock all the repositories and use cases
            const reviewsRepositoryMock = {
                deleteMany: jest.fn().mockResolvedValue({}),
            } as unknown as ReviewsRepository;

            const commentsRepositoryMock = {
                deleteMany: jest.fn().mockResolvedValue({}),
            } as unknown as CommentsRepository;

            const mentionsRepositoryMock = {
                deleteMany: jest.fn().mockResolvedValue({}),
            } as unknown as MentionsRepository;

            const postsUseCasesMock = {
                deleteManyPostsAndHandleSideEffects: jest.fn().mockResolvedValue({}),
            } as unknown as PostsUseCases;

            const restaurantsRepositoryMock = {
                deletePlatformAccess: jest.fn().mockResolvedValue({}),
                findOneOrFail: jest.fn().mockResolvedValue({
                    subscriptionsProviderId,
                    type: BusinessCategory.BRAND,
                }),
                updateOne: jest.fn().mockResolvedValue({}),
            } as unknown as RestaurantsRepository;

            const subscriptionsProviderMock = {
                updateSubscriptionsProviderLocation: jest.fn().mockResolvedValue({}),
            } as unknown as SubscriptionsProvider;

            // Register mocks
            container.registerInstance(ReviewsRepository, reviewsRepositoryMock);
            container.registerInstance(CommentsRepository, commentsRepositoryMock);
            container.registerInstance(MentionsRepository, mentionsRepositoryMock);
            container.registerInstance(PostsUseCases, postsUseCasesMock);
            container.registerInstance(RestaurantsRepository, restaurantsRepositoryMock);
            container.registerInstance(InjectionToken.SubscriptionsProvider, subscriptionsProviderMock);

            const deletePlatformUseCase = container.resolve(DeletePlatformUseCase);

            await deletePlatformUseCase.execute(platformId.toString());

            // Verify subscriptions provider was called to clear the restaurant connection
            expect(subscriptionsProviderMock.updateSubscriptionsProviderLocation).toHaveBeenCalledWith({
                subscriptionsProviderLocationId: subscriptionsProviderId,
                malouRestaurantId: null,
            });

            // Verify subscriptions provider was called exactly once
            expect(subscriptionsProviderMock.updateSubscriptionsProviderLocation).toHaveBeenCalledTimes(1);

            // Verify restaurant was updated to remove subscriptionsProviderId
            expect(restaurantsRepositoryMock.updateOne).toHaveBeenCalledWith({
                filter: { _id: restaurantId },
                update: { subscriptionsProviderId: null },
            });

            // Verify restaurant was updated exactly once
            expect(restaurantsRepositoryMock.updateOne).toHaveBeenCalledTimes(1);
        });
    });
});
