import { z } from 'zod';

import { ApplicationLanguage } from '@malou-io/package-utils';

export const newOrganizationEventValidator = z.object({
    organizationName: z.string().transform((name) => name?.trim()),
    organizationProviderId: z.string().transform((id) => id?.trim()),
    principalUserEmail: z
        .string()
        .email()
        .transform((email) => email?.trim().toLowerCase()),
    principalUserLang: z.nativeEnum(ApplicationLanguage),
    token: z.string(),
});

export type NewOrganizationEvent = z.infer<typeof newOrganizationEventValidator>;
