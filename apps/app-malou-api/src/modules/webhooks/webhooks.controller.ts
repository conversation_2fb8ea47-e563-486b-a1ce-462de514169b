import axios, { AxiosRequestConfig } from 'axios';
import { NextFunction, Request, Response } from 'express';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { MalouErrorCode } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { Body } from ':helpers/decorators/validators';
import { logger } from ':helpers/logger';
import { RequestWithRawBody } from ':helpers/utils.types';
import MaintenanceRepository from ':modules/maintenance/maintenance.repository';
import { CreateNewOrganizationUseCase } from ':modules/webhooks/malou/use-cases/create-new-organization.use-case';
import { NewOrganizationEvent, newOrganizationEventValidator } from ':modules/webhooks/malou/validators/new-organization.validators';
import { facebookPermissionsUseCases } from ':modules/webhooks/platforms';
import FacebookWebhookMessagesUseCases from ':modules/webhooks/platforms/facebook/messages/use-cases';
import { processPermissionWebhookReceptionUseCase } from ':modules/webhooks/platforms/facebook/permissions/use-cases';
import { FacebookWebhookPostAndCommentsHandlerUseCase } from ':modules/webhooks/platforms/facebook/posts/use-cases/handle-post-and-comments.use-case';
import { InstagramWebhookPostInsightsUseCases } from ':modules/webhooks/platforms/instagram/post-insights/use-cases';
import { HandleJimoIncomingEventsUseCase } from ':modules/webhooks/platforms/jimo/use-cases/handle-jimo-incoming-events.use-case';
import { HandleTheForkIncomingEventsUseCase } from ':modules/webhooks/platforms/thefork/use-cases/handle-thefork-incoming-events.use-case';
import { HandleTiktokIncomingEventsUseCase } from ':modules/webhooks/platforms/tiktok/use-cases/handle-tiktok-incoming-events.use-case';

import { HandleFacebookWebhookMentionsUseCase } from './platforms/facebook/mentions/use-cases/handle-mentions.use-case';
import { HandleIgWebhookMentionsUseCase } from './platforms/instagram/mentions/use-cases/handle-mentions.use-case';
import { HandleIgWebhookCommentUseCase } from './platforms/instagram/posts/use-cases/handle-comment.use-case';

enum FbWebhookEntryTypes {
    COMMENT = 'comments',
    FEED = 'feed',
    MESSAGING = 'messaging',
    MENTION = 'mention',
}

enum IgWebhookEntryTypes {
    COMMENT = 'comments',
    MESSAGING = 'messaging',
    MENTIONS = 'mentions',
    STORY_INSIGHTS = 'story_insights',
}

@singleton()
export default class WebhooksController {
    constructor(
        private readonly _maintenanceRepository: MaintenanceRepository,
        private readonly _facebookMessageUseCases: FacebookWebhookMessagesUseCases,
        private readonly _instagramPostInsightsUseCases: InstagramWebhookPostInsightsUseCases,
        private readonly _facebookWebhookPostAndCommentsHandlerUseCase: FacebookWebhookPostAndCommentsHandlerUseCase,
        private readonly _handleIgWebhookCommentUseCase: HandleIgWebhookCommentUseCase,
        private readonly _handleFacebookWebhookMentionsUseCase: HandleFacebookWebhookMentionsUseCase,
        private readonly _handleIgWebhookMentionsUseCase: HandleIgWebhookMentionsUseCase,
        private readonly _handleTiktokIncomingEventsUseCase: HandleTiktokIncomingEventsUseCase,
        private readonly _handleJimoIncomingEventsUseCase: HandleJimoIncomingEventsUseCase,
        private readonly _handleTheForkIncomingEventsUseCase: HandleTheForkIncomingEventsUseCase,
        private readonly _createNewOrganizationUseCase: CreateNewOrganizationUseCase
    ) {}

    handleCheckForRedirection = async (req: Request, res: Response, next: NextFunction) => {
        const maintenance = (await this._maintenanceRepository.find({ filter: {} }))[0];
        if (maintenance?.webhookRedirectActive && process.env.I_AM_A !== 'developer') {
            for (const redirectUri of maintenance.localWebhookUris) {
                const url = redirectUri + req.originalUrl;
                logger.info('[WEBHOOK_REPRODUCING_REQUEST] :', url);
                const options: AxiosRequestConfig = { url, method: req.method as any, headers: { 'ngrok-skip-browser-warning': 1 } };
                if (req.method !== 'GET') {
                    options.data = req.body;
                }
                try {
                    await axios(options);
                } catch (err) {
                    logger.error('[ERROR_REDIRECTING_WEBHOOK]', err);
                }
            }
        }
        next();
    };

    handleFacebookWebhookVerification = async (req: Request, res: Response, next: NextFunction) => {
        if (Config.platforms.facebook.webhooks.token !== req.query['hub.verify_token']) {
            next(new MalouError(MalouErrorCode.FORBIDDEN, { message: 'Forbidden, cannot verify web hook' }));
        }
        return res.send(req.query['hub.challenge']);
    };

    handleFacebookPageWebhooks = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const entry = req.body?.entry[0];
            const { object } = req.body;
            const { id, changes, messaging } = entry;
            const entryType = messaging ? FbWebhookEntryTypes.MESSAGING : changes?.[0]?.field;
            const value = changes?.[0]?.value;
            logger.info('[WEBHOOK_FACEBOOK] - Received webhook call', {
                object,
                ...entry,
            });

            switch (entryType) {
                case FbWebhookEntryTypes.FEED:
                    const feedResult = await this._facebookWebhookPostAndCommentsHandlerUseCase.execute(value, id);
                    return res.send(feedResult);
                case FbWebhookEntryTypes.MESSAGING:
                    const isFacebookPage = true;
                    const messageResult = await this._facebookMessageUseCases.handleMessage(messaging, id, isFacebookPage);
                    return res.send(messageResult);
                case FbWebhookEntryTypes.MENTION:
                    const mentionResult = await this._handleFacebookWebhookMentionsUseCase.execute(value, id);
                    return res.send(mentionResult);
                default:
                    return res.send('not implemented');
            }
        } catch (e) {
            logger.error('[WEBHOOK_FACEBOOK_ERROR]', e);
            return res.send('error');
        }
    };

    handleFacebookPermissionsWebhooks = async (req: Request, res: Response, next: NextFunction) => {
        try {
            logger.info('[WEBHOOK_PERMISSION] - ', req.body);
            const entries = req.body?.entry; // list of entries
            const processPermissionWebhookReception = processPermissionWebhookReceptionUseCase(
                facebookPermissionsUseCases.handleUserConnectedStateChange
            );
            await processPermissionWebhookReception(entries);
            return res.json({});
        } catch (error) {
            logger.error('[WEBHOOK_FACEBOOK_PERMISSIONS_ERROR]', error);
            return res.send('error');
        }
    };

    // https://developers.facebook.com/docs/graph-api/webhooks/getting-started/webhooks-for-instagram
    handleInstagramWebhooks = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const entry = req.body?.entry[0];
            const { object } = req.body;
            const { id, changes, time, messaging } = entry;
            const entryType = messaging ? 'messaging' : changes?.[0]?.field;
            const value = changes?.[0]?.value;
            logger.info('[WEBHOOK_INSTAGRAM] - Received webhook call', {
                object,
                ...entry,
            });
            switch (entryType) {
                case IgWebhookEntryTypes.COMMENT:
                    const commentsResult = await this._handleIgWebhookCommentUseCase.execute(value, time, id);
                    return res.send(commentsResult);
                case IgWebhookEntryTypes.MESSAGING:
                    const isFacebookPage = false;
                    const messageResult = await this._facebookMessageUseCases.handleMessage(messaging, id, isFacebookPage);
                    return res.send(messageResult);
                case IgWebhookEntryTypes.MENTIONS:
                    const mentionResult = await this._handleIgWebhookMentionsUseCase.execute(value, id);
                    return res.send(mentionResult);
                case IgWebhookEntryTypes.STORY_INSIGHTS:
                    const storyInsights = await this._instagramPostInsightsUseCases.handleIgStoryInsights(value, id);
                    return res.send(storyInsights);
                default:
                    return res.send('not implemented');
            }
        } catch (error) {
            logger.error('[WEBHOOK_INSTAGRAM_ERROR]', error);
            return res.send('error');
        }
    };

    // https://developers.tiktok.com/doc/webhooks-events
    async handleTiktokPostEvents(req: RequestWithRawBody, res: Response, next: NextFunction) {
        try {
            logger.info('[WEBHOOKS] [TIKTOK] - Received webhook call', req.body);

            const signature = req.headers?.['tiktok-signature']?.toString();
            assert(signature, 'TikTok signature missing');
            assert(req.rawBody, 'TikTok raw body missing');

            // Run it in async to answer instantaneously to Tiktok
            this._handleTiktokIncomingEventsUseCase.execute({ body: req.body, rawBody: req.rawBody, signature }).catch((err) => {
                logger.error('[WEBHOOKS] [TIKTOK] - Error handling incoming events', {
                    err,
                });
            });

            return res.status(200).json({});
        } catch (err) {
            next(err);
        }
    }

    async handleJimoEvents(req: RequestWithRawBody, res: Response, next: NextFunction) {
        try {
            logger.info('[WEBHOOKS] [JIMO] - Received webhook call', req.body);
            await this._handleJimoIncomingEventsUseCase.execute({ body: req.body });
            return res.status(200).json({});
        } catch (err) {
            logger.error('[WEBHOOKS] [JIMO] - Error handling incoming events', {
                err,
            });
            next(err);
        }
    }

    @Body(newOrganizationEventValidator)
    async handleMalouNewOrganization(req: Request<any, any, NewOrganizationEvent>, res: Response, next: NextFunction) {
        try {
            logger.info('[WEBHOOKS] [MALOU] - Received new organization request', req.body);
            if (req.body.token !== process.env.MALOU_INTERNAL_API_TOKEN) {
                throw new MalouError(MalouErrorCode.FORBIDDEN, { message: 'Forbidden' });
            }
            await this._createNewOrganizationUseCase.execute(req.body);
            return res.status(200).json({});
        } catch (err) {
            next(err);
        }
    }

    // https://docs.thefork.io/B2B-API/event-webhook-flow
    async handleTheForkEvents(req: Request<any, any, any, any>, res: Response, next: NextFunction) {
        try {
            logger.info('[WEBHOOKS] [THEFORK] - Received webhook call', req.body, req.query);
            await this._handleTheForkIncomingEventsUseCase.execute({ body: req.body, query: req.query });
            return res.status(200).json({});
        } catch (err) {
            next(err);
        }
    }
}
