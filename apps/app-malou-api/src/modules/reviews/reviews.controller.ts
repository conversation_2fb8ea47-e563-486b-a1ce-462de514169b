import { ForbiddenError, subject } from '@casl/ability';
import { NextFunction, Request, Response } from 'express';
import { intersectionBy } from 'lodash';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import {
    AddTranslationToReviewBodyDto,
    addTranslationToReviewBodyValidator,
    AddTranslationToReviewParamsDto,
    addTranslationToReviewParamsValidator,
    createPrivateReviewBodyValidator,
    CreatePrivateReviewDto,
    DeleteReviewParamsDto,
    deleteReviewParamsValidator,
    DeleteReviewsForPlatformParamsDto,
    DeleteReviewsForPlatformParamsValidator,
    DownloadReviewsAsPdfResponseDto,
    FetchAiRelevantBricksParamsDto,
    fetchAiRelevantBricksParamsValidator,
    GetChartDataParamsDto,
    getChartDataParamsValidator,
    GetChartRestaurantsReviewsTotalQueryDto,
    getChartRestaurantsReviewsTotalQueryValidator,
    GetFilteredReviewsAsPdfBodyDto,
    getFilteredReviewsAsPdfBodyValidator,
    GetFilteredReviewsAsPdfQueryDto,
    getFilteredReviewsAsPdfQueryValidator,
    GetRestaurantsReviewsAnswerRateQueryDto,
    getRestaurantsReviewsAnswerRateQueryValidator,
    GetRestaurantsReviewsAnswerTimeQueryDto,
    getRestaurantsReviewsAnswerTimeQueryValidator,
    GetRestaurantsReviewsAverageRatingDto,
    GetRestaurantsReviewsAverageRatingQueryDto,
    getRestaurantsReviewsAverageRatingQueryValidator,
    getRestaurantsReviewsEvolutionQueryValidator,
    GetRestaurantsReviewsEvolutionRateQueryDto,
    GetRestaurantsReviewsOutputQueryDto,
    getRestaurantsReviewsQueryValidator,
    GetRestaurantsReviewsRatingQueryDto,
    getRestaurantsReviewsRatingQueryValidator,
    GetRestaurantsReviewsV2QueryDto,
    getRestaurantsReviewsV2QueryValidator,
    GetRestaurantsReviewsV2ResponseDto,
    GetRestaurantsUnansweredCountBodyDto,
    getRestaurantsUnansweredCountBodyValidator,
    GetRestaurantsUnansweredCountQueryDto,
    getRestaurantsUnansweredCountQueryValidator,
    GetRestaurantsUnansweredCountResponseDto,
    GetReviewByIdParamsDto,
    getReviewByIdParamsValidator,
    GetReviewCountBodyDto,
    getReviewCountBodyValidator,
    GetReviewCountResponseDto,
    GetReviewerNameValidationParamsDto,
    getReviewerNameValidationParamsValidator,
    GetReviewerNameValidationQueryDto,
    getReviewerNameValidationQueryValidator,
    GetReviewPageBodyDto,
    getReviewPageBodyValidator,
    GetReviewPageParamsDto,
    getReviewPageParamsValidator,
    GetReviewPageQueryDto,
    getReviewPageQueryValidator,
    GetReviewPageResponseDto,
    GetReviewsBySegmentTopicBodyDto,
    getReviewsBySegmentTopicBodyValidator,
    GetReviewsBySocialIdsBodyDto,
    getReviewsBySocialIdsBodyValidator,
    GetReviewsEvolutionTotalBodyDto,
    getReviewsEvolutionTotalBodyValidator,
    GetReviewsEvolutionTotalResponseBodyDto,
    GetReviewsWithSegmentAnalysesParamsDto,
    getReviewsWithSegmentAnalysesParamsValidator,
    GetReviewsWithSegmentAnalysesQueryDto,
    getReviewsWithSegmentAnalysesQueryValidator,
    GetReviewTotalCountParamsDto,
    getReviewTotalCountParamsValidator,
    GetTotalReviewCountResponseDto,
    LinkEmailToPrivateReviewBodyDto,
    linkEmailToPrivateReviewBodyValidator,
    LinkEmailToPrivateReviewParamsDto,
    linkEmailToPrivateReviewParamsValidator,
    restaurantIdParamsValidator,
    ReviewResponseDto,
    ReviewWithTranslationsResponseDto,
    SynchronizeReviewsQueryDto,
    synchronizeReviewsQueryValidator,
    SynchronizeReviewsResponseDto,
    UpdateKeywordsLangBodyDto,
    updateKeywordsLangBodyValidator,
    UpdateKeywordsLangParamsDto,
    updateKeywordsLangParamsValidator,
    UpdateReviewArchivedValueBodyDto,
    updateReviewArchivedValueBodyValidator,
    UpdateReviewArchivedValueParamsDto,
    updateReviewArchivedValueParamsValidator,
} from '@malou-io/package-dto';
import {
    ApiResult,
    ApiResultError,
    ApiResultV2,
    CaslAction,
    CaslSubject,
    errorReplacer,
    FilterType,
    MalouErrorCode,
} from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';
import { Body, Params, Query } from ':helpers/decorators/validators';
import { ReviewFilters } from ':helpers/filters/review-filters';
import { logger } from ':helpers/logger';
import { ReviewPagination } from ':helpers/pagination';
import { RequestWithPermissions, RequestWithUser } from ':helpers/utils.types';
import CampaignsRepository from ':modules/campaigns/campaigns.repository';
import { PlatformsUseCases } from ':modules/platforms/platforms.use-cases';
import { PrivateReviewsRepository } from ':modules/private-reviews/private-reviews.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ReviewFiltersInput } from ':modules/reviews/reviews.interfaces';
import { ReviewsDtoMapper } from ':modules/reviews/reviews.mapper.dto';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import ReviewsUseCases from ':modules/reviews/reviews.use-cases';
import { DownloadReviewsAsPdfUseCase, GetReviewsPaginatedUseCase } from ':modules/reviews/use-cases';
import { GetReviewsWithSegmentAnalysesUseCase } from ':modules/reviews/use-cases//get-reviews-with-segment-analyses/get-reviews-with-segment-analyses.use-case';
import { AddTranslationToReviewUseCase } from ':modules/reviews/use-cases/add-translation-to-review/add-translation-to-review.use-case';
import { GetAverageAnswerTimeUseCase } from ':modules/reviews/use-cases/get-average-answer-time/get-average-answer-time.use-case';
import { GetReviewByIdUseCase } from ':modules/reviews/use-cases/get-review-by-id/get-review-by-id.use-case';
import { GetReviewerNameValidationUseCase } from ':modules/reviews/use-cases/get-reviewer-name-validation/get-reviewer-name-validation.use-case';
import { GetReviewsBySegmentTopicUseCase } from ':modules/reviews/use-cases/get-reviews-by-segment-topic/get-reviews-by-segment-topic.use-case';
import { GetReviewsBySocialIdUseCase } from ':modules/reviews/use-cases/get-reviews-by-social-id/get-reviews-by-social-id.use-case';
import { LinkClientToPrivateReviewUseCase } from ':modules/reviews/use-cases/link-client-to-private-review/link-client-to-private-review.use-case';
import { ReplyToReviewUseCase } from ':modules/reviews/use-cases/reply-to-review/reply-to-review.use-case';
import { UpdateReviewRelevantBricksUseCase } from ':modules/reviews/use-cases/update-review-relevant-bricks/update-review-relevant-bricks.use-case';
import { GetFiltersForChartDataUtil } from ':modules/reviews/utils/get-filters-for-chart-data-util';
import { UsersRepository } from ':modules/users/users.repository';
import {
    isFeatureAvailableForRestaurant,
    isFeatureAvailableForRestaurants,
} from ':services/experimentations-service/experimentation.service';

@singleton()
export default class ReviewsController {
    constructor(
        private readonly _platformsUseCases: PlatformsUseCases,
        private readonly _reviewsUseCases: ReviewsUseCases,
        private readonly _campaignsRepository: CampaignsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _privateReviewsRepository: PrivateReviewsRepository,
        private readonly _usersRepository: UsersRepository,
        private readonly _reviewsMapper: ReviewsDtoMapper,
        private readonly _downloadReviewsAsPdfService: DownloadReviewsAsPdfUseCase,
        private readonly _getReviewsPaginatedUseCase: GetReviewsPaginatedUseCase,
        private readonly _addTranslationToReviewUseCase: AddTranslationToReviewUseCase,
        private readonly _updateReviewRelevantBricksUseCase: UpdateReviewRelevantBricksUseCase,
        private readonly _getReviewByIdUseCase: GetReviewByIdUseCase,
        private readonly _getReviewsBySocialIdUseCase: GetReviewsBySocialIdUseCase,
        private readonly _getReviewsBySegmentTopicUseCase: GetReviewsBySegmentTopicUseCase,
        private readonly _getReviewsWithSegmentAnalysesUseCase: GetReviewsWithSegmentAnalysesUseCase,
        private readonly _getAverageAnswerTimeUseCase: GetAverageAnswerTimeUseCase,
        private readonly _replyToReviewUseCase: ReplyToReviewUseCase,
        private readonly _getReviewerNameValidationUseCase: GetReviewerNameValidationUseCase,
        private readonly _getFiltersForChartDataUtil: GetFiltersForChartDataUtil,
        private readonly _linkClientToPrivateReviewUseCase: LinkClientToPrivateReviewUseCase
    ) {}

    @Params(getChartDataParamsValidator)
    @Query(getRestaurantsReviewsAnswerRateQueryValidator)
    async handleGetAnsweredAndNotAnsweredReviews(
        req: Request<any, any, any, GetRestaurantsReviewsAnswerRateQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, platforms, comparisonPeriod } = req.query;
            const filters = await this._getFiltersForChartDataUtil.getReviewFilters({
                startDate,
                endDate,
                restaurantId,
                comparisonPeriod,
                platforms,
            });

            if (filters.periodCantBeSet()) {
                return null;
            }
            const searchQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });
            const results = await this._reviewsUseCases.getAnsweredAndNotAnsweredReviews(searchQuery);
            return res.json({
                data: {
                    results,
                },
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(getChartDataParamsValidator)
    @Query(getRestaurantsReviewsAnswerTimeQueryValidator)
    async handleGetAnswerTime(req: Request<any, any, any, GetRestaurantsReviewsAnswerTimeQueryDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const { comparisonPeriod, endDate, platforms, startDate } = req.query;

            const filters = await this._getFiltersForChartDataUtil.getReviewFilters({
                startDate,
                endDate,
                restaurantId,
                comparisonPeriod,
                platforms,
            });

            if (filters.periodCantBeSet()) {
                return null;
            }
            const searchQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });
            const averageAnsweredTime: number | null = await this._getAverageAnswerTimeUseCase.execute(searchQuery);
            return res.json({
                data: averageAnsweredTime,
            });
        } catch (err) {
            next(err);
        }
    }

    handleGetReviewsWithAnalysis = async (req: Request, res: Response, next: NextFunction) => {
        try {
            const restaurantId = req.params.restaurant_id;
            const { start_date: startDate, end_date: endDate, platforms } = req.query as any;

            const filters = new ReviewFilters({
                startDate,
                endDate,
                restaurantId,
                platforms,
            });
            if (filters.periodCantBeSet()) {
                throw new MalouError(MalouErrorCode.FILTER_MISSING_PARAMS, { message: 'Missing data, need start_date & end_date' });
            }

            const searchQuery = filters.buildQuery({ filterType: FilterType.REVIEWS });

            const isSemanticAnalysisFeatureEnabled = await isFeatureAvailableForRestaurant({
                featureName: 'release-new-semantic-analysis',
                restaurantId: restaurantId,
            });

            const reviews = await this._reviewsUseCases.getReviewsWithAnalysis(searchQuery, isSemanticAnalysisFeatureEnabled);

            return res.json({
                data: reviews,
            });
        } catch (err) {
            next(err);
        }
    };

    @Params(getChartDataParamsValidator)
    @Query(getRestaurantsReviewsRatingQueryValidator)
    async handleGetReviewsRating(req: Request<any, any, any, GetRestaurantsReviewsRatingQueryDto>, res: Response, next: NextFunction) {
        try {
            const { restaurantId } = req.params;
            const { comparisonPeriod, endDate, platforms, startDate } = req.query;

            const filters = await this._getFiltersForChartDataUtil.getReviewFilters({
                endDate,
                platforms,
                restaurantId,
                startDate,
                comparisonPeriod,
            });

            if (!filters.validDates()) {
                throw new MalouError(MalouErrorCode.FILTER_MISSING_PARAMS, { message: 'need to specify two dates to have time range' });
            }
            const result = await this._reviewsUseCases.getRestaurantReviewsRating(filters); // ratings are rounded to the nearest integer
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(getChartDataParamsValidator)
    @Query(getRestaurantsReviewsAverageRatingQueryValidator)
    async handleGetRestaurantsReviewsRatingAverage(
        req: Request<GetChartDataParamsDto, never, never, GetRestaurantsReviewsAverageRatingQueryDto>,
        res: Response<ApiResultV2<GetRestaurantsReviewsAverageRatingDto, ApiResultError>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { comparisonPeriod, endDate, platforms, startDate } = req.query;

            const filters = await this._getFiltersForChartDataUtil.getReviewFilters({
                startDate,
                endDate,
                restaurantId,
                comparisonPeriod,
                platforms,
            });

            if (!filters.validDates()) {
                throw new MalouError(MalouErrorCode.FILTER_MISSING_PARAMS, { message: 'need to specify two dates to have time range' });
            }
            const result = await this._reviewsUseCases.getRestaurantsReviewsAverageRating(filters);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(getChartDataParamsValidator)
    @Query(getRestaurantsReviewsEvolutionQueryValidator)
    async handleGetReviewsEvolution(
        req: Request<any, any, any, GetRestaurantsReviewsEvolutionRateQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { comparisonPeriod, endDate, platforms, startDate } = req.query;
            const filters = await this._getFiltersForChartDataUtil.getReviewFilters({
                startDate,
                endDate,
                restaurantId,
                comparisonPeriod,
                platforms,
            });

            if (!filters.validDates()) {
                throw new MalouError(MalouErrorCode.FILTER_MISSING_PARAMS, { message: 'need to specify two dates to have time range' });
            }
            const result = await this._reviewsUseCases.getRestaurantReviewsEvolution(filters);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(getChartDataParamsValidator)
    @Query(getChartRestaurantsReviewsTotalQueryValidator)
    async handleGetReviewsEvolutionTotal(
        req: Request<GetChartDataParamsDto, any, any, GetChartRestaurantsReviewsTotalQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const restaurantId = req.params.restaurantId;
            const { comparisonPeriod, endDate, platforms, startDate } = req.query;

            const filters = await this._getFiltersForChartDataUtil.getReviewFilters({
                startDate,
                endDate,
                restaurantId,
                comparisonPeriod,
                platforms,
            });

            if (!filters.validDates()) {
                throw new MalouError(MalouErrorCode.FILTER_MISSING_PARAMS, { message: 'need to specify two dates to have time range' });
            }
            const result = await this._reviewsUseCases.getRestaurantsReviews(filters);
            return res.json({
                data: result,
            });
        } catch (err) {
            next(err);
        }
    }

    @Body(getReviewsEvolutionTotalBodyValidator)
    async handleGetReviewsEvolutionTotalInDateRange(
        req: Request<any, any, GetReviewsEvolutionTotalBodyDto>,
        res: Response<ApiResultV2<GetReviewsEvolutionTotalResponseBodyDto>>,
        next: NextFunction
    ) {
        try {
            const userRestaurants = await this._usersRepository.getUserWithActiveRestaurants(req.user._id);
            if (!userRestaurants?.restaurants?.length) {
                res.json({ data: [] });
                return;
            }
            const allRestaurantIds = userRestaurants.restaurants?.map((r) => r.restaurantId.toString()) ?? [];
            const restaurantIds = intersectionBy(allRestaurantIds, req.body.restaurantIds);
            if (!restaurantIds.length) {
                res.json({ data: [] });
                return;
            }
            const { startDate, endDate, comparisonPeriod, platformKeys } = req.body;
            const filters = new ReviewFilters({
                startDate,
                endDate,
                ...(comparisonPeriod && {
                    comparisonPeriodData: {
                        comparisonPeriod,
                    },
                }),
                restaurantId: restaurantIds,
                platforms: platformKeys,
            });
            if (!filters.validDates()) {
                throw new MalouError(MalouErrorCode.FILTER_MISSING_PARAMS, { message: 'need to specify two dates to have time range' });
            }
            const result = await this._reviewsUseCases.getRestaurantsReviewsGroupedByRestaurantIds(filters);
            return res.json({
                data: result,
            });
        } catch (err) {
            logger.error('[ERROR_AGGREGATED_STATS] [REVIEWS]', {
                error: JSON.stringify(err, errorReplacer),
                body: JSON.stringify(req.body),
            });
            next(err);
        }
    }

    @Query(getRestaurantsReviewsV2QueryValidator)
    async handleGetRestaurantsReviewsV2(
        req: Request<any, any, any, GetRestaurantsReviewsV2QueryDto>,
        res: Response<ApiResultV2<GetRestaurantsReviewsV2ResponseDto>>,
        next: NextFunction
    ) {
        try {
            const {
                answerable,
                answered,
                archived,
                end_date: endDate,
                not_answered: notAnswered,
                page_number: pageNumber,
                page_size: pageSize,
                pending,
                platforms,
                private_platforms: privatePlatforms,
                ratings,
                restaurant_ids: restaurantIds,
                show_private: showPrivate,
                skip,
                start_date: startDate,
                text: searchText,
                unarchived,
                with_text: withText,
                without_text: withoutText,
            } = req.query;

            const pagination = new ReviewPagination({ pageNumber, pageSize, skip });
            const filters: ReviewFiltersInput = {
                searchText,
                ratings,
                showPrivate,
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                pending,
                archived,
                unarchived,
                withText,
                withoutText,
                restaurantIds,
                answerable,
                privatePlatforms,
            };

            const semanticAnalysisFeatureEnabledForRestaurants = await isFeatureAvailableForRestaurants({
                restaurantIds,
                featureName: 'release-new-semantic-analysis',
            });
            const isSemanticAnalysisFeatureEnabled = semanticAnalysisFeatureEnabledForRestaurants?.length === restaurantIds.length;

            const reviews = await this._getReviewsPaginatedUseCase.execute(pagination, filters, isSemanticAnalysisFeatureEnabled);

            return res.json({
                data: this._reviewsMapper.toGetRestaurantsReviewsV2ResponseDto(reviews, pagination),
            });
        } catch (error) {
            return next(error);
        }
    }

    @Body(getReviewCountBodyValidator)
    async handleGetReviewCount(
        req: Request<any, any, GetReviewCountBodyDto>,
        res: Response<ApiResultV2<GetReviewCountResponseDto>>,
        next: NextFunction
    ) {
        try {
            const filters = req.body;
            const count = await this._reviewsUseCases.getEstimatedReviewsCount(filters);

            return res.json({ data: { count } });
        } catch (error) {
            return next(error);
        }
    }
    @Params(updateReviewArchivedValueParamsValidator)
    @Body(updateReviewArchivedValueBodyValidator)
    async handleUpdateReviewArchivedValue(
        req: RequestWithPermissions<UpdateReviewArchivedValueParamsDto, any, UpdateReviewArchivedValueBodyDto>,
        res: Response<ApiResult<ReviewResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { review_id: reviewId } = req.params;

            const { archived, isPrivate } = req.body;
            const review = isPrivate
                ? await this._privateReviewsRepository.getPrivateReviewById(reviewId)
                : await this._reviewsRepository.getReviewById(reviewId);

            assert(review, 'Review not found');
            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                'update',
                subject(CaslSubject.REVIEW, { restaurantId: review.restaurantId })
            );

            const updatedReview = await this._reviewsUseCases.updateReviewArchivedValue(reviewId, archived, review);
            const updatedReviewDto = this._reviewsMapper.toReviewResponseDto(updatedReview);
            return res.json({ msg: 'Review updated.', data: updatedReviewDto });
        } catch (err) {
            next(err);
        }
    }

    @Params(fetchAiRelevantBricksParamsValidator)
    async handleFetchAiRelevantBricks(
        req: RequestWithPermissions<FetchAiRelevantBricksParamsDto>,
        res: Response<ApiResult<ReviewWithTranslationsResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { review_id: reviewId } = req.params;
            assert(req.user, 'User is not defined');
            const { _id: userId } = req.user;
            const updatedReview = await this._updateReviewRelevantBricksUseCase.execute({ reviewId, userId: userId.toString() });
            return res.json({ msg: 'Review updated.', data: updatedReview });
        } catch (err) {
            next(err);
        }
    }

    async handleCanAnswer(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { restaurant_id: restaurantId } = req.params;
            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.REVIEW, { restaurantId })
            );
            return res.json({ msg: 'Yes he can.' });
        } catch (err) {
            next(err);
        }
    }

    @Query(getRestaurantsUnansweredCountQueryValidator)
    async handleGetRestaurantsUnansweredCount(
        req: Request<any, any, any, GetRestaurantsUnansweredCountQueryDto>,
        res: Response<ApiResult<GetRestaurantsUnansweredCountResponseDto>>,
        next: NextFunction
    ) {
        try {
            const {
                text: searchText,
                ratings,
                end_date: endDate,
                start_date: startDate,
                platforms,
                answered,
                not_answered: notAnswered,
                pending,
                archived,
                unarchived,
                with_text: withText,
                without_text: withoutText,
                show_private: showPrivate,
                restaurant_ids: restaurantIds,
            } = req.query;

            const filters: ReviewFiltersInput = {
                searchText,
                ratings,
                showPrivate,
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                pending,
                archived,
                unarchived,
                withText,
                withoutText,
                restaurantIds,
                answerable: true,
            };
            const count = await this._reviewsUseCases.getEstimatedReviewsCount(filters);
            return res.json({ data: { count } });
        } catch (err) {
            next(err);
        }
    }

    @Body(getRestaurantsUnansweredCountBodyValidator)
    async handleGetRestaurantsUnansweredCountV2(
        req: Request<any, any, GetRestaurantsUnansweredCountBodyDto>,
        res: Response<ApiResult<GetRestaurantsUnansweredCountResponseDto>>,
        next: NextFunction
    ) {
        try {
            const filters = req.body;
            const count = await this._reviewsUseCases.getEstimatedReviewsCount(filters);
            return res.json({ data: { count } });
        } catch (err) {
            next(err);
        }
    }

    @Query(getReviewPageQueryValidator)
    @Params(getReviewPageParamsValidator)
    async handleGetReviewPage(
        req: Request<GetReviewPageParamsDto, any, any, GetReviewPageQueryDto>,
        res: Response<ApiResult<GetReviewPageResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { review_id: reviewId } = req.params;
            const {
                text,
                ratings,
                end_date: endDate,
                start_date: startDate,
                platforms,
                answered,
                not_answered: notAnswered,
                pending,
                archived,
                unarchived,
                with_text: withText,
                without_text: withoutText,
                show_private: showPrivate,
                answerable,
                restaurant_ids: restaurantIds,
            } = req.query;

            const filters: ReviewFiltersInput = {
                text,
                ratings,
                showPrivate,
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                pending,
                archived,
                unarchived,
                withText,
                withoutText,
                restaurantIds,
                answerable,
            };
            const page = await this._reviewsRepository.getReviewPage(reviewId, filters);
            res.json({ data: page });
        } catch (err) {
            next(err);
        }
    }

    @Body(getReviewPageBodyValidator)
    @Params(getReviewPageParamsValidator)
    async handleGetReviewPageV2(
        req: Request<GetReviewPageParamsDto, any, GetReviewPageBodyDto>,
        res: Response<ApiResult<GetReviewPageResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { review_id: reviewId } = req.params;
            const filters: ReviewFiltersInput = req.body;
            const page = await this._reviewsRepository.getReviewPage(reviewId, filters);
            res.json({ data: page });
        } catch (err) {
            next(err);
        }
    }

    async handlePublishComment(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { user } = req;
            assert(user, 'User is not defined');
            const userWithProfilePic = await this._usersRepository.findOne({
                filter: { _id: user._id },
                options: {
                    populate: [
                        {
                            path: 'profilePicture',
                        },
                    ],
                    lean: true,
                },
            });
            const userAgent = req.headers['user-agent'];
            const { review_id: reviewId, restaurant_id: restaurantId } = req.params;
            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.REVIEW, { restaurantId })
            );
            const { comment } = req.body;
            const review = await this._replyToReviewUseCase.execute({
                user: userWithProfilePic as any,
                reviewId,
                comment,
                restaurantId,
                headerConfig: { userAgent },
                isFromJob: false,
            });
            return res.json({ msg: 'Comment published.', data: review });
        } catch (err) {
            next(err);
        }
    }

    async handleUpdateComment(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { user } = req;
            assert(user, 'User is not defined');
            const userWithProfilePic = await this._usersRepository.findOne({
                filter: { _id: user._id },
                options: {
                    populate: [
                        {
                            path: 'profilePicture',
                        },
                    ],
                    lean: true,
                },
            });
            const { comment } = req.body;
            const { review_id: reviewId, comment_id: commentId, restaurant_id: restaurantId } = req.params;
            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.REVIEW, { restaurantId })
            );
            const review = await this._reviewsUseCases.updateComment({
                user: userWithProfilePic as any,
                reviewId,
                commentId,
                comment,
            });
            return res.json({ msg: 'Comment updated.', data: review });
        } catch (err) {
            return next(err);
        }
    }

    @Query(synchronizeReviewsQueryValidator)
    async handleSynchronizeReviews(
        req: Request<any, any, any, SynchronizeReviewsQueryDto>,
        res: Response<ApiResult<SynchronizeReviewsResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantIds } = req.query;
            const promises = restaurantIds.map((restaurantId) =>
                this._reviewsUseCases.startUpdateReviewsForRestaurant(restaurantId).catch((error) => console.log(error))
            );
            await Promise.all(promises);
            return res.status(201).json({ msg: 'Started synchronization' });
        } catch (err) {
            next(err);
        }
    }

    @Params(restaurantIdParamsValidator)
    async handleSetReviewsLastUpdate(req: Request, res: Response, next: NextFunction) {
        const { restaurant_id: restaurantId } = req.params;
        return this._restaurantsRepository
            .findOneAndUpdate({ filter: { _id: restaurantId }, update: { reviewsLastUpdate: new Date() } })
            .then(() => res.status(200).json({ msg: 'synchronize date updated.' }))
            .catch((e) => next(e));
    }

    @Params(DeleteReviewsForPlatformParamsValidator)
    async handleDeleteReviewsForPlatform(req: Request<DeleteReviewsForPlatformParamsDto>, res: Response, next: NextFunction) {
        const { restaurantId, platformKey } = req.params;
        return this._platformsUseCases
            .getPlatformsForRestaurantId(restaurantId)
            .then((platforms) => {
                const platform = platforms.find((p) => p.key === platformKey);
                if (!platform) {
                    throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                        message: 'Platform not found',
                        metadata: { platformKey, restaurantId },
                    });
                }
                return this._reviewsRepository
                    .deleteMany({ filter: { platformId: platform._id } })
                    .then(() => res.json({ msg: 'Reviews deleted.' }))
                    .catch((e) => next(e));
            })
            .catch((e) => next(e));
    }

    @Params(deleteReviewParamsValidator)
    async handleDeleteReview(req: Request<DeleteReviewParamsDto>, res: Response, next: NextFunction) {
        const { reviewId } = req.params;
        return this._reviewsRepository
            .deleteReviewById(reviewId)
            .then(() => res.json({ msg: 'Review deleted.' }))
            .catch((e) => next(e));
    }

    @Params(getReviewByIdParamsValidator)
    async handleGetReviewById(
        req: Request<GetReviewByIdParamsDto>,
        res: Response<ApiResult<ReviewWithTranslationsResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { review_id: reviewId } = req.params;
            const review = await this._getReviewByIdUseCase.execute(reviewId);
            return res.json({ data: review });
        } catch (err) {
            next(err);
        }
    }

    @Body(getReviewsBySocialIdsBodyValidator)
    async handleGetReviewBySocialIds(req: Request<never, never, GetReviewsBySocialIdsBodyDto>, res: Response, next: NextFunction) {
        try {
            const { socialIds, restaurantIds } = req.body;
            const review = await this._getReviewsBySocialIdUseCase.execute({ socialIds, restaurantIds });
            return res.json({ data: review });
        } catch (err) {
            next(err);
        }
    }

    @Body(getReviewsBySegmentTopicBodyValidator)
    async handleGetReviewsByTopic(req: Request<never, never, GetReviewsBySegmentTopicBodyDto>, res: Response, next: NextFunction) {
        try {
            const { topic, startDate, endDate, platformKeys, restaurantIds, shouldShowSubcategories, isMainCategory } = req.body;
            assert(startDate, 'Start date is required');
            assert(endDate, 'End date is required');
            assert(platformKeys?.length, 'Platform keys are required');
            const reviews = await this._getReviewsBySegmentTopicUseCase.execute({
                topic,
                startDate,
                endDate,
                platformKeys,
                restaurantIds,
                shouldShowSubcategories,
                isMainCategory,
            });
            return res.json({ data: reviews });
        } catch (err) {
            next(err);
        }
    }

    @Params(getReviewsWithSegmentAnalysesParamsValidator)
    @Query(getReviewsWithSegmentAnalysesQueryValidator)
    async handleGetReviewsWithSegmentAnalyses(
        req: Request<GetReviewsWithSegmentAnalysesParamsDto, any, any, GetReviewsWithSegmentAnalysesQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { restaurantId } = req.params;
            const { startDate, endDate, platformKeys } = req.query;
            const reviews = await this._getReviewsWithSegmentAnalysesUseCase.execute({
                restaurantId,
                startDate,
                endDate,
                platformKeys,
            });
            return res.json({ data: reviews });
        } catch (err) {
            next(err);
        }
    }

    @Params(getReviewerNameValidationParamsValidator)
    @Query(getReviewerNameValidationQueryValidator)
    async handleGetReviewerNameValidation(
        req: Request<GetReviewerNameValidationParamsDto, any, any, GetReviewerNameValidationQueryDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { reviewId } = req.params;
            const { lang } = req.query;
            const reviewerNameValidation = await this._getReviewerNameValidationUseCase.execute(reviewId, lang);
            return res.json({ data: { reviewerNameValidation } });
        } catch (err) {
            next(err);
        }
    }

    async handlePublishPrivateReviewComment(req: RequestWithPermissions, res: Response, next: NextFunction) {
        try {
            const { review_id: reviewId, restaurant_id: restaurantId } = req.params;
            assert(req.userRestaurantsAbility, 'User restaurants ability is not defined');
            ForbiddenError.from(req.userRestaurantsAbility).throwUnlessCan(
                CaslAction.UPDATE,
                subject(CaslSubject.REVIEW, { restaurantId })
            );
            const { comment } = req.body;
            const review = await this._reviewsUseCases.replyPrivate({ reviewId, comment });
            return res.json({ msg: 'Comment published.', data: review });
        } catch (err) {
            next(err);
        }
    }

    @Body(createPrivateReviewBodyValidator)
    async handlePublishPrivateReview(req: Request<never, never, CreatePrivateReviewDto>, res: Response, next: NextFunction) {
        try {
            const { privateReview: privateReviewDto } = req.body;
            const review = await this._reviewsUseCases.createPrivateReview(this._reviewsMapper.toCreatePrivateReview(privateReviewDto));
            if (privateReviewDto.campaignId && privateReviewDto.clientId) {
                const setValues = {
                    'contactInteractions.$.lastStarRatingDate': new Date(),
                    'contactInteractions.$.lastStarRating': review.rating,
                };
                await this._campaignsRepository.findOneAndUpdate({
                    filter: { _id: privateReviewDto.campaignId, 'contactInteractions.clientId': privateReviewDto.clientId },
                    update: { ...setValues },
                });
            }
            return res.json({ msg: 'Review Created', data: { review } });
        } catch (err) {
            next(err);
        }
    }

    @Params(linkEmailToPrivateReviewParamsValidator)
    @Body(linkEmailToPrivateReviewBodyValidator)
    async handleLinkClientIdOrEmailToPrivateReview(
        req: Request<LinkEmailToPrivateReviewParamsDto, any, LinkEmailToPrivateReviewBodyDto>,
        res: Response,
        next: NextFunction
    ) {
        try {
            const { privateReviewId } = req.params;
            const { email, clientId } = req.body;
            const updatedPrivateReview = await this._linkClientToPrivateReviewUseCase.execute({
                privateReviewId,
                email: email || undefined,
                clientId: clientId || undefined,
            });
            return res.json({ msg: 'Email linked to private review successfully.', data: updatedPrivateReview });
        } catch (error) {
            return next(error);
        }
    }

    @Params(getReviewTotalCountParamsValidator)
    async handleGetReviewTotalCountForPlatform(
        req: Request<GetReviewTotalCountParamsDto, any, any, any>,
        res: Response<ApiResultV2<GetTotalReviewCountResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { restaurantId, platformKey } = req.params;
            const count = await this._reviewsUseCases.getPlatformTotalReviewCount(restaurantId, platformKey);
            return res.json({ data: { count } });
        } catch (err) {
            next(err);
        }
    }

    @Params(updateKeywordsLangParamsValidator)
    @Body(updateKeywordsLangBodyValidator)
    async handleUpdateKeywordsLang(
        req: Request<UpdateKeywordsLangParamsDto, any, UpdateKeywordsLangBodyDto>,
        res: Response<ApiResultV2<ReviewWithTranslationsResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { review_id: reviewId } = req.params;
            const { keywordsLang } = req.body;

            const review = await this._reviewsRepository.updateReviewKeywordsLang(reviewId, keywordsLang);
            const reviewDto = this._reviewsMapper.toReviewWithTranslationsResponseDto(review);
            return res.json({ data: reviewDto });
        } catch (error) {
            next(error);
        }
    }

    @Query(getRestaurantsReviewsQueryValidator)
    async handleGetFilteredReviewsAsPdf(
        req: RequestWithUser<never, never, never, GetRestaurantsReviewsOutputQueryDto>,
        res: Response<ApiResultV2<DownloadReviewsAsPdfResponseDto>>,
        next: NextFunction
    ) {
        try {
            assert(req.user, 'User is not defined');

            // Prepare the query
            const {
                text: searchText,
                ratings,
                end_date: endDate,
                start_date: startDate,
                platforms,
                answered,
                not_answered: notAnswered,
                pending,
                archived,
                unarchived,
                with_text: withText,
                without_text: withoutText,
                show_private: showPrivate,
                answerable,
                restaurant_ids: restaurantIds,
                private_platforms: privatePlatforms,
                timeZone,
            } = req.query;

            assert(timeZone, 'Time zone is required');

            // Prepare Pagination and Filters
            const pagination = new ReviewPagination({ pageSize: 1000, total: 0 });
            const reviewFilters: ReviewFiltersInput = {
                searchText,
                ratings,
                showPrivate,
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                pending,
                archived,
                unarchived,
                withText,
                withoutText,
                restaurantIds,
                answerable,
                privatePlatforms,
            };

            const semanticAnalysisFeatureEnabledForRestaurants = await isFeatureAvailableForRestaurants({
                restaurantIds,
                featureName: 'release-new-semantic-analysis',
            });
            const isSemanticAnalysisFeatureEnabled = semanticAnalysisFeatureEnabledForRestaurants?.length === restaurantIds.length;
            const pdfUrl = await this._downloadReviewsAsPdfService.execute(
                reviewFilters,
                pagination,
                req.user,
                timeZone,
                isSemanticAnalysisFeatureEnabled
            );
            return res.status(200).json({
                data: pdfUrl,
            });
        } catch (err) {
            next(err);
        }
    }

    @Query(getFilteredReviewsAsPdfQueryValidator)
    @Body(getFilteredReviewsAsPdfBodyValidator)
    async handleGetFilteredReviewsAsPdfV2(
        req: RequestWithUser<never, never, GetFilteredReviewsAsPdfBodyDto, GetFilteredReviewsAsPdfQueryDto>,
        res: Response<ApiResultV2<DownloadReviewsAsPdfResponseDto>>,
        next: NextFunction
    ) {
        try {
            // Prepare the query
            assert(req.user, 'User is not defined');
            const { timeZone } = req.query;
            assert(timeZone, 'Time zone is required');
            const {
                searchText,
                ratings,
                endDate,
                startDate,
                platforms,
                answered,
                notAnswered,
                pending,
                archived,
                unarchived,
                withText,
                withoutText,
                showPrivate,
                answerable,
                restaurantIds,
                privatePlatforms,
            } = req.body;

            // Prepare Pagination and sort
            const pagination = new ReviewPagination({ pageSize: 1000, total: 0 });
            const reviewFilters: ReviewFiltersInput = {
                searchText,
                ratings,
                showPrivate,
                startDate,
                endDate,
                platforms,
                answered,
                notAnswered,
                pending,
                archived,
                unarchived,
                withText,
                withoutText,
                restaurantIds,
                answerable,
                privatePlatforms,
            };

            const semanticAnalysisFeatureEnabledForRestaurants = await isFeatureAvailableForRestaurants({
                restaurantIds,
                featureName: 'release-new-semantic-analysis',
            });
            const isSemanticAnalysisFeatureEnabled = semanticAnalysisFeatureEnabledForRestaurants?.length === restaurantIds.length;

            const pdfUrl = await this._downloadReviewsAsPdfService.execute(
                reviewFilters,
                pagination,
                req.user,
                timeZone,
                isSemanticAnalysisFeatureEnabled
            );

            return res.status(200).json({
                data: pdfUrl,
            });
        } catch (err) {
            next(err);
        }
    }

    @Params(addTranslationToReviewParamsValidator)
    @Body(addTranslationToReviewBodyValidator)
    async handleAddTranslationToReview(
        req: Request<AddTranslationToReviewParamsDto, any, AddTranslationToReviewBodyDto>,
        res: Response<ApiResultV2<ReviewWithTranslationsResponseDto>>,
        next: NextFunction
    ) {
        try {
            const { reviewId } = req.params;
            const { translation, language, source, isPrivateReview } = req.body;
            const review = await this._addTranslationToReviewUseCase.execute(reviewId, translation, language, source, isPrivateReview);
            return res.json({ data: review });
        } catch (error) {
            next(error);
        }
    }
}
