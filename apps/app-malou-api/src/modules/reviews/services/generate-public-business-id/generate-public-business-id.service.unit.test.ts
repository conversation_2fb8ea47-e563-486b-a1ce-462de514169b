import { container } from 'tsyringe';

import { DbId, newDbId } from '@malou-io/package-models';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultOrganization } from ':modules/organizations/organization.builder';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';
import { GeneratePublicBusinessIdService } from ':modules/reviews/services/generate-public-business-id/generate-public-business-id.service';
import { getDefaultReview } from ':modules/reviews/tests/reviews.builder';

let generatePublicBusinessIdService: GeneratePublicBusinessIdService;

describe('GeneratePublicBusinessIdService', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['ReviewsRepository', 'RestaurantsRepository', 'OrganizationsRepository']);

        generatePublicBusinessIdService = container.resolve(GeneratePublicBusinessIdService);
    });

    describe('generatePublicBusinessId', () => {
        it('should do nothing when review already has publicBusinessId', async () => {
            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().reviewPublicBusinessIdCount(5).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant().organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .publicBusinessId('existing-public-business-id')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await expect(generatePublicBusinessIdService.generatePublicBusinessId(reviewId)).resolves.toBeUndefined();
        });

        it('should generate publicBusinessId for review without one', async () => {
            const organizationName = 'Test Restaurant Organization';
            const initialCount = 10;

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name(organizationName).reviewPublicBusinessIdCount(initialCount).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant().organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).publicBusinessId(undefined).build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await generatePublicBusinessIdService.generatePublicBusinessId(reviewId);

            // Verify that the review now has publicBusinessId
            const reviewsRepository = container.resolve(ReviewsRepository);
            const updatedReview = await reviewsRepository.findOne({
                filter: { _id: seededObjects.reviews[0]._id },
                projection: { publicBusinessId: 1 },
                options: { lean: true },
            });

            expect(updatedReview?.publicBusinessId).toBeDefined();
            expect(updatedReview?.publicBusinessId).not.toBeUndefined();

            // Verify that the organization's counter was incremented
            const organizationsRepository = container.resolve(OrganizationsRepository);
            const updatedOrganization = await organizationsRepository.findOne({
                filter: { _id: seededObjects.organizations[0]._id },
                projection: { reviewPublicBusinessIdCount: 1 },
                options: { lean: true },
            });

            expect(updatedOrganization?.reviewPublicBusinessIdCount).toBe(initialCount + 1);
        });

        it('should skip review when restaurant is not found', async () => {
            const nonExistentRestaurantId = newDbId();

            const testCase = new TestCaseBuilderV2<'reviews'>({
                seeds: {
                    reviews: {
                        data() {
                            return [getDefaultReview().restaurantId(nonExistentRestaurantId).publicBusinessId(undefined).build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await expect(generatePublicBusinessIdService.generatePublicBusinessId(reviewId)).resolves.toBeUndefined();

            // Verify that the review still has null publicBusinessId
            const reviewsRepository = container.resolve(ReviewsRepository);
            const updatedReview = await reviewsRepository.findOne({
                filter: { _id: seededObjects.reviews[0]._id },
                projection: { publicBusinessId: 1 },
                options: { lean: true },
            });

            expect(updatedReview?.publicBusinessId).toBeUndefined();
        });

        it('should skip review when organization is not found', async () => {
            const nonExistentOrganizationId = newDbId();

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().organizationId(nonExistentOrganizationId).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [getDefaultReview().restaurantId(dependencies.restaurants()[0]._id).publicBusinessId(undefined).build()];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await expect(generatePublicBusinessIdService.generatePublicBusinessId(reviewId)).resolves.toBeUndefined();

            // Verify that the review still has null publicBusinessId
            const reviewsRepository = container.resolve(ReviewsRepository);
            const updatedReview = await reviewsRepository.findOne({
                filter: { _id: seededObjects.reviews[0]._id },
                projection: { publicBusinessId: 1 },
                options: { lean: true },
            });

            expect(updatedReview?.publicBusinessId).toBeUndefined();
        });

        it('should handle review that already has publicBusinessId', async () => {
            const organizationName = 'Mixed Test Organization';
            const initialCount = 5;

            const testCase = new TestCaseBuilderV2<'reviews' | 'restaurants' | 'organizations'>({
                seeds: {
                    organizations: {
                        data() {
                            return [getDefaultOrganization().name(organizationName).reviewPublicBusinessIdCount(initialCount).build()];
                        },
                    },
                    restaurants: {
                        data(dependencies) {
                            return [getDefaultRestaurant().organizationId(dependencies.organizations()[0]._id).build()];
                        },
                    },
                    reviews: {
                        data(dependencies) {
                            return [
                                getDefaultReview()
                                    .restaurantId(dependencies.restaurants()[0]._id)
                                    .publicBusinessId('existing-id-1')
                                    .build(),
                            ];
                        },
                    },
                },
            });

            await testCase.build();
            const seededObjects = testCase.getSeededObjects();
            const reviewId = (seededObjects.reviews[0]._id as DbId).toString();

            await generatePublicBusinessIdService.generatePublicBusinessId(reviewId);

            // Verify that the review still has the same publicBusinessId
            const reviewsRepository = container.resolve(ReviewsRepository);
            const updatedReview = await reviewsRepository.findOne({
                filter: { _id: seededObjects.reviews[0]._id },
                projection: { publicBusinessId: 1 },
                options: { lean: true },
            });

            expect(updatedReview?.publicBusinessId).toBe('existing-id-1');

            // Verify that the organization's counter was not incremented
            const organizationsRepository = container.resolve(OrganizationsRepository);
            const updatedOrganization = await organizationsRepository.findOne({
                filter: { _id: seededObjects.organizations[0]._id },
                projection: { reviewPublicBusinessIdCount: 1 },
                options: { lean: true },
            });

            expect(updatedOrganization?.reviewPublicBusinessIdCount).toBe(initialCount);
        });

        it('should handle non-existent review ID gracefully', async () => {
            const nonExistentReviewId = newDbId().toString();

            await expect(generatePublicBusinessIdService.generatePublicBusinessId(nonExistentReviewId)).resolves.toBeUndefined();
        });
    });
});
