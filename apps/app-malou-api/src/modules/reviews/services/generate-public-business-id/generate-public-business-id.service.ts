import { singleton } from 'tsyringe';

import { DbId, toDbId } from '@malou-io/package-models';
import { REVIEW_PUBLIC_BUSINESS_ID_SEPARATOR } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { GeneratePublicBusinessIdProducer } from ':modules/reviews/queues/generate-public-business-id/generate-public-business-id.producer';
import { ReviewsRepository } from ':modules/reviews/reviews.repository';

@singleton()
export class GeneratePublicBusinessIdService {
    constructor(
        private readonly _organizationsRepository: OrganizationsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _reviewsRepository: ReviewsRepository,
        private readonly _generatePublicBusinessIdProducer: GeneratePublicBusinessIdProducer
    ) {}

    async startGeneratePublicBusinessId(reviewIds: string[]): Promise<void> {
        for (const reviewId of reviewIds) {
            await this._generatePublicBusinessIdProducer.execute({ reviewId });
        }
    }

    async generatePublicBusinessId(reviewId: string): Promise<void> {
        logger.info('[GeneratePublicBusinessIdService] Generating public business id for review', { reviewId });

        const reviewWithoutPublicBusinessId = await this._reviewsRepository.findOne({
            filter: {
                _id: toDbId(reviewId),
                publicBusinessId: null,
            },
            projection: { _id: 1, restaurantId: 1 },
            options: { lean: true },
        });

        if (!reviewWithoutPublicBusinessId) {
            logger.info('[GeneratePublicBusinessIdService] Review not found or already has public business id', { reviewId });
            return;
        }

        logger.info('[GeneratePublicBusinessIdService] Found reviews without public business id', {
            review: reviewWithoutPublicBusinessId,
        });

        const publicBusinessId = await this._generatePublicBusinessId(reviewWithoutPublicBusinessId.restaurantId);
        if (!publicBusinessId) {
            return;
        }

        await this._reviewsRepository.updateOne({
            filter: { _id: reviewWithoutPublicBusinessId._id },
            update: { $set: { publicBusinessId } },
        });

        logger.info('[GeneratePublicBusinessIdService] Finished generating public business id for review', { reviewId });
    }

    createIncrementedPublicBusinessId(currentReviewPublicBusinessIdCount: number, organizationId: string): string {
        const newCount = currentReviewPublicBusinessIdCount + 1;

        const newCountAsHexa = newCount.toString(16).toUpperCase();
        const newPrettyCountAsHexa = String(newCountAsHexa).padStart(6, '0');
        return `${organizationId}${REVIEW_PUBLIC_BUSINESS_ID_SEPARATOR}${newPrettyCountAsHexa}`;
    }

    private async _generatePublicBusinessId(restaurantId: DbId): Promise<string | undefined> {
        const restaurant = await this._restaurantsRepository.findOne({
            filter: { _id: restaurantId },
            options: { lean: true, projection: { organizationId: 1 } },
        });
        if (!restaurant?.organizationId) {
            return undefined;
        }

        const organization = await this._organizationsRepository.findOneAndUpdate({
            filter: { _id: restaurant.organizationId },
            update: { $inc: { reviewPublicBusinessIdCount: 1 } },
            options: { lean: true, new: true, projection: { reviewPublicBusinessIdCount: 1 } },
        });
        if (!organization) {
            return undefined;
        }

        return this.createIncrementedPublicBusinessId(organization.reviewPublicBusinessIdCount, organization._id.toString());
    }
}
