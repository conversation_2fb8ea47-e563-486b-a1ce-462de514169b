import { singleton } from 'tsyringe';

import { Config } from ':config';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsProducer } from ':queues/sqs-template/generic-sqs-producer';

export type GeneratePublicBusinessIdProducerPayload = {
    reviewId: string;
};

@singleton()
export class GeneratePublicBusinessIdProducer extends GenericSqsProducer<GeneratePublicBusinessIdProducerPayload> {
    constructor() {
        super({
            useCaseQueueTag: UseCaseQueueTag.GENERATE_PUBLIC_BUSINESS_ID,
            queueUrl: Config.services.sqs.generatePublicBusinessIdFifoQueueUrl,
        });
    }
    async execute(payload: GeneratePublicBusinessIdProducerPayload): Promise<void> {
        await this.sendMessage(payload, { groupId: payload.reviewId });
    }
}
