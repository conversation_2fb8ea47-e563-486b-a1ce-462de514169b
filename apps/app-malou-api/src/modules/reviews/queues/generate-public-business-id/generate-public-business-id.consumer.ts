import { isNil } from 'lodash';
import { SQSMessage } from 'sqs-consumer';
import { singleton } from 'tsyringe';
import { z } from 'zod';

import { objectIdValidator } from '@malou-io/package-dto';
import { MalouErrorCode } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { GeneratePublicBusinessIdService } from ':modules/reviews/services/generate-public-business-id/generate-public-business-id.service';
import { UseCaseQueueTag } from ':queues/sqs-template/constant';
import { GenericSqsConsumer } from ':queues/sqs-template/generic-sqs-consumer';

const generatePublicBusinessIdMessageValidator = z.object({
    reviewId: objectIdValidator,
});

@singleton()
export class GeneratePublicBusinessIdConsumer extends GenericSqsConsumer {
    constructor(private readonly _generatePublicBusinessIdService: GeneratePublicBusinessIdService) {
        super({
            useCaseQueueTag: UseCaseQueueTag.GENERATE_PUBLIC_BUSINESS_ID,
            queueUrl: Config.services.sqs.generatePublicBusinessIdFifoQueueUrl,
        });
    }

    async handleMessage(msg: SQSMessage): Promise<void> {
        const body = msg.Body ? JSON.parse(msg.Body) : null;
        if (isNil(body)) {
            throw new MalouError(MalouErrorCode.SQS_MESSAGE_NOT_FOUND, { message: 'body is nil' });
        }

        const { reviewId } = generatePublicBusinessIdMessageValidator.parse(body);

        await this._generatePublicBusinessIdService.generatePublicBusinessId(reviewId);
    }
}
