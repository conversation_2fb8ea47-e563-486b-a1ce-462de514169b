import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IMedia, IPost, toDbId } from '@malou-io/package-models';
import { MediaType, PostPublicationStatus, PublicationType, SocialAttachmentsMediaTypes } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { MediasRepository } from ':modules/media/medias.repository';
import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    GetIgMediaResponse,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { FormatMediaService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/format-medias.service';
import { HandleMetaPublicationErrorService } from ':modules/posts/v2/use-cases/publish-post-on-platform/services/handle-meta-publication-error.service';
import { PublishStoryOnPlatform } from ':modules/posts/v2/use-cases/publish-story-on-platform/platforms/publish-story-on-platform.interface';
import { PublishStoryOnInstagramService } from ':modules/posts/v2/use-cases/publish-story-on-platform/services/publish-story-on-instagram.service';
import { UploadStoryOnInstagramService } from ':modules/posts/v2/use-cases/publish-story-on-platform/services/upload-story-on-instagram.service';
import { SlackChannel, SlackService } from ':services/slack.service';

export enum InstagramStoryType {
    PHOTO = 'PHOTO',
    VIDEO = 'VIDEO',
}

@singleton()
export class PublishStoryOnInstagramUseCase implements PublishStoryOnPlatform {
    constructor(
        private readonly _metaGraphApiHelper: MetaGraphApiHelper,
        private readonly _postsRepository: PostsRepository,
        private readonly _slackService: SlackService,
        private readonly _handleMetaPublicationErrorService: HandleMetaPublicationErrorService,
        private readonly _formatMediaService: FormatMediaService,
        private readonly _mediasRepository: MediasRepository,
        private readonly _uploadStoryOnInstagramService: UploadStoryOnInstagramService,
        private readonly _publishStoryOnInstagramService: PublishStoryOnInstagramService
    ) {}

    async execute(story: IPost, platform: Platform, credentialId: string): Promise<void> {
        logger.info('[PublishStoryOnInstagramUseCase] Start', {
            postId: story._id.toString(),
            platformId: platform._id.toString(),
            credentialId,
        });

        assert(story.attachments?.length, 'Post does not have attachments');
        const mediasUnordered = await this._mediasRepository.find({ filter: { _id: { $in: story.attachments } }, options: { lean: true } });
        const medias = story.attachments
            .map((id) => mediasUnordered.find((media) => media._id.toString() === id?.toString()))
            .filter((media): media is NonNullable<typeof media> => media !== undefined);

        assert(medias.length === story.attachments.length, 'Post attachments do not match medias');

        const igContainerIds: string[] = [];

        // ####### Upload media #######
        for (const medium of medias) {
            logger.info('[PublishStoryOnInstagramUseCase] processing medium', { id: medium._id.toString() });
            const mediaStoredObjectsWithType = await this._formatMediaService.formatMedias({
                medias: [medium],
                publicationType: PublicationType.STORY,
            });
            const mediaUrl = mediaStoredObjectsWithType[0].storedObject.publicUrl;
            const instagramStoryType = this._getInstagramStoryType(medium);
            const igMediaIdRes = await this._uploadStoryMedia(story, platform, credentialId, mediaUrl, instagramStoryType);
            if (igMediaIdRes.isErr()) {
                logger.error('[PublishStoryOnInstagramUseCase] Error on upload', { error: igMediaIdRes.error });
                await this._handleMetaPublicationErrorService.execute(igMediaIdRes.error, story._id.toString());
                return;
            }
            igContainerIds.push(igMediaIdRes.value.igContainerId);
        }

        const igUserId = platform.socialId;
        assert(igUserId, 'Missing socialId on platform');
        for (const igContainerId of igContainerIds) {
            // ####### Publish media #######
            const publishStoryRes = await this._publishStoryMedia(platform, credentialId, igContainerId);
            if (publishStoryRes.isErr()) {
                logger.error('[PublishStoryOnInstagramUseCase] Error on publish', { error: publishStoryRes.error });
                await this._handleMetaPublicationErrorService.execute(publishStoryRes.error, story._id.toString());
                return;
            }

            // ####### Update post in DB #######
            const updateRes = await this._updateStoryFromIgApi(
                story._id.toString(),
                credentialId,
                igUserId,
                publishStoryRes.value.igPostId
            );
            if (updateRes.isErr()) {
                logger.error('[PublishStoryOnInstagramUseCase] Error when updating story', {
                    storyId: story._id.toString(),
                    error: updateRes.error,
                });
                const line0 = `:rotating_light: Manual actions need to be taken !!!`;
                const line1 = `
                    Platform: ${story.key}, StoryId: ${story._id.toString()}, restaurantId: ${story.restaurantId.toString()}, 
                    credentialId: ${credentialId}, fbPostId: ${publishStoryRes.value.igPostId}, igContainerId: ${igContainerId}
                `.trim();
                const line2 = `ErrorCode: ${updateRes.error.code}  Endpoint: ${updateRes.error.endpoint}`;
                const line3 = `\`\`\`${updateRes.error.stringifiedRawError}\`\`\``;
                const text = `${line0}\n${line1}\n${line2}\n${line3}`;
                this._slackService.sendMessage({ text, channel: SlackChannel.POSTS_V2_ALERTS, shouldPing: true });
                break;
            }
            logger.info('[PublishStoryOnInstagramUseCase] Story updated');
        }
    }

    private async _uploadStoryMedia(
        story: IPost,
        platform: Platform,
        credentialId: string,
        mediaUrl: string,
        instagramStoryType: InstagramStoryType
    ): Promise<Result<{ igContainerId: string }, MetaGraphApiHelperErrorObject>> {
        if (instagramStoryType === InstagramStoryType.VIDEO) {
            const igContainerIdRes = await this._uploadStoryOnInstagramService.execute(story, platform, credentialId, {
                type: 'video',
                mediaUrl,
            });
            if (igContainerIdRes.isErr()) {
                return err(igContainerIdRes.error);
            }
            return ok({ igContainerId: igContainerIdRes.value.containerId });
        }

        if (instagramStoryType === InstagramStoryType.PHOTO) {
            const fbPhotoIdRes = await this._uploadStoryOnInstagramService.execute(story, platform, credentialId, {
                type: 'photo',
                mediaUrl,
            });
            if (fbPhotoIdRes.isErr()) {
                return err(fbPhotoIdRes.error);
            }
            return ok({ igContainerId: fbPhotoIdRes.value.containerId });
        }

        assert.fail('Unknown facebook story type');
    }

    private async _publishStoryMedia(
        platform: Platform,
        credentialId: string,
        igContainerId: string
    ): Promise<Result<{ igPostId: string }, MetaGraphApiHelperErrorObject>> {
        const igPostIdRes = await this._publishStoryOnInstagramService.execute(platform, credentialId, igContainerId);
        if (igPostIdRes.isErr()) {
            return err(igPostIdRes.error);
        }
        return ok({ igPostId: igPostIdRes.value.postId });
    }

    private _getInstagramStoryType(media: IMedia): InstagramStoryType {
        if (media.type === MediaType.VIDEO) {
            return InstagramStoryType.VIDEO;
        }
        if (media.type === MediaType.PHOTO) {
            return InstagramStoryType.PHOTO;
        }
        assert.fail('Media type not handled for facebook story');
    }

    private async _updateStoryFromIgApi(
        storyId: string,
        credentialId: string,
        igUserId: string,
        igMediaId: string
    ): Promise<Result<void, MetaGraphApiHelperErrorObject>> {
        const igMediaRes = await this._metaGraphApiHelper.getIgMedia(credentialId, igUserId, igMediaId);
        if (igMediaRes.isErr()) {
            return err(igMediaRes.error);
        }
        const igMedia = igMediaRes.value;
        assert(igMedia.media_product_type === 'STORY', 'Instagram media is not a story');
        const mappedIgMediaToMalouStory: Partial<IPost> = {
            published: PostPublicationStatus.PUBLISHED,
            socialId: igMedia.id,
            socialCreatedAt: new Date(igMedia.timestamp),
            socialUpdatedAt: new Date(igMedia.timestamp),
            socialLink: igMedia.permalink,
            socialAttachments: this._getSocialAttachments(igMedia),
            isPublishing: false,
        };

        await this._postsRepository.updateOne({ filter: { _id: toDbId(storyId) }, update: mappedIgMediaToMalouStory });

        return ok();
    }

    private _getSocialAttachments(igMedia: GetIgMediaResponse): IPost['socialAttachments'] {
        const socialAttachments: IPost['socialAttachments'] = [];
        assert(igMedia.media_product_type === 'STORY', 'Instagram media is not a story');
        if (igMedia.media_type === 'IMAGE') {
            socialAttachments.push({
                type: SocialAttachmentsMediaTypes.IMAGE,
                urls: {
                    original: igMedia.media_url,
                },
                thumbnailUrl: igMedia.media_url,
            });
        } else if (igMedia.media_type === 'VIDEO') {
            socialAttachments.push({
                type: SocialAttachmentsMediaTypes.VIDEO,
                urls: {
                    original: igMedia.media_url,
                },
                thumbnailUrl: igMedia.thumbnail_url,
            });
        } else {
            assert.fail('Instagram story media is not a photo or video');
        }

        return socialAttachments;
    }
}
