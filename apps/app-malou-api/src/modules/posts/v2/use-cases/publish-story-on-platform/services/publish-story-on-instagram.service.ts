import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { errorReplacer, RetryError, retryResult } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiCredentialsHandlerErrorCodes } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    MetaGraphApiHelperEndpoint,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class PublishStoryOnInstagramService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        platform: Platform,
        credentialId: string,
        igContainerId: string
    ): Promise<Result<{ postId: string }, MetaGraphApiHelperErrorObject>> {
        const igUserId = platform.socialId;
        assert(igUserId, 'Missing socialId on platform');

        const publishStoryRes = await this._metaGraphApiHelper.publishIgContainer(credentialId, igUserId, igContainerId);
        if (publishStoryRes.isErr()) {
            return err(publishStoryRes.error);
        }

        const getContainerRes = await retryResult(() => this._metaGraphApiHelper.getIgContainer(credentialId, igUserId, igContainerId), {
            attempts: 10,
            backoffStrategy: 'exponential',
            isSuccess: (res) => res.status_code === 'PUBLISHED',
            shouldRetrySuccess: (res) => !['ERROR', 'EXPIRED'].includes(res.status_code),
        });
        if (getContainerRes.isErr()) {
            if (getContainerRes.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS) {
                return err({
                    endpoint: MetaGraphApiHelperEndpoint.GET_IG_CONTAINER,
                    code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                    stringifiedRawError: JSON.stringify(getContainerRes.error, errorReplacer),
                });
            }
            assert(getContainerRes.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            assert(getContainerRes.error.lastResult.isErr());
            return err(getContainerRes.error.lastResult.error);
        }

        return ok({ postId: publishStoryRes.value.mediaId });
    }
}
