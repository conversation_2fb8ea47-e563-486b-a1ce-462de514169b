import { err, ok, Result } from 'neverthrow';
import assert from 'node:assert/strict';
import { singleton } from 'tsyringe';

import { IPost } from '@malou-io/package-models';
import { errorReplacer, RetryError, retryResult } from '@malou-io/package-utils';

import { Platform } from ':modules/platforms/platforms.entity';
import { MetaGraphApiCredentialsHandlerErrorCodes } from ':modules/posts/v2/providers/meta/meta-graph-api-credentials-handler/meta-graph-api-credentials-handler.definitions';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import {
    MetaGraphApiHelperEndpoint,
    MetaGraphApiHelperErrorObject,
} from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper.definitions';

@singleton()
export class UploadStoryOnInstagramService {
    constructor(private readonly _metaGraphApiHelper: MetaGraphApiHelper) {}

    async execute(
        story: IPost,
        platform: Platform,
        credentialId: string,
        mediaOptions: {
            type: 'photo' | 'video';
            mediaUrl: string;
        }
    ): Promise<Result<{ containerId: string }, MetaGraphApiHelperErrorObject>> {
        const pageId = platform.socialId;
        assert(pageId, 'Missing socialId on platform');

        let uploadRes: Result<{ containerId: string }, MetaGraphApiHelperErrorObject> | undefined;

        if (mediaOptions.type === 'photo') {
            uploadRes = await this._metaGraphApiHelper.createIgImageContainerForStory(
                credentialId,
                pageId,
                mediaOptions.mediaUrl,
                story.userTagsList?.[0] ?? undefined
            );
        } else if (mediaOptions.type === 'video') {
            uploadRes = await this._metaGraphApiHelper.createIgVideoContainerForStory(
                credentialId,
                pageId,
                mediaOptions.mediaUrl,
                story.userTagsList?.[0] ?? undefined
            );
        } else {
            assert.fail('Instagram media type not supported for story upload');
        }

        if (uploadRes.isErr()) {
            return err(uploadRes.error);
        }

        const getContainerRes = await retryResult(
            () => this._metaGraphApiHelper.getIgContainer(credentialId, pageId, uploadRes.value.containerId),
            {
                attempts: 10,
                backoffStrategy: 'exponential',
                isSuccess: (res) => res.status_code === 'FINISHED',
                shouldRetrySuccess: (res) => !['ERROR', 'EXPIRED'].includes(res.status_code),
            }
        );
        if (getContainerRes.isErr()) {
            if (getContainerRes.error.error === RetryError.SHOULD_NOT_RETRY_AFTER_SUCCESS) {
                return err({
                    endpoint: MetaGraphApiHelperEndpoint.GET_IG_CONTAINER,
                    code: MetaGraphApiCredentialsHandlerErrorCodes.UNKNOWN_ERROR,
                    stringifiedRawError: JSON.stringify(getContainerRes.error, errorReplacer),
                });
            }
            assert(getContainerRes.error.error === RetryError.STILL_ERROR_AFTER_RETRIES);
            assert(getContainerRes.error.lastResult.isErr());
            return err(getContainerRes.error.lastResult.error);
        }

        return ok({ containerId: getContainerRes.value.id });
    }
}
