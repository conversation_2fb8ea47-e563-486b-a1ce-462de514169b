import { singleton } from 'tsyringe';

import { GetTiktokQueryCreatorInfoDto, SocialPostDto } from '@malou-io/package-dto';
import {
    DeviceType,
    getFeatureFlaggedPlatforms,
    getPlatformKeysWithReel,
    getSocialPlatformKeysWithPost,
    PlatformKey,
    PostLocation,
    TiktokPrivacyStatus,
    WHITELISTED_PAGE_IDS,
} from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { TiktokQueryCreatorInfoUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-query-creator-info.use-case';
import { PostAuthor, PostAuthorProps } from ':modules/posts/v2/entities/author.entity';
import { SocialPost } from ':modules/posts/v2/entities/social-post.entity';
import { MetaGraphApiHelper } from ':modules/posts/v2/providers/meta/meta-graph-api-helper/meta-graph-api.helper';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import { isFeatureAvailableForUser } from ':services/experimentations-service/experimentation.service';

@singleton()
export class CreateSocialPostUseCase {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _metaGraphApiHelper: MetaGraphApiHelper,
        private readonly _tiktokQueryCreatorInfoUseCase: TiktokQueryCreatorInfoUseCase
    ) {}

    async execute({
        restaurantId,
        author,
        date,
        isReel,
        createdFromDeviceType,
    }: {
        restaurantId: string;
        author: PostAuthorProps;
        date?: Date;
        isReel: boolean;
        createdFromDeviceType?: DeviceType;
    }): Promise<SocialPostDto> {
        logger.info('[CREATE SOCIAL POST] started', { restaurantId, author });

        const platforms = await this._platformsRepository.getPlatformsByRestaurantId(restaurantId);
        const keys = await this._getSocialPlatformKeys(platforms, isReel, author);

        const postAuthor = new PostAuthor(author);

        let location: PostLocation | undefined = undefined;
        if (keys.includes(PlatformKey.FACEBOOK)) {
            location = await this._getFacebookLocation(platforms);
        }

        const tiktokOptions = await this._getDefaultTiktokOptions(restaurantId);

        const createPost: SocialPost = SocialPost.createEmpty({
            platformKeys: keys,
            author: postAuthor,
            location,
            date,
            isReel,
            tiktokOptions,
            createdFromDeviceType,
        });

        await this._postsRepository.createSocialPost(createPost, restaurantId);
        const dto = createPost.toDto();

        logger.info('[CREATE SOCIAL POST] finished', dto);
        return dto;
    }

    private async _getSocialPlatformKeys(platforms: Platform[], isReel: boolean, user: PostAuthorProps): Promise<PlatformKey[]> {
        const basePlatformKeys = isReel ? getPlatformKeysWithReel() : getSocialPlatformKeysWithPost();
        const featureFlaggedPlatforms = getFeatureFlaggedPlatforms();

        const featureFlaggedPlatformsEnabledForUser = await Promise.all(
            featureFlaggedPlatforms.map(async (ffPlatform) => {
                const enabled = ffPlatform.featureFlagKey
                    ? await isFeatureAvailableForUser({ userId: user.id, featureName: ffPlatform.featureFlagKey })
                    : true;
                return { key: ffPlatform.key, enabled };
            })
        );

        const platformKeysEnabled = basePlatformKeys.filter((key) => {
            const featureFlaggedPlatform = featureFlaggedPlatforms.find((ffPlatform) => ffPlatform.key === key);
            return (
                !featureFlaggedPlatform ||
                featureFlaggedPlatformsEnabledForUser.some((platform) => platform.key === featureFlaggedPlatform.key)
            );
        });

        const connectedSocialPlatforms = platforms.filter(
            (platform) =>
                platformKeysEnabled.includes(platform.key) && (platform.key !== PlatformKey.MAPSTR || !!platform.credentials?.length)
        );

        return connectedSocialPlatforms.map((platform) => platform.key);
    }

    private async _getFacebookLocation(platforms: Platform[]): Promise<PostLocation | undefined> {
        const fbPlatform = platforms.find((platform) => platform.key === PlatformKey.FACEBOOK);
        if (!fbPlatform) {
            return undefined;
        }
        const credentialId = fbPlatform.credentials?.[0];
        const fbPageId = fbPlatform.socialId;
        if (!credentialId || !fbPageId) {
            return undefined;
        }

        const res = await this._metaGraphApiHelper.fetchPageLocation(credentialId, fbPageId);
        if (res.isErr()) {
            return undefined;
        }
        const postLocation = res.value;
        if (
            (postLocation.location?.latitude !== undefined && postLocation.location?.longitude !== undefined) ||
            WHITELISTED_PAGE_IDS.includes(postLocation.id)
        ) {
            return {
                id: postLocation.id,
                link: postLocation.link,
                name: postLocation.name,
                location:
                    postLocation.location && postLocation.location.latitude && postLocation.location.longitude
                        ? {
                              ...postLocation.location,
                              latitude: postLocation.location.latitude,
                              longitude: postLocation.location.longitude,
                          }
                        : undefined,
            };
        }
        return undefined;
    }

    private async _getDefaultTiktokOptions(restaurantId: string): Promise<SocialPost['tiktokOptions']> {
        let creatorInfo: GetTiktokQueryCreatorInfoDto | null = null;
        try {
            creatorInfo = await this._tiktokQueryCreatorInfoUseCase.execute({ restaurantId });
        } catch (error) {
            logger.error('[CREATE SOCIAL POST] Error while fetching tiktok creator info', error);
        }
        return {
            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
            interactionAbility: {
                comment: creatorInfo?.commentDisabled === false,
                duet: false,
                stitch: false,
            },
            contentDisclosureSettings: {
                isActivated: false,
                yourBrand: false,
                brandedContent: false,
            },
            autoAddMusic: false,
        };
    }
}
