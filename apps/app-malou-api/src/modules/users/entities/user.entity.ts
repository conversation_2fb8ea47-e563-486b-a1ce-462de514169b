import { DateTime } from 'luxon';

import { ApplicationLanguage, BaseEntity, EntityConstructor, Role, UserCaslRole } from '@malou-io/package-utils';

export type UserProps = EntityConstructor<User>;

export class User extends BaseEntity {
    email: string;
    password: string;
    subscriptionsProviderId?: string;
    settings: UserSettings;
    organizationIds: string[];
    role: Role;
    caslRole: UserCaslRole;
    verified: boolean;
    defaultLanguage: ApplicationLanguage;
    hasV3Access: boolean;
    hasBeenDeactivatedByAdmin: boolean;

    name?: string;
    lastname?: string;
    profilePicture?: string;
    createdByUserId?: string;
    expireSessionBefore?: Date;
    shouldExpireAbilitySession?: boolean;
    lastVisitedRestaurantId?: string;

    constructor(data: UserProps) {
        super(data);
        this.name = data.name;
        this.email = data.email;
        this.password = data.password;
        this.subscriptionsProviderId = data.subscriptionsProviderId;
        this.settings = data.settings;
        this.organizationIds = data.organizationIds;
        this.lastname = data.lastname;
        this.profilePicture = data.profilePicture;
        this.role = data.role;
        this.caslRole = data.caslRole;
        this.verified = data.verified;
        this.hasBeenDeactivatedByAdmin = data.hasBeenDeactivatedByAdmin;
        this.createdByUserId = data.createdByUserId;
        this.defaultLanguage = data.defaultLanguage;
        this.expireSessionBefore = data.expireSessionBefore;
        this.shouldExpireAbilitySession = data.shouldExpireAbilitySession;
        this.hasV3Access = data.hasV3Access;
        this.settings = new UserSettings(data.settings);
        this.lastVisitedRestaurantId = data.lastVisitedRestaurantId?.toString();
    }

    canReceivePushNotifications(): boolean {
        return this.settings?.notificationSettings?.userDevicesTokens?.length > 0 && this.settings?.notificationSettings?.active;
    }

    canReceiveMessagesPushNotifications(): boolean {
        return this.settings?.notificationSettings?.messages?.active;
    }

    canReceiveReviewsPushNotifications(): boolean {
        return this.settings?.notificationSettings?.reviews?.active;
    }

    canReceiveMessagesPushNotificationsInRealTime(): boolean {
        return this.settings?.notificationSettings?.messages?.realtime;
    }

    canReceiveReviewsPushNotificationsInRealTime(): boolean {
        return this.settings?.notificationSettings?.reviews?.realtime;
    }

    canReceiveMessagesPushNotificationsForDay(day: number): boolean {
        return this.settings?.notificationSettings?.messages?.receivingWeekDays.includes(day);
    }

    canReceiveReviewsPushNotificationsForDay(day: number): boolean {
        return this.settings?.notificationSettings?.reviews?.receivingWeekDays.includes(day);
    }

    canReceiveReviewsPushNotificationsForAutoRepliedReviews(): boolean {
        return this.settings?.notificationSettings?.reviews?.includeAutoRepliedReviews;
    }

    isReviewRatingInUserConcernedRatings(rating?: string): boolean {
        return rating ? this.settings?.notificationSettings?.reviews?.concernedRatings?.includes(parseInt(rating, 10)) : false;
    }

    canReceiveNoMoreScheduledPostsPushNotifications(): boolean {
        const diffInDays = DateTime.now().diff(
            DateTime.fromJSDate(this.settings?.notificationSettings?.posts?.noMoreScheduledPosts?.lastNotificationSentDate),
            'days'
        ).days;

        return this.settings?.notificationSettings?.posts?.noMoreScheduledPosts?.active && diffInDays >= 2;
    }

    canReceivePostPublishingError(): boolean {
        return this.settings?.notificationSettings?.posts?.publishError?.active;
    }
}

export class UserSettings {
    receiveFeedbacks: boolean;
    receiveMessagesNotifications: { active: boolean; restaurantsIds: string[] };
    notificationSettings: NotificationSettings;
    notifications: UserNotificationSettings;

    constructor(data: UserProps['settings']) {
        this.receiveFeedbacks = data.receiveFeedbacks;
        this.receiveMessagesNotifications = data.receiveMessagesNotifications;
        this.notifications = new UserNotificationSettings(data.notifications);
        this.notificationSettings = new NotificationSettings(data.notificationSettings);
    }
}

export class UserNotificationSettings {
    email: {
        reviewReplyReminder: {
            active: boolean;
        };
        specialHourReminder: {
            active: boolean;
        };
        postSuggestion: {
            active: boolean;
        };
        postError: {
            active: boolean;
        };
        roiActivated: {
            active: boolean;
        };
        summary: {
            active: boolean;
        };
        platformDisconnected: {
            active: boolean;
        };
    };
    web: {
        showFloatingToast: boolean;
        filters: {
            restaurantIds: string[];
        };
        newReviews: {
            active: boolean;
        };
        reviewReplyReminder: {
            active: boolean;
        };
        specialHourReminder: {
            active: boolean;
        };
        postSuggestion: {
            active: boolean;
        };
        postError: {
            active: boolean;
        };
        newMessage: {
            active: boolean;
        };
        roiActivated: {
            active: boolean;
        };
        platformDisconnected: {
            active: boolean;
        };
        informationUpdateError: {
            active: boolean;
        };
    };

    mobile: {
        userDevicesTokens: string[];
        active: boolean;
        newMessage: {
            active: boolean;
            realtime: boolean;
            receivingWeekDays: number[];
        };
        newReviews: {
            active: boolean;
            realtime: boolean;
            receivingWeekDays: number[];
            concernedRatings: number[];
            includeAutoRepliedReviews: boolean;
        };
        noMoreScheduledPosts: {
            active: boolean;
        };
    };

    constructor(data: UserProps['settings']['notifications']) {
        this.email = {
            reviewReplyReminder: {
                active: data?.email?.reviewReplyReminder?.active,
            },
            specialHourReminder: {
                active: data?.email?.specialHourReminder?.active,
            },
            postSuggestion: {
                active: data?.email?.postSuggestion?.active,
            },
            postError: {
                active: data?.email?.postError?.active,
            },
            roiActivated: {
                active: data?.email?.roiActivated?.active,
            },
            summary: {
                active: data?.email?.summary?.active,
            },
            platformDisconnected: {
                active: data?.email?.platformDisconnected?.active,
            },
        };

        this.web = {
            showFloatingToast: data?.web?.showFloatingToast,

            filters: {
                restaurantIds: data?.web?.filters?.restaurantIds ?? [],
            },
            newReviews: {
                active: data?.web?.newReviews?.active,
            },
            reviewReplyReminder: {
                active: data?.web?.reviewReplyReminder?.active,
            },
            specialHourReminder: {
                active: data?.web?.specialHourReminder?.active,
            },
            postSuggestion: {
                active: data?.web?.postSuggestion?.active,
            },
            postError: {
                active: data?.web?.postError?.active,
            },
            newMessage: {
                active: data?.web?.newMessage?.active,
            },
            roiActivated: {
                active: data?.web?.roiActivated?.active,
            },
            platformDisconnected: {
                active: data?.web?.platformDisconnected?.active,
            },
            informationUpdateError: {
                active: data?.web?.informationUpdateError?.active,
            },
        };

        this.mobile = {
            userDevicesTokens: data?.mobile?.userDevicesTokens ?? [],
            active: data?.mobile?.active ?? true,
            newMessage: {
                active: data?.mobile?.newMessage?.active,
                realtime: data?.mobile?.newMessage?.realtime,
                receivingWeekDays: data?.mobile?.newMessage?.receivingWeekDays,
            },
            newReviews: {
                active: data?.mobile?.newReviews?.active,
                realtime: data?.mobile?.newReviews?.realtime,
                receivingWeekDays: data?.mobile?.newReviews?.receivingWeekDays,
                concernedRatings: data?.mobile?.newReviews?.concernedRatings,
                includeAutoRepliedReviews: data?.mobile?.newReviews?.includeAutoRepliedReviews,
            },
            noMoreScheduledPosts: {
                active: data?.mobile?.noMoreScheduledPosts?.active,
            },
        };
    }
}

export class NotificationSettings {
    userDevicesTokens: string[];
    active: boolean;
    reviews: {
        active: boolean;
        realtime: boolean;
        receivingWeekDays: number[];
        concernedRatings: number[];
        includeAutoRepliedReviews: boolean;
    };
    messages: { active: boolean; realtime: boolean; receivingWeekDays: number[] };
    posts: { noMoreScheduledPosts: { active: boolean; lastNotificationSentDate: Date }; publishError: { active: boolean } };

    constructor(data: UserProps['settings']['notificationSettings']) {
        this.userDevicesTokens = data?.userDevicesTokens ?? [];
        this.active = data?.active;
        this.reviews = data?.reviews;
        this.messages = data?.messages;
        this.posts = {
            noMoreScheduledPosts: {
                active: data?.posts?.noMoreScheduledPosts?.active,
                lastNotificationSentDate: new Date(data?.posts?.noMoreScheduledPosts?.lastNotificationSentDate),
            },
            publishError: {
                active: data?.posts?.publishError?.active,
            },
        };
    }
}
