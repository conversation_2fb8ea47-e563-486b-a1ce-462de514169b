import jwt from 'jsonwebtoken';
import { singleton } from 'tsyringe';

import { IUser, toDbId } from '@malou-io/package-models';
import { EmailCategory, EmailType, generateRandomPassword } from '@malou-io/package-utils';

import { Config } from ':config';
import MailingUseCases from ':modules/mailing/use-cases';
import ReportsRepository from ':modules/reports/reports.repository';

import { UsersRepository } from '../users.repository';

@singleton()
export class CreateNewAccountService {
    constructor(
        private readonly _usersRepository: UsersRepository,
        private readonly _reportsRepository: ReportsRepository,
        private readonly _mailingUseCases: MailingUseCases
    ) {}

    async createAccount({
        user,
        verified = false,
        sendConfirmEmail = true,
    }: {
        user: Partial<IUser>;
        verified?: boolean;
        sendConfirmEmail?: boolean;
    }): Promise<IUser> {
        const newUser = {
            ...user,
            email: user.email?.trim().toLowerCase(),
            password: generateRandomPassword(),
            verified,
        };
        const userInDb = await this._usersRepository.createUser(newUser);
        await this._reportsRepository.createReportsForUser(toDbId(userInDb._id));
        if (sendConfirmEmail) {
            await this.sendConfirmEmail(userInDb._id.toString());
        }
        return userInDb;
    }

    async sendConfirmEmail(userId: string) {
        const { password, _id, createdAt } = await this._getUserWithPasswordHash(userId);
        const token = this._getTempUserToken({ _id, password, createdAt });
        const url = this._getConfirmAccountUrl(userId, token);
        return this._mailingUseCases.sendEmail(EmailCategory.USER_NOTIF, EmailType.CONFIRM_CREATE_ACCOUNT, {
            userId,
            url,
        });
    }

    private _getTempUserToken({ password: passwordHash, _id: userId, createdAt }: { password: any; _id: any; createdAt: any }) {
        const secret = `${passwordHash}-${createdAt}`;
        const token = jwt.sign({ userId }, secret);
        return token;
    }

    private _getUserWithPasswordHash(userId: string) {
        return this._usersRepository.getUserWithPasswordHash({ _id: userId });
    }

    private _getConfirmAccountUrl = function (userId: string, token: string) {
        return `${Config.baseAppUrl}/auth/confirm/${userId}/${token}`;
    };
}
