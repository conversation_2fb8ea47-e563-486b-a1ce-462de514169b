import * as crypto from 'crypto';
import jwt from 'jsonwebtoken';
import { singleton } from 'tsyringe';

import { DbId, ID, IMedia, IOrganization, IUser, IUserRestaurant, OverwriteOrAssign, toDbId, toDbIds } from '@malou-io/package-models';
import { ApplicationLanguage, CaslRole, MalouErrorCode, Role } from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import { PostsRepository } from ':modules/posts/v2/repository/posts.repository';
import ReportsRepository from ':modules/reports/reports.repository';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';

import { CreateNewAccountService } from './services/create-new-account.service';
import { UsersRepository } from './users.repository';

@singleton()
export default class UsersUseCases {
    constructor(
        private readonly _usersRepository: UsersRepository,
        private readonly _userRestaurantsRepository: UserRestaurantsRepository,
        private readonly _reportsRepository: ReportsRepository,
        private readonly _credentialsRepository: CredentialsRepository,
        private readonly _postsRepository: PostsRepository,
        private readonly _createNewAccountService: CreateNewAccountService
    ) {}

    async getUserWithActualRestaurants(id: string) {
        return this._usersRepository.getFilteredUserRestaurants({ _id: toDbId(id) }).then(([user]) => user);
    }

    getUserById = async (_id: string): Promise<any> =>
        // TODO: populate with different levels, type will work properly when with have strict false
        // OverwriteOrAssign<
        //     IUser,
        //     {
        //         restaurants: IUserRestaurantWithRestaurant[];
        //         organizations: IOrganization[];
        //         profilePicture: IMedia;
        //     }
        // >
        this._usersRepository.findOne({
            filter: { _id: toDbId(_id) },
            options: {
                populate: [
                    {
                        path: 'profilePicture',
                    },
                    {
                        path: 'restaurants',
                        populate: [
                            {
                                path: 'restaurant',
                                select: ['roiActivated', 'type'],
                            },
                        ],
                    },
                    {
                        path: 'organizations',
                    },
                ],
                lean: true,
            },
        });

    async getUserByEmail(email: string): Promise<OverwriteOrAssign<
        IUser,
        {
            restaurants: IUserRestaurant[];
            organizations: IOrganization[];
            profilePicture: IMedia;
        }
    > | null> {
        const populatedUser = await this._usersRepository.findOne({
            filter: { email },
            options: {
                populate: [
                    {
                        path: 'profilePicture',
                    },
                    {
                        path: 'restaurants',
                    },
                    {
                        path: 'organizations',
                    },
                ],
                lean: true,
            },
        });
        return populatedUser;
    }

    forceExpireAbilitySession = (_id: ID) => {
        return this._usersRepository.findOneAndUpdate({ filter: { _id }, update: { shouldExpireAbilitySession: true } });
    };

    async updateUserRestaurant(filter, data: Partial<IUserRestaurant>, options: any) {
        return this._userRestaurantsRepository.findOneAndUpdate({ filter, update: data, options });
    }

    async getUserRestaurantsOfUserId(userId: string): Promise<IUserRestaurant[]> {
        return this._userRestaurantsRepository.find({ filter: { userId: toDbId(userId) }, options: { lean: true } });
    }

    async getUserRestaurantsPopulatedWithUser(
        filter: Partial<IUserRestaurant> = {}
    ): Promise<OverwriteOrAssign<IUserRestaurant, { user: IUser }>[]> {
        const userRestaurants = await this._userRestaurantsRepository.find({
            filter,
            options: { populate: [{ path: 'user' }], lean: true },
        });
        return userRestaurants;
    }

    /**
     * @deprecated should not be used except for exceptional cases. Deactivate user instead.
     */
    deleteUserById = async (userId: string) => {
        const userToDelete = await this.getUserById(userId);

        if (!userToDelete) {
            throw new MalouError(MalouErrorCode.USER_NOT_FOUND, { metadata: { userId } });
        }
        if (userToDelete.role === Role.ADMIN) {
            throw new MalouError(MalouErrorCode.USER_CANNOT_DELETE_ADMIN);
        }
        const credential = await this._credentialsRepository.findOne({
            filter: { userId: toDbId(userId) },
            options: { lean: true },
        });
        if (credential) {
            // Need to do this properly and reassign the credential to another user otherwise this will cause bugs
            throw new MalouError(MalouErrorCode.USER_HAS_LINKED_ENTITIES);
        }
        const post = await this._postsRepository.findOne({
            filter: { 'author._id': toDbId(userId) },
            options: { lean: true },
        });
        if (post) {
            // Need to do this properly and reassign the post to another user otherwise this will cause bugs
            throw new MalouError(MalouErrorCode.USER_HAS_LINKED_ENTITIES);
        }
        await this._usersRepository.deleteOne({ filter: { _id: toDbId(userId) } });
        await this._applyHooksAfterDelete([userId]);
    };

    async deleteManyUsers({ _id: userIds }: { _id: string[] }) {
        await this._usersRepository.deleteMany({ filter: { _id: toDbIds(userIds) } });
        await this._applyHooksAfterDelete(userIds);
    }

    private _applyHooksAfterDelete = async (userIds: string[]) => {
        await Promise.all([
            this._userRestaurantsRepository.deleteMany({ filter: { userId: { $in: toDbIds(userIds) } } }),
            this._reportsRepository.removeUsersFromReports(userIds),
        ]);
    };

    createAccount = async ({
        user,
        verified = false,
        sendConfirmEmail = true,
    }: {
        user: Partial<IUser>;
        verified?: boolean;
        sendConfirmEmail?: boolean;
    }): Promise<IUser> => {
        return this._createNewAccountService.createAccount({
            user,
            verified,
            sendConfirmEmail,
        });
    };

    async updateUserFromAdmin(
        id: string,
        data: Partial<IUser>
    ): Promise<OverwriteOrAssign<
        IUser,
        {
            profilePicture: IMedia;
        }
    > | null> {
        if (data.email) {
            data.email = data.email?.trim().toLowerCase();
        }

        const updatedUser = await this._usersRepository.findOneAndUpdate({
            filter: { _id: toDbId(id) },
            update: data,
            options: {
                populate: [
                    {
                        path: 'profilePicture',
                    },
                ],
            },
        });

        // If a user is deactivated / activated, we need to remove him from other services
        if (data.verified === false) {
            await this.deactivateUserFromServices(id);
        } else if (data.verified === true) {
            await this.activateUserFromServices(id);
        }

        return updatedUser;
    }

    async deactivateUserFromServices(userId: string) {
        await this._reportsRepository.updateMany({ filter: { userId: toDbId(userId) }, update: { active: false } });
    }

    async activateUserFromServices(userId: string) {
        await this._reportsRepository.updateMany({ filter: { userId: toDbId(userId) }, update: { active: true } });
    }

    /**
     * get list of users matching filter and filter restaurants field with active and existing restaurants in the db
     * @param {*} filter
     */
    async getUsersWithActualRestaurantsField(filter) {
        return this._usersRepository.getFilteredUserRestaurants(filter);
    }

    getPasswordResetUrl(user, token) {
        return `${Config.baseAppUrl}/auth/password/new/${user._id}/${token}`;
    }

    getAddedToRestaurantUrl(restaurantId: ID): string {
        return `${process.env.BASE_URL}/restaurants/${restaurantId}`;
    }

    getTempUserToken({ password: passwordHash, _id: userId, createdAt }: { password: any; _id: any; createdAt: any }) {
        const secret = `${passwordHash}-${createdAt}`;
        const token = jwt.sign({ userId }, secret);
        return token;
    }

    getUserWithPasswordHash(userId: string) {
        return this._usersRepository.getUserWithPasswordHash({ _id: userId });
    }

    getUserByEmailWithPasswordHash(email: string) {
        return this._usersRepository.getUserWithPasswordHash({ email });
    }

    getUserSettings(userId: ID) {
        return this._usersRepository.findOne({
            filter: { _id: userId },
            options: {
                lean: true,
                populate: [
                    {
                        path: 'settings',
                        populate: [
                            {
                                path: 'receiveMessagesNotifications',
                                populate: [
                                    {
                                        path: 'restaurants',
                                    },
                                ],
                            },
                        ],
                    },
                ],
            },
        });
    }

    getFrontChatUserEmailHash(email: string, language: ApplicationLanguage) {
        const hmac = crypto.createHmac('sha256', Config.vendors.front.chatUserVerificationSecret[language]);
        return hmac.update(email).digest('hex');
    }

    willDowngradeLastOwner = (userRestaurants: IUserRestaurant[], targetedUrId: ID, caslRole: string) => {
        if (!userRestaurants.some((userRestaurant) => userRestaurant.caslRole === CaslRole.OWNER)) {
            return false;
        } // If there already is no owner. Should not happen but if the restaurant is poorly configured it could
        const updatedUserRestaurants = userRestaurants.map((userRestaurant) => {
            if ((userRestaurant._id as DbId).equals(targetedUrId)) {
                return { ...userRestaurant, caslRole };
            }
            return userRestaurant;
        });
        return !updatedUserRestaurants.some((userRestaurant) => userRestaurant.caslRole === CaslRole.OWNER);
    };

    updateUserLastVisitedRestaurantId = ({ userId, restaurantId }: { userId: DbId; restaurantId: string }) => {
        return this._usersRepository.findOneAndUpdate({
            filter: { _id: userId },
            update: { lastVisitedRestaurantId: toDbId(restaurantId) },
        });
    };
}
