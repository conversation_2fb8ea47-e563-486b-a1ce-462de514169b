import { container } from 'tsyringe';

import { newDbId } from '@malou-io/package-models';
import { CaslRole, MalouErrorCode, ReportType } from '@malou-io/package-utils';

import { registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { getDefaultCredential } from ':modules/credentials/tests/credential.builder';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import ReportsRepository from ':modules/reports/reports.repository';
import { getDefaultReport } from ':modules/reports/tests/report.builder';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';
import { UserRestaurantsRepository } from ':modules/user-restaurants/user-restaurants.repository';
import { getDefaultUser, getDefaultUserRestaurant } from ':modules/users/tests/user.builder';
import { UsersRepository } from ':modules/users/users.repository';
import UsersUseCases from ':modules/users/users.use-cases';

describe('User UseCases', () => {
    let usersUseCases: UsersUseCases;
    let usersRepository: UsersRepository;
    let reportsRepository: ReportsRepository;
    let userRestaurantsRepository: UserRestaurantsRepository;

    beforeAll(() => {
        registerRepositories([
            'UsersRepository',
            'RestaurantsRepository',
            'ReportsRepository',
            'UserRestaurantsRepository',
            'CredentialsRepository',
            'PostsRepository',
        ]);

        usersUseCases = container.resolve(UsersUseCases);
        usersRepository = container.resolve(UsersRepository);
        userRestaurantsRepository = container.resolve(UserRestaurantsRepository);
        reportsRepository = container.resolve(ReportsRepository);
    });

    it('getUserWithActualRestaurants - should return no user if it doesnt exist', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('restaurant_0').build(),
                            getDefaultRestaurant().name('restaurant_1').build(),
                            getDefaultRestaurant().name('restaurant_2').build(),
                        ];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[2]._id)
                                .build(),
                        ];
                    },
                },
            },
        });

        await testCase.build();

        const user = await usersUseCases.getUserWithActualRestaurants(newDbId().toString());
        expect(user).toBeUndefined();
    });

    it('getUserWithActualRestaurants - should return user with all active restaurants', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('restaurant_0').active(false).build(),
                            getDefaultRestaurant().name('restaurant_1').build(),
                            getDefaultRestaurant().name('restaurant_2').build(),
                            getDefaultRestaurant().name('restaurant_3').build(),
                        ];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .caslRole(CaslRole.ADMIN)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[1]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[3]._id)
                                .caslRole(CaslRole.GUEST)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                return {
                    ...dependencies.users[0],
                    password: null,
                    organizations: [],
                    restaurants: [
                        {
                            ...dependencies.userRestaurants[1],
                            restaurantDetails: {
                                name: dependencies.restaurants[1].name,
                                active: dependencies.restaurants[1].active,
                                _id: dependencies.restaurants[1]._id,
                            },
                        },
                        {
                            ...dependencies.userRestaurants[2],
                            restaurantDetails: {
                                name: dependencies.restaurants[3].name,
                                active: dependencies.restaurants[3].active,
                                _id: dependencies.restaurants[3]._id,
                            },
                        },
                    ],
                };
            },
        });

        await testCase.build();
        const { users } = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const user = await usersUseCases.getUserWithActualRestaurants(users[0]._id.toString());
        expect(user).toStrictEqual(expectedResult);
    });

    it('getUserWithActualRestaurants - should return user with no restaurants if none is active', async () => {
        const testCase = new TestCaseBuilderV2<'restaurants' | 'users' | 'userRestaurants'>({
            seeds: {
                restaurants: {
                    data() {
                        return [
                            getDefaultRestaurant().name('restaurant_0').active(false).build(),
                            getDefaultRestaurant().name('restaurant_1').active(false).build(),
                            getDefaultRestaurant().name('restaurant_2').active(false).build(),
                        ];
                    },
                },
                users: {
                    data() {
                        return [getDefaultUser().name('user_0').email('<EMAIL>').build()];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[0]._id)
                                .build(),
                            getDefaultUserRestaurant()
                                .userId(dependencies.users()[0]._id)
                                .restaurantId(dependencies.restaurants()[2]._id)
                                .build(),
                        ];
                    },
                },
            },
            expectedResult(dependencies) {
                return {
                    ...dependencies.users[0],
                    password: null,
                    organizations: [],
                    restaurants: [],
                };
            },
        });

        await testCase.build();
        const { users } = testCase.getSeededObjects();
        const expectedResult = testCase.getExpectedResult();

        const user = await usersUseCases.getUserWithActualRestaurants(users[0]._id.toString());
        expect(user).toStrictEqual(expectedResult);
    });

    it('should create reports when creating user', async () => {
        const user = await usersUseCases.createAccount({ user: getDefaultUser().build() });

        const reports = await reportsRepository.find({ filter: { userId: user._id }, options: { lean: true } });
        expect(reports.length).toBe(4);
        expect(reports).toEqual(
            expect.arrayContaining([
                expect.objectContaining({ type: ReportType.MONTHLY_PERFORMANCE, userId: user._id, active: true }),
                expect.objectContaining({ type: ReportType.WEEKLY_PERFORMANCE, userId: user._id, active: true }),
                expect.objectContaining({ type: ReportType.DAILY_REVIEWS, userId: user._id, active: true }),
                expect.objectContaining({ type: ReportType.WEEKLY_REVIEWS, userId: user._id, active: true }),
            ])
        );
    });

    it('should delete reports and userRestaurants when deleting users', async () => {
        const testCase = new TestCaseBuilderV2<'users' | 'userRestaurants' | 'reports'>({
            seeds: {
                users: {
                    data() {
                        return [getDefaultUser().email('<EMAIL>').build()];
                    },
                },
                reports: {
                    data(dependencies) {
                        return [
                            getDefaultReport().userId(dependencies.users()[0]._id).type(ReportType.MONTHLY_PERFORMANCE).build(),
                            getDefaultReport().userId(dependencies.users()[0]._id).type(ReportType.WEEKLY_REVIEWS).build(),
                        ];
                    },
                },
                userRestaurants: {
                    data(dependencies) {
                        return [
                            getDefaultUserRestaurant().userId(dependencies.users()[0]._id).build(),
                            getDefaultUserRestaurant().userId(dependencies.users()[0]._id).build(),
                        ];
                    },
                },
            },
            expectedResult() {
                return [];
            },
        });

        await testCase.build();
        const { users } = testCase.getSeededObjects();

        // Test that creation occurred as expected
        const user = await usersRepository.findOne({ filter: { _id: users[0]._id }, options: { lean: true } });
        if (!user) {
            expect(user).toBeDefined();
            return;
        }

        const [reports, userRestaurants] = await Promise.all([
            reportsRepository.find({ filter: { userId: user._id } }),
            userRestaurantsRepository.find({ filter: { userId: user._id } }),
        ]);

        expect(reports.length).toBe(2);
        expect(userRestaurants.length).toBe(2);

        const usersRestaurantsDeleteManySpy = jest.spyOn(userRestaurantsRepository, 'deleteMany');
        const removeUsersFromReportsSpy = jest.spyOn(reportsRepository, 'removeUsersFromReports');

        // Test deletion
        await usersUseCases.deleteUserById(user._id.toString());

        const [userAfterDeletion, reportsAfterDeletion, userRestaurantsAfterDeletion] = await Promise.all([
            usersRepository.findOne({ filter: { _id: users[0]._id } }),
            reportsRepository.find({ filter: { userId: user._id } }),
            userRestaurantsRepository.find({ filter: { userId: user._id } }),
        ]);

        expect(userAfterDeletion).toBe(null);
        expect(reportsAfterDeletion.length).toBe(0);
        expect(userRestaurantsAfterDeletion.length).toBe(0);
        expect(usersRestaurantsDeleteManySpy).toHaveBeenCalledTimes(1);
        expect(usersRestaurantsDeleteManySpy).toHaveBeenCalledWith({ filter: { userId: { $in: [user._id] } } });
        expect(removeUsersFromReportsSpy).toHaveBeenCalledTimes(1);
        expect(removeUsersFromReportsSpy).toHaveBeenCalledWith([user._id.toString()]);
    });

    it('should throw an error if trying to delete a user linked to a credential', async () => {
        const testCase = new TestCaseBuilderV2<'users' | 'credentials'>({
            seeds: {
                users: {
                    data() {
                        return [getDefaultUser().email('<EMAIL>').build()];
                    },
                },
                credentials: {
                    data(dependencies) {
                        return [getDefaultCredential().userId(dependencies.users()[0]._id).build()];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.USER_HAS_LINKED_ENTITIES,
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const userId = seededObjects.users[0]._id.toString();

        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(usersUseCases.deleteUserById(userId)).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });

    it('should throw an error if trying to delete a user linked to a post', async () => {
        const testCase = new TestCaseBuilderV2<'users' | 'posts'>({
            seeds: {
                users: {
                    data() {
                        return [getDefaultUser().email('<EMAIL>').build()];
                    },
                },
                posts: {
                    data(dependencies) {
                        return [getDefaultPost().author({ _id: dependencies.users()[0]._id, name: dependencies.users()[0].name }).build()];
                    },
                },
            },
            expectedErrorCode: MalouErrorCode.USER_HAS_LINKED_ENTITIES,
        });

        await testCase.build();
        const seededObjects = testCase.getSeededObjects();
        const userId = seededObjects.users[0]._id.toString();

        const expectedErrorCode = testCase.getExpectedErrorCode();

        await expect(usersUseCases.deleteUserById(userId)).rejects.toThrow(
            expect.objectContaining({
                malouErrorCode: expectedErrorCode,
            })
        );
    });
});
