import { injectable } from 'tsyringe';

import { CreateNewAccountService } from ':modules/users/services/create-new-account.service';

@injectable()
export class ResendConfirmationEmailUseCase {
    constructor(private readonly _createNewAccountService: CreateNewAccountService) {}

    async execute(userId: string): Promise<void> {
        await this._createNewAccountService.sendConfirmEmail(userId);
    }
}
