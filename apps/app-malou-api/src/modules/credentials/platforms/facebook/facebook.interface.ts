/**
 * The key presence depends on which fields are set in the query,
 * this is why all key are optional
 */
import { FacebookCredential } from ':modules/credentials/platforms/facebook/entities/facebook-credential.entity';

export interface IFacebookAccount {
    id?: string;
    name?: string;
    link?: string;
    access_token?: string;
    picture?: {
        data?: {
            height?: number;
            width?: number;
            is_silhouette?: boolean;
            url?: string;
        };
    };
    overall_star_rating?: number;
    location?: {
        city?: string;
        country?: string;
        street?: string;
        zip?: string;
        latitude?: number;
        longitude?: number;
    };
    instagram_business_account?: {
        id?: string;
        name?: string;
        biography?: string;
        profile_picture_url?: string;
        username?: string;
    };
    store_location_descriptor?: string;
    parent_page?: {
        name?: string;
        id?: string;
    };
    locations?: IFacebookDataWithPaging<IFacebookAccount>;
    has_transitioned_to_new_page_experience?: boolean;
}

export interface IFacebookUser {
    id?: string;
    name?: string;
    accounts?: IFacebookDataWithPaging<IFacebookAccount>;
}

export interface IFacebookPaging {
    cursors?: {
        before?: string;
        after?: string;
    };
    next?: string;
    previous?: string;
}

export interface IFacebookAccountAggregation<T = IFacebookAccount> {
    id: string;
    name: string;
    data: T[];
}

export interface IFacebookDataWithPaging<T> {
    data?: T[];
    paging?: IFacebookPaging;
}

export interface IFacebookInsights {
    id: string;
    name: string;
    title: string;
    description: string;
    period: string;
    values: {
        value: number;
        end_time: Date;
    }[];
}

export interface FacebookCredentialsWithPageId {
    credential: FacebookCredential;
    pageId: string;
}
