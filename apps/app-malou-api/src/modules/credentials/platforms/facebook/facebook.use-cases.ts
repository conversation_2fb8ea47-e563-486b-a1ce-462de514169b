// https://github.com/node-facebook/facebook-node-sdk
import axios from 'axios';
import { FB } from 'fb';
import _, { isNil, omitBy, uniqBy } from 'lodash';
import { DateTime } from 'luxon';
import assert from 'node:assert/strict';
import { container } from 'tsyringe';

import {
    EntityRepository,
    IComment,
    ICommentMention,
    ICredential,
    ID,
    IPostWithAttachments,
    IPostWithAttachmentsAndThumbnail,
    IReview,
    IUserWithOrganizations,
    toDbId,
    toDbIds,
} from '@malou-io/package-models';
import {
    eventsPermissions,
    facebookPageAccessTokenErrorCodes,
    getPlatformKeysLinkedWithMeta,
    isFacebookTimeoutError,
    isNotNil,
    MalouErrorCode,
    MediaType,
    PermissionStatus,
    PlatformKey,
    PostType,
    scopes,
    TimeInMilliseconds,
} from '@malou-io/package-utils';

import { Config } from ':config';
import { MalouError } from ':helpers/classes/malou-error';
import { getIntervals } from ':helpers/date/date';
import { logger } from ':helpers/logger';
import { isFulfilled } from ':helpers/utils';
import { FacebookCredential } from ':modules/credentials/platforms/facebook/entities/facebook-credential.entity';
import {
    IFacebookAccount,
    IFacebookAccountAggregation,
    IFacebookDataWithPaging,
    IFacebookInsights,
    IFacebookPaging,
} from ':modules/credentials/platforms/facebook/facebook.interface';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import {
    AccountWithPageAccess,
    ActionPermissionList,
    ConversationPlatform,
    CredentialValidityStatus,
    DebuggedToken,
    FacebookApiTypes,
    FacebookCityResult,
    FacebookComment,
    FacebookEntityPermissionStatus,
    FacebookMessage,
    FacebookPageInfo,
    FacebookPermissionStatus,
    FbApiResult,
    FbMessage,
    IgMediaContainerStatus,
    IgMessage,
    PermissionStatusRequirements,
    SocialConversationWithMessages,
} from ':modules/credentials/platforms/facebook/facebook.types';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { MalouRestaurantSearchResult } from ':modules/platforms/use-cases/search-social-ids/search-social-ids.interface';
import { FbGraphApiResponseIgStoryInsights } from ':modules/post-insights/post-insights.interface';
import { FbPostData } from ':modules/posts/platforms/facebook/facebook-post.interface';
import {
    IgMediaProductType,
    IgPagePostsResponse,
    IgPostData,
    IgStoryData,
} from ':modules/posts/platforms/instagram/instagram-post.interface';

FB.options({
    version: Config.platforms.facebook.api.apiVersion,
    appId: Config.platforms.facebook.api.appId,
    redirectUri: Config.platforms.facebook.api.redirectUri,
    timeout: Config.platforms.facebook.api.timeoutThresholdInMs,
});

const MAX_MEDIAS_LIMIT = 10_000;
const DEFAULT_IG_ACCOUNTS_LIMIT = 75;
const DEFAULT_ACCOUNTS_LIMIT = 150;

// 2108006 -> "Contenu multimédia publié avant la conversion en compte business"
// This error subcode only show up when the endpoint return only bad post
// If our research contains bad post and good post, this error does not show up, so we can just ignore it
const MEDIA_ERROR_SUBCODE_TO_IGNORE = 2108006;

const COMMENTS_NUMBER_PER_PAGE = 50;

const FB_PUBLISHED_POST_ERROR_MESSAGE_TO_IGNORE =
    '(#100) Object does not exist, cannot be loaded due to missing permission or reviewable feature, or does not support this operation.';

const facebookCredentialsRepository = container.resolve(FacebookCredentialsRepository);
const platformsRepository = container.resolve(PlatformsRepository);

// To implements IPlatformCredentialsUseCases (use cases returned by getPlatformCredentialsUseCases)
export const create = function () {
    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
        message: 'Method not implemented.',
    });
};

// To implements IPlatformCredentialsUseCases (use cases returned by getPlatformCredentialsUseCases)
export const patch = function () {
    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
        message: 'Method not implemented.',
    });
};

/**
 *
 * @returns {boolean}
 */
export const isIgConnected = async function (credentialId: string): Promise<boolean> {
    const credential = await facebookCredentialsRepository.getCredentialById(credentialId);
    return credential?.pageAccess?.some((pA) => !!pA.igPageId);
};

/**
 *
 * @returns {boolean} true if the credential has pageAccess,
 * useful to make the difference between ig or fb connection, on the same credential
 */
export const isFbConnected = async function (credentialId: string): Promise<boolean> {
    const credential = await facebookCredentialsRepository.getCredentialById(credentialId);
    return credential?.pageAccess?.some((pA) => !!pA.fbPageId);
};

/**
 * Get facebook access token for first time
 * @param {string} code - facebook code returned when user completed facebook app auth flow
 */
export const fetchAccessToken = function (code) {
    return FB.api('oauth/access_token', {
        client_id: Config.platforms.facebook.api.appId,
        client_secret: Config.platforms.facebook.api.appSecret,
        redirect_uri: Config.platforms.facebook.api.redirectUri,
        code,
    });
};

export const getAccount = async function (credentialId: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint =
        '/me?fields=picture,overall_star_rating,location,name,link,access_token,instagram_business_account{name,biography,profile_picture_url,username},store_location_descriptor';
    const method = 'get';
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params: {},
        pageAccessToken,
    });
};

/**
 * Get list of pages linked to userAccessToken and their locations (= stores in fb)
 */
export const getAccountsAndStores = async function (userAccessToken: string): Promise<IFacebookAccountAggregation> {
    const result = await _refreshUserAccessToken(userAccessToken);

    const userFields = 'id,name';
    const accountFields =
        'has_transitioned_to_new_page_experience,picture,overall_star_rating,location,name,link,access_token,instagram_business_account{name,biography,profile_picture_url,username},store_location_descriptor,parent_page';
    const limit = DEFAULT_ACCOUNTS_LIMIT;

    const userInfo = await _callFbApi({
        endpoint: `/me?fields=${userFields}`,
        method: 'get',
        params: {},
        token: result.access_token,
    });

    const { id: userId, name: userName } = userInfo;

    let allAccounts: IFacebookAccount[] = [];
    let hasNextPage = true;
    let afterCursor: string | null = null;
    let loopBreaker = 10;

    while (hasNextPage && loopBreaker > 0) {
        const endpoint = `/me/accounts?fields=${accountFields}&limit=${limit}${afterCursor ? `&after=${afterCursor}` : ''}`;

        const response: { data: IFacebookAccount[]; paging: IFacebookPaging } = await _callFbApi({
            endpoint,
            method: 'get',
            params: {},
            token: result.access_token,
        });

        if (response?.data?.length) {
            allAccounts = [...allAccounts, ...response.data];
        }

        if (response?.paging?.cursors?.after && response.data.length === limit) {
            // The second condition is because sometimes the cursor contains next but there is no next page
            afterCursor = response.paging.cursors.after;
        } else {
            hasNextPage = false;
        }
        loopBreaker -= 1;
    }
    const locationAccounts = await _getLocationsForAccounts(allAccounts);

    if (locationAccounts?.length) {
        allAccounts.push(...locationAccounts);
    }
    return {
        id: userId,
        name: userName,
        data: uniqBy(allAccounts, 'id'),
    };
};

const _getLocationsForAccounts = async (accounts: IFacebookAccount[]): Promise<IFacebookAccount[]> => {
    try {
        const parentPageCandidates = accounts.filter((account) => !account.store_location_descriptor);
        const results = await Promise.allSettled(
            parentPageCandidates.map((account) =>
                _callFbApi({
                    // eslint-disable-next-line max-len
                    endpoint: `/${account.id}?fields=locations{id,store_location_descriptor,name,link,location,access_token,has_transitioned_to_new_page_experience,picture}`,
                    method: 'get',
                    params: {},
                    token: account.access_token ?? '', // we need to fetch potential locations using page access token otherwise it will fail
                })
            )
        );
        const locationAccounts = results
            .filter(isFulfilled)
            .map((r) => r.value)
            .filter((v) => !!v.locations?.data?.length)
            .flatMap((acc) => acc.locations.data)
            .filter((locationAccount) => !!locationAccount.access_token);
        return locationAccounts;
    } catch (error) {
        logger.warn('[FB_FAILED_TO_FETCH_LOCATIONS]', { error });
        return [];
    }
};

export const updateBrandPageSettingsAboutPosts = async function (credentialId: string, pageId: string, params: Record<string, any>) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `${pageId}/settings`;
    const method = 'post';
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const getUserAccounts = async function (credentialId: string) {
    const credential = await facebookCredentialsRepository.getCredentialById(credentialId);
    if (!credential) {
        throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_NOT_FOUND, {
            message: 'Facebook not connected',
        });
    }
    const result = await _refreshUserAccessToken(credential.userAccessToken);
    // eslint-disable-next-line max-len
    const endpoint = `/me?fields=id,name,accounts.limit(${DEFAULT_IG_ACCOUNTS_LIMIT}){name,instagram_business_account{name,profile_picture_url,username}}`;

    const method = 'get';
    const params = {};
    const token = result.access_token;
    const res = await _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId: credential.id,
    });
    const managedAccounts = res.accounts;
    if (!managedAccounts) {
        return { data: [] };
    }
    let count = 0;
    while (count < +Config.platforms.facebook.api.nextCount && managedAccounts?.paging?.next) {
        const {
            data: { data, paging },
        } = await axios.get(managedAccounts.paging.next);
        managedAccounts.data = managedAccounts.data.concat(data);
        managedAccounts.paging = paging;
        count += 1;
    }

    return { ...managedAccounts, id: res.id, name: res.name };
};

export const getUserAccountsAndStores = async function (credentialId: string): Promise<IFacebookAccountAggregation> {
    const credential = await facebookCredentialsRepository.getCredentialById(credentialId);
    if (!credential) {
        throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_NOT_FOUND, {
            message: 'Facebook not connected',
        });
    }

    const userAccessToken = credential.userAccessToken;
    const accountAggregation: IFacebookAccountAggregation = await getAccountsAndStores(userAccessToken);

    if (!credential.pageAccess?.length && accountAggregation.data?.length) {
        _refreshPageAccess(credentialId, accountAggregation).catch((e) => logger.error('[FB_CREDENTIALS] _refreshPageAccess error', e));
    }
    return accountAggregation;
};

const _refreshPageAccess = function (credentialId: string, accountAggregation: IFacebookAccountAggregation) {
    const pageAccess = accountAggregation.data.map((account) => ({
        fbPageId: account.id,
        igPageId: account.instagram_business_account?.id ?? null,
        pageAccessToken: account.access_token,
    }));

    return facebookCredentialsRepository.findOneAndUpdate({ filter: { _id: toDbId(credentialId) }, update: { pageAccess } });
};

export const fetchFields = async function (credentialId: string, pageId: string, facebookFields: string[]) {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = pageId;
    const method = 'get';
    const params = {
        fields: facebookFields
            .filter((f) => !!f)
            .join(',')
            .replace(/,{2,}/g, ','),
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

/**
 * Get page ratings
 * https://developers.facebook.com/docs/graph-api/reference/page/ratings/
 * required authorizations : pages_show_list pages_read_user_content public_profil
 */
export const getPageRatings = async function (credentialId: string, pageId: string, recentOnly: boolean) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `${pageId}/ratings`;
    const method = 'get';
    const since = new Date(Date.now() - TimeInMilliseconds.DAY).getTime();

    const createdTimeField = recentOnly ? `created_time.since(${since})` : 'created_time';
    const params = {
        // eslint-disable-next-line max-len
        fields: `${createdTimeField},has_rating,has_review,open_graph_story{comments,message,id},rating,recommendation_type,review_text,reviewer{id,name,picture}`,
    };
    const result = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
    return result.data.map((el) => ({ type: 'rating', ...el }));
};

/**
 * Create visitor post comment
 */
export const postVisitorPostComment = async function (credentialId: string, review: IReview, comment: FacebookComment) {
    const credential = await _getCredentialById(credentialId);
    const pageId = review.socialId.split('_')[0];
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `/${review.socialId}/comments`;
    const method = 'post';
    const params = {
        ...comment,
        fields: 'id,created_time,from{id,name,picture},is_hidden,message,comments',
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 * Create visitor post comment
 * https://developers.facebook.com/docs/graph-api/reference/v10.0/object/comments
 * necessary authorizations : pages_manage_engagement pages_show_list public_profil
 */
export const postComment = async function (credentialId: string, postId: string, message: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId); // for facebook, postId = pageId_postId - i manually extract the page id to make the request work
    const endpoint = `/${postId}/comments`;
    const method = 'post';
    const params = {
        message,
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const replyToComment = async function (credentialId: string, mention: Partial<ICommentMention>, message: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `/${mention.socialId}/comments`;
    const method = 'post';
    const params = { message };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const replyToRating = async function (credentialId: string, pageId: string, rating: IReview, comment: FacebookComment) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId); // for facebook, postId = pageId_postId - i manually extract the page id to make the request work
    const endpoint = `/${rating.socialId}/comments`;
    const method = 'post';
    const params = {
        ...comment,
        fields: 'id,created_time,from{id,name,picture},is_hidden,message,comments',
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-comment/replies
 */
export const igReplyToComment = async function (credentialId: string, comment: Partial<ICommentMention>, message: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId); // doc says that /replies endpoint requires UserAccessToken
    const endpoint = `/${comment.socialId}/replies?message=${message}`;
    const method = 'post';
    const params = {};
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-comment/replies
 */
export const replyToPost = async function (credentialId: string, socialId: string, message: string, pageId: string): Promise<any> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId); // doc says that /replies endpoint requires UserAccessToken
    const endpoint = `/${socialId}/comments?message=${message}`;
    const method = 'post';
    const params = {};
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/mentions/
 */
export const igReplyToMention = async function (
    credentialId: string,
    mediaId: string,
    message: string,
    pageId: string,
    commentId?: string
): Promise<any> {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = `/${pageId}/mentions?media_id=${mediaId}${commentId ? '&comment_id=' + commentId : ''}&message=${message}`;
    const method = 'post';
    const params = {};
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const postFields = async function (credentialId: string, pageId: string, data: Partial<FacebookPageInfo>) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = pageId;
    const method = 'post';
    const params = data;

    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 * necessary authorizations : pages_manage_posts
 */
export const getPagePosts = async (
    credentialId: string,
    pageId: string,
    recentPostsOnly = false
): Promise<{ posts: { data: FbPostData[] } }> => {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = pageId;
    const method = 'get';
    const params = {
        fields: 'posts{id,is_published,message,created_time,updated_time,permalink_url,story,status_type,attachments{subattachments,media},comments{like_count,id,from,message,created_time,comments{like_count,id,from,message,created_time}}}',
    };
    const res = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
    let posts: FbPostData[] = res?.posts?.data || [];
    let next = res?.posts?.paging?.next;
    let count = 0;
    const fetchPostsDepth = recentPostsOnly ? 2 : +Config.platforms.facebook.api.nextCount;
    while (count < fetchPostsDepth && next) {
        const result = await axios.get(next);
        const {
            data: { data, paging },
        } = result;
        posts = posts.concat(data);
        next = paging.next;
        count += 1;
    }
    return { posts: { data: posts } };
};

/**
 * necessary authorizations : pages_manage_posts
 */
export const getPagePostsWithComments = async (credentialId: string, pageId: string) => {
    const fbPosts = await getPagePosts(credentialId, pageId);
    const posts = fbPosts?.posts?.data;

    const postsWithAllComments = await _getAllCommentsFromPaging(posts);
    const postsWithAllCommentsAndAllReplies = await _getAllCommentsRepliesFromPaging(postsWithAllComments);
    return { posts: { data: postsWithAllCommentsAndAllReplies } };
};

export const getPagePublishedPosts = async function (
    credentialId: string,
    pageId: string,
    since: Date,
    until: Date = new Date(),
    fieldsParam = 'permalink_url,message,created_time,attachments{media,media_type,subattachments{media,media_type}},insights.metric(post_impressions),shares,comments.summary(true).limit(0),likes.summary(true).limit(0)'
): Promise<FbPostData[]> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const INTERVAL_MAX_DAYS = 10;
    if (DateTime.fromJSDate(until).diff(DateTime.fromJSDate(since), 'day').days > INTERVAL_MAX_DAYS + 1) {
        const intervals = getIntervals(since, until, {
            days: INTERVAL_MAX_DAYS,
        });
        return Promise.all(
            intervals.map(({ start, end }) => _getPagePublishedPosts(credential, pageAccessToken, pageId, start, end, fieldsParam))
        ).then((posts) => _.uniqBy(posts.flat(1), 'id'));
    }
    return _getPagePublishedPosts(credential, pageAccessToken, pageId, since, until, fieldsParam);
};

// https://developers.facebook.com/docs/graph-api/reference/v20.0/page/published_posts
const _getPagePublishedPosts = async function (
    credential: FacebookCredential,
    pageAccessToken: string,
    pageId: string,
    since: Date,
    until: Date = new Date(),
    fieldsParam = 'permalink_url,message,created_time,attachments{media,media_type,subattachments{media,media_type}},insights.metric(post_impressions),shares,comments.summary(true).limit(0),likes.summary(true).limit(0)'
): Promise<FbPostData[]> {
    const endpoint = pageId;
    const method = 'get';
    const sinceParam = `since(${Math.round(since.getTime() / 1000)})`;
    const untilParam = `until(${Math.round(until.getTime() / 1000)})`;
    const params = {
        fields: `name,published_posts.${sinceParam}.${untilParam}.limit(20){${fieldsParam}}`,
    };
    let res;
    try {
        res = await _callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });
    } catch (error: any) {
        // We can get this error when we try to get a bugged post
        // For information, the error does not appear if the request return multiple post (the bugged one is just filtered out by fb api)
        if (error.message?.startsWith(FB_PUBLISHED_POST_ERROR_MESSAGE_TO_IGNORE)) {
            res = null;
        } else {
            throw error;
        }
    }

    const username: string = res?.name;

    let posts = res?.published_posts?.data ?? [];
    let count = 0;
    while (count < +Config.platforms.facebook.api.nextCount && res?.paging?.next) {
        const resPostsRest = await axios.get(res.paging.next);
        const {
            data: { data, paging },
        } = resPostsRest;
        posts = [posts, data].flat();
        res.paging.next = paging.next;
        count += 1;
    }

    return (posts as FbPostData[]).map((post) => ({ ...post, username }));
};

/**
 * instagram_basic pages_read_engagement pages_show_list
 */
export const igGetPagePosts = async (
    credentialId: string,
    pageId: string,
    maxPostCount: number = Infinity
): Promise<{
    media: {
        data: IgPostData[];
    };
}> => {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = pageId;
    const method = 'get';
    const mediaLimit = maxPostCount === Infinity ? 'media' : `media.limit(${maxPostCount})`;
    const params = {
        fields: `${mediaLimit}{
                caption,
                media_url,
                media_type,
                media_product_type,
                is_shared_to_feed,
                permalink,
                thumbnail_url,
                attachments{
                    subattachments,
                    media},
                timestamp,
                children{
                    media_url,
                    media_type,
                    thumbnail_url
                },
                comments_count,
                like_count,
                comments{
                    username,
                    timestamp,
                    like_count,
                    text,
                    replies{
                        username,
                        user,
                        text,
                        like_count,
                        timestamp
                    }
                },
                insights.metric(impressions,plays,video_views,views,reach,likes,comments,shares,saved,total_interactions)
            }`,
    };
    const res: IgPagePostsResponse = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
    let media = res?.media?.data ?? [];
    const mediaLength = media.length;
    if (media.length < maxPostCount) {
        let next = res?.media?.paging?.next;
        let count = 0;
        while (count < +Config.platforms.facebook.api.nextCount && next && media.length < maxPostCount) {
            const result = await axios.get(next);
            const {
                data: { data, paging },
            } = result;
            const filteredData =
                data.length + media.length > maxPostCount ? data.filter((e, index) => index < maxPostCount - mediaLength) : data;
            media = media.concat(filteredData);
            next = paging.next;
            count += 1;
        }
    }

    return { media: { data: media } };
};

/**
 * instagram_basic pages_read_engagement pages_show_list
 */
export const igGetPagePostsWithComments = async (credentialId: string, pageId: string, maxPostsNumber: number = Infinity) => {
    const igMedias = await igGetPagePosts(credentialId, pageId, maxPostsNumber);
    const media = igMedias?.media?.data;

    const mediaWithAllComments = await _getAllCommentsFromPaging(media);
    return { media: { data: mediaWithAllComments } };
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/business_discovery
 */
export const igGetCompetitorPosts = async function (credentialId: string, igPageId: string, userName: string) {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = igPageId;
    const method = 'get';
    const params = {
        // eslint-disable-next-line max-len
        fields: `business_discovery.username(${userName}){followers_count,username,media_count,media.limit(20){timestamp,media_url,thumbnail_url,like_count,comments_count,caption,permalink,media_type,media_product_type,children{media_url,media_type}}}`,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/business_discovery
 */
export const igGetCompetitorInfo = async function (credentialId: string, igPageId: string, userName: string) {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = igPageId;
    const method = 'get';
    const params = {
        fields: `business_discovery.username(${userName}){followers_count,username,media_count,profile_picture_url,biography,name}`,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const igGetOembed = async function (credentialId: string, url: string) {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = 'instagram_oembed';
    const method = 'get';
    const params = {
        url,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const getPhoto = async function (credentialId: string, photoId: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = photoId;
    const method = 'get';
    const params = {
        fields: 'id,created_time,updated_time,comments{like_count,id,from,message,created_time,comments{like_count,id,from,message,created_time}}',
    };
    const photo = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
    if (photo?.comments) {
        const photoWithAllComments = await _getAllCommentsFromPaging(photo?.comments?.data);
        const photoWithAllCommentsAndAllReplies = await _getAllCommentsRepliesFromPaging(photoWithAllComments);
        photo.comments.data = photoWithAllCommentsAndAllReplies;
    }
    return photo;
};

export const getMessage = async function (
    credentialId: string,
    messageId: string,
    pageId: string,
    platform = ConversationPlatform.MESSENGER
): Promise<IgMessage | FbMessage> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = messageId;
    const method = 'get';
    const fields = 'message,from,to,reactions,shares,is_unsupported,story,attachments,created_time,thread_id';
    const params = {
        fields: platform === 'messenger' ? fields.replace('reactions,', '') : fields,
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const getConversation = async function (credentialId: string, conversationId: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = conversationId;
    const method = 'get';
    const params = {
        fields: 'id,participants,link',
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const getAllConversationsAndMessages = async function (
    credentialId: string,
    pageId: string,
    platform = ConversationPlatform.MESSENGER
): Promise<{ data: SocialConversationWithMessages<typeof platform>[] }> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = 'me/conversations';
    const method = 'get';
    const fields =
        'id,participants,link,unread_count,messages{message,from,to,reactions,shares,is_unsupported,story,attachments,created_time,thread_id}';
    const params = {
        platform,
        fields: platform === 'messenger' ? fields.replace('reactions,', '') : fields,
    };
    const res = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });

    let conversations: SocialConversationWithMessages<typeof platform>[] = res?.data || [];
    let next = res?.paging?.next;
    let count = 0;
    while (count < Number(Config.platforms.facebook.api.nextCount) && next) {
        const result = await axios.get(next);
        const {
            data: { data, paging },
        } = result;
        conversations = conversations.concat(data);
        next = paging?.next;
        count += 1;
    }

    conversations = conversations.filter((conversation) => conversation.messages?.data?.length > 0);

    conversations = await Promise.all(
        conversations.map(async (conversation) => {
            let messages = conversation.messages?.data || [];
            let nextMessages = conversation.messages?.paging?.next;
            let countMessages = 0;
            while (countMessages < Number(Config.platforms.facebook.api.nextCount) && nextMessages) {
                const result = await axios.get(nextMessages);
                const {
                    data: { data, paging },
                } = result;
                messages = [...messages, ...data];
                nextMessages = paging?.next;
                countMessages += 1;
            }
            conversation.messages.data = messages;
            return conversation;
        })
    );
    return { data: conversations };
};

export const getAllConversations = async function (
    credentialId: string,
    pageId: string,
    platform = ConversationPlatform.MESSENGER,
    userSocialId: string | null = null
): Promise<{ data: SocialConversationWithMessages[] }> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = 'me/conversations';
    const method = 'get';
    const params: {
        platform: ConversationPlatform;
        fields: string;
        user_id?: string;
    } = {
        platform,
        fields: 'id,participants,link,messages{id},unread_count',
    };
    if (userSocialId) {
        Object.assign(params, { user_id: userSocialId });
    }
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const getUserProfilePicture = async function (
    userSocialId: string,
    credentialId: string,
    pageId: string,
    platform: ConversationPlatform
) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `${userSocialId}`;
    const method = 'get';
    const params = {
        fields: platform === ConversationPlatform.MESSENGER ? '' : endpoint[0] === '1' ? 'profile_picture_url' : 'profile_pic',
    };
    const res = await _callFbApi({
        token: pageAccessToken,
        endpoint,
        method,
        params,
        credentialId,
    });
    if (platform === ConversationPlatform.INSTAGRAM) return res?.profile_picture_url || res?.profile_pic;
    return res?.profile_pic;
};

export const sendMessage = async function (credentialId: string, pageId: string, body: FacebookMessage) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = 'me/messages';
    const method = 'post';
    const params = {
        ...body,
    };
    const res = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
    return { data: res };
};

export const getPost = async function (credentialId: string, postId: string, pageId: string): Promise<FbPostData> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = postId;
    const method = 'get';
    const params = {
        fields: 'id,from,message,created_time,updated_time,story,status_type,permalink_url,attachments{subattachments,media},comments{like_count,id,from,message,created_time,comments{like_count,id,from,message,created_time}}',
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const igGetPost = async function (credentialId: string, igPostId: string, pageId: string): Promise<IgPostData> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = igPostId;
    const method = 'get';
    const params = {
        fields: 'username,caption,media_url,media_type,media_product_type,is_shared_to_feed,permalink,thumbnail_url,timestamp,children{media_url,media_type},comments{username,timestamp,like_count,text,replies{username,user,text,like_count,timestamp}},owner{name,profile_picture_url,username}',
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const getComment = async function (credentialId: string, comment: Partial<IComment>) {
    const credential = await _getCredentialById(credentialId);
    const pageId = comment.postSocialId?.split('_')[0];
    assert(pageId, 'Missing pageId');
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = comment.socialId;
    assert(endpoint, 'Missing comment social id');
    const method = 'get';
    const params = {
        fields: 'like_count,id,from,message,created_time,comments{like_count,id,from,message,created_time}',
    };
    return _callWithPageAccessToken({
        credential,
        endpoint,
        pageId,
        method,
        params,
        pageAccessToken,
    });
};

export const igGetComment = async function (credentialId: string, comment: IComment) {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = comment.socialId;
    const method = 'get';
    const params = {
        fields: 'username,timestamp,like_count,text,replies{username,user,text,like_count,timestamp}',
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const igPostComment = async function (credentialId: string, mediaId: string, message: string) {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = `${mediaId}/comments`;
    const method = 'post';
    const params = {
        message,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

/**
 *
 * https://developers.facebook.com/docs/graph-api/reference/page/insights/
 */
export const getInsights = async function (
    credentialId: string,
    pageId: string,
    since: string,
    until: string
): Promise<IFacebookDataWithPaging<IFacebookInsights>> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `${pageId}/insights`;
    const method = 'get';
    const params = {
        metric: ['page_impressions', 'page_post_engagements'].join(','),
        period: 'day',
        since,
        until,
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/media
 */
export const igGetPagePostsWithInsights = async function (
    credentialId: string,
    pageId: string,
    since: Date,
    until: Date = new Date(),
    // If false and some interval call throw, we simply ignore it by returning empty data
    // specifically used for Maloupe for now
    shouldRaiseError: boolean = true
): Promise<IgPostData[]> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const INTERVAL_MAX_DAYS = 10;
    if (DateTime.fromJSDate(until).diff(DateTime.fromJSDate(since), 'day').days > INTERVAL_MAX_DAYS + 1) {
        const intervals = getIntervals(since, until, {
            days: INTERVAL_MAX_DAYS,
        });

        const postWithInsightPromise = (start: Date, end: Date) =>
            _igGetPagePostsWithInsights(credential, pageAccessToken, pageId, start, end);

        return Promise.all(
            intervals.map(({ start, end }) =>
                shouldRaiseError ? postWithInsightPromise(start, end) : postWithInsightPromise(start, end).catch(() => [])
            )
        ).then((posts) => _.uniqBy(posts.flat(1), 'id'));
    }
    return _igGetPagePostsWithInsights(credential, pageAccessToken, pageId, since, until);
};

const _igGetPagePostsWithInsights = async function (
    credential: FacebookCredential,
    pageAccessToken: string,
    pageId: string,
    since: Date,
    until: Date = new Date()
): Promise<IgPostData[]> {
    // default limit is 25 medias
    const endpoint = `${pageId}/media`;
    const method = 'get';
    const sinceTimestamp = Math.round(since.getTime() / 1000);
    const untilTimestamp = Math.round(until.getTime() / 1000);
    if (sinceTimestamp === untilTimestamp) {
        return [];
    }
    const params = {
        fields: 'username,media_type,media_product_type,is_shared_to_feed,permalink,thumbnail_url,caption,insights.metric(impressions,plays,video_views,views,reach,likes,comments,shares,saved),timestamp,comments_count,like_count,media_url,children{media_url,media_type}',
        since: sinceTimestamp,
        until: untilTimestamp,
        limit: 20,
    };
    // to avoid problem: "The media was posted before the most recent time that the user's account was converted to a business account from a personal account"
    // we need to loop until we get this error
    // https://stackoverflow.com/questions/********/instagram-graph-api-fetch-media-insights-metric-when-a-user-switched-from-pers
    // that seems to be for media after 2017-05-00, need to clarify
    const res = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
    let posts = res.data;
    let count = 0;
    while (count < +Config.platforms.facebook.api.nextCount && res.paging?.next) {
        try {
            const resPostsRest = await axios.get(res.paging.next);
            const {
                data: { data, paging },
            } = resPostsRest;
            posts = [posts, data].flat();
            res.paging.next = paging.next;
            count += 1;
        } catch (e) {
            res.paging.next = null;
        }
    }
    return posts;
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/media#affichage
 */
export const igGetPostsEngagements = async function (credentialId: string, pageId: string, since: string, until: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `${pageId}/media`;
    const method = 'get';
    const params = {
        fields: 'timestamp,insights.metric(total_interactions)',
        since,
        until,
        limit: MAX_MEDIAS_LIMIT,
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/media#affichage
 */
export const igGetPosts = async function (credentialId: string, pageId: string, since: string, until: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `${pageId}/media`;
    const method = 'get';
    const params = {
        fields: 'timestamp',
        since,
        until,
        limit: MAX_MEDIAS_LIMIT,
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *  Get followers (instagram or facebook: same endpoint)
 */
export const getFollowers = async function (credentialId: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = pageId;
    const method = 'get';
    const params = {
        fields: 'followers_count',
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const igUsername = async function (credentialId: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = pageId;
    const method = 'get';
    const params = {
        fields: 'username',
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/media
 */
export const igCreatePost = async function (
    credentialId: string,
    post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments,
    igPageId: string,
    isCarouselItem: boolean | null = null
) {
    if (!post.attachments || post.attachments.length === 0) {
        throw new MalouError(MalouErrorCode.POST_MUST_HAVE_MEDIA, { message: 'Post must have media', metadata: { post } });
    }

    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;

    const {
        type,
        urls: { original, igFit },
    } = post.attachments[0];

    const endpoint = `/${igPageId}/media`;
    const method = 'post';

    const params: Record<string, any> = omitBy(
        {
            caption: post.text ?? '',
            location_id: post.location?.id,
            user_tags: post.userTags,
            is_carousel_item: isCarouselItem,
        },
        isNil
    );
    switch (type) {
        case MediaType.PHOTO:
            params.image_url = igFit ?? original;
            break;
        case MediaType.VIDEO:
            params.media_type = 'VIDEO';
            params.video_url = original;
            break;
        default:
            throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_MEDIA_NOT_SUPPORTED, {
                message: 'Media type not supported in facebook create post',
                metadata: { type },
            });
    }
    if (post.postType === PostType.REEL) {
        params.media_type = IgMediaProductType.REELS;
        params.share_to_feed = post.isReelDisplayedInFeed;
        if (post.thumbnail && 'urls' in post.thumbnail) {
            const {
                urls: { original: originalThumbnail, igFit: igFitThumbnail },
            } = post.thumbnail;
            params.cover_url = igFitThumbnail ?? originalThumbnail;
        } else if (post.thumbnailOffsetTimeInMs) {
            params.thumb_offset = post.thumbnailOffsetTimeInMs;
        }
    }

    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/media
 */
export const igCreateCarousel = async function (
    credentialId: string,
    containerIds: string[],
    post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments,
    igPageId: string
) {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;

    const endpoint = `/${igPageId}/media`;
    const method = 'post';
    const params = omitBy(
        {
            caption: post.text ?? '',
            location_id: post.location?.id,
            media_type: 'CAROUSEL', // TODO: add enum for instagram media_type
            children: containerIds,
        },
        isNil
    );
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const igCheckCreationStatus = async function (
    credentialId: string,
    containerId: string
): Promise<{
    id: string;
    status: string;
    status_code: IgMediaContainerStatus;
}> {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;

    const endpoint = `/${containerId}`;
    const method = 'get';
    const params = omitBy(
        {
            fields: 'status_code,status',
        },
        isNil
    );
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

/**
 *
 * https://developers.facebook.com/docs/instagram-api/reference/ig-user/media_publish
 */
export const igPublishPost = async function (credentialId: string, igCreationId: string, igPageId: string): Promise<{ id: string }> {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = `/${igPageId}/media_publish`;
    const method = 'post';
    const params = {
        creation_id: igCreationId,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

/**
 * @return the facebook video id
 */
export const uploadPublishedVideo = async (credentialId: string, pageId: string, url: string, text: string): Promise<string> => {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `/${pageId}/videos`;
    const method = 'post';
    const params = {
        published: true,
        file_url: url,
        description: text,
    };

    const res = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });

    return res.id;
};

/**
 * @return the facebook photo id
 */
export const uploadUnpublishedPhoto = async function (credentialId: string, pageId: string, url: string): Promise<string> {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `/${pageId}/photos`;
    const method = 'post';
    const params = {
        published: false,
        url,
    };

    const res = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });

    return res.id;
};

/**
 * @return the facebook post id (in the format : page_id + '_' + post_id)
 */
export const publishPostWithUnpublishedFbPhotoIds = async (
    credentialId: string,
    pageId: string,
    body: { text: string; location?: string },
    unpublishedPhotoIds: string[]
): Promise<string> => {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `/${pageId}/feed`;
    const method = 'post';
    const params: Record<string, any> = omitBy(
        {
            message: body.text,
            place: body.location,
        },
        isNil
    );
    unpublishedPhotoIds.forEach((id, idx) => {
        params[`attached_media[${idx}]`] = { media_fbid: id }; // https://developers.facebook.com/docs/graph-api/reference/page/photos#multi
    });
    const res = await _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
    return res.id;
};

/**
 * This method will throw exception "Unsupported post request" due to API facebook even if
 * params are good. This method will also update profile picture.
 * Facebook endpoint is broken 15/06/21.
 */
export const publishPageProfilePicture = async function (credentialId: string, logo: { picture: string }, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `/${pageId}/picture`;
    const method = 'post';
    const params = logo;
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

/**
 *
 * https://developers.facebook.com/docs/graph-api/reference/page-post/#Deleting
 */
export const deletePagePost = async function (credentialId: string, postId: string, pageId: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = postId;
    const method = 'delete';
    const params = {};
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const subscribeToWebhooks = async function (credentialId: string, pageId: string, subscribedFields: string) {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = `${pageId}/subscribed_apps`;
    const method = 'post';
    const params = {
        subscribed_fields: subscribedFields,
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const checkAccessToken = async (accessToken: string, platformKey: string) => {
    const access = await fetchDebugAccessToken(accessToken);
    return {
        ...access,
        missingAccess: _getMissingAccess(access.scopes, platformKey),
        platformKey,
    };
};

export const fetchDebugAccessToken = async (accessToken: string): Promise<DebuggedToken> => {
    const appToken = Config.platforms.facebook.api.appToken;
    const endpoint = '/debug_token';
    const method = 'get';
    const params = {
        input_token: accessToken,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token: appToken,
    }).then((access) => ({ ...access.data }));
};

export const getAccessFromCredential = async ({
    credentialId,
    pageId,
    platformKey,
}: {
    credentialId: string;
    pageId: string;
    platformKey: string;
}) => {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    return checkAccessToken(pageAccessToken, platformKey);
};

export const setAccountsPermissions = async (
    accounts: MalouRestaurantSearchResult[],
    credential: FacebookCredential,
    platformKey: string
): Promise<MalouRestaurantSearchResult[]> => {
    const debugToken = await fetchDebugAccessToken(credential.userAccessToken);

    const _getPermissionsStatusForPlatform = getPermissionsStatusForPlatformUseCase(
        getPermissionsStatusFromGranularScopes,
        genericFbApiCall
    );
    return Promise.all(
        accounts.map(async (account) => ({
            ...account,
            access: await _getPermissionsStatusForPlatform(credential, platformKey, account.socialId, debugToken),
        }))
    );
};

export const getUserPlatformsPermissions = async (restaurantId: ID) => {
    const [fbPlatformData, igPlatformData] = await Promise.all(
        getPlatformKeysLinkedWithMeta().map(async (key) => {
            const res = await platformsRepository.findOne({
                filter: { restaurantId, key },
                options: {
                    lean: true,
                    populate: [
                        {
                            path: 'credentials',
                        },
                    ],
                },
            });
            return {
                ...res,
                credentials: res?.credentials.map((credential: any) => new FacebookCredential(credential)),
            };
        })
    );

    const permissions: {
        userPermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
        fbPagePermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
        igPagePermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
    } = {
        userPermissions: {
            status: PermissionStatus.NOT_CONNECTED,
            data: null,
        },
        fbPagePermissions: {
            status: PermissionStatus.NOT_CONNECTED,
            data: null,
        },
        igPagePermissions: {
            status: PermissionStatus.NOT_CONNECTED,
            data: null,
        },
    };

    if (fbPlatformData) {
        const firstCredential = fbPlatformData.credentials?.[0];
        assert(firstCredential, 'Missing credential');
        // TODO Calls en parallèle ?
        permissions.fbPagePermissions = await _fetchPlatformPagePermissions(
            firstCredential,
            fbPlatformData.socialId ?? '',
            PlatformKey.FACEBOOK
        );
        permissions.userPermissions = await _fetchPlatformUserPermissions(firstCredential);
    }

    if (igPlatformData) {
        const firstCredential = igPlatformData.credentials?.[0];
        assert(firstCredential, 'Missing credential');
        permissions.igPagePermissions = await _fetchPlatformPagePermissions(
            firstCredential,
            igPlatformData.socialId ?? '',
            PlatformKey.INSTAGRAM
        );
        if (!permissions.userPermissions.data || permissions.userPermissions.status === PermissionStatus.NO_ACCESS) {
            permissions.userPermissions = await _fetchPlatformUserPermissions(firstCredential);
        }
    }
    return _getEventPermissions(permissions as FacebookPermissionStatus);
};

/**
 * get information about facebook pages
 * https://developers.facebook.com/docs/pages/searching/?locale=fr_FR
 */
export const pagesSearch = async (
    query: string,
    filters: { onlyWithLocation: boolean; whitelistedPageIds: string[]; wantedPlatformSocialId?: string }
) => {
    const endpoint = 'pages/search';
    const params = {
        q: query,
        fields: 'id,name,location,link',
        access_token: Config.platforms.facebook.api.appToken,
    };
    const method = 'get';
    const pagesWithPaging: IFacebookDataWithPaging<Pick<IFacebookAccount, 'id' | 'name' | 'location' | 'link'>> = await _callFbApi({
        endpoint,
        method,
        params,
        token: Config.platforms.facebook.api.appToken,
    });

    const wantedPlatformSocialId = filters.wantedPlatformSocialId;

    let pages = pagesWithPaging?.data || [];
    let next = pagesWithPaging?.paging?.next;
    let count = 0;

    let wantedPages = wantedPlatformSocialId ? pages.filter((page) => page.id === wantedPlatformSocialId) : [];
    const maxRetries = 3;
    while (count < maxRetries && next && !wantedPages.length) {
        const result = await axios.get(next);
        const {
            data: { data, paging },
        } = result;

        pages = pages.concat(data);
        next = paging.next;
        count += 1;
        wantedPages = wantedPlatformSocialId ? pages.filter((page) => page.id === wantedPlatformSocialId) : [];
    }

    if (filters.onlyWithLocation) {
        const wantedLocationsWithLocation = wantedPages.filter(
            (page) =>
                (page.id && filters.whitelistedPageIds.includes(page.id)) ||
                (!isNil(page.location?.latitude) && !isNil(page.location?.longitude))
        );
        const pagesWithLocation = pages.filter(
            (page) =>
                (page.id && filters.whitelistedPageIds.includes(page.id)) ||
                (!isNil(page.location?.latitude) && !isNil(page.location?.longitude))
        );
        return !!wantedPlatformSocialId && wantedLocationsWithLocation.length ? wantedLocationsWithLocation : pagesWithLocation;
    }

    return pages;
};

export const citySearch = async (query: string): Promise<FbApiResult<FacebookCityResult[]>> => {
    const token = Config.platforms.facebook.api.appToken;
    const endpoint = `search?type=adgeolocation&location_types=["city"]&q=${query}`;
    const params = {};
    const method = 'get';
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
    });
};

export const isBrandPage = async (credentialId: string, pageId: string): Promise<boolean> => {
    const res = await getPageById(credentialId, pageId, 'locations');
    return !!res.locations;
};

export const getPageById = async (credentialId: string, pageId: string, fields: any) => {
    const credential = await _getCredentialById(credentialId);
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
    const endpoint = pageId;
    const method = 'get';
    const params = {
        fields,
    };
    return _callWithPageAccessToken({
        credential,
        pageId,
        endpoint,
        method,
        params,
        pageAccessToken,
    });
};

export const igGetPageStories = async (
    credentialId: string,
    pageId: string,
    maxStoriesNumber: number = Infinity
): Promise<IgStoryData[]> => {
    const credential = await _getCredentialById(credentialId);
    const { userAccessToken: token } = credential;
    const method = 'get';
    const endpoint = `${pageId}/stories`;
    const params = {
        fields: `media_url,media_type,timestamp,permalink`,
    };
    const res = await _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });

    let stories = res?.data || [];
    let next = res?.paging?.next;
    let count = 0;
    while (count < +Config.platforms.facebook.api.nextCount && next && stories.length < maxStoriesNumber) {
        const result = await axios.get(next);
        const {
            data: { data, paging },
        } = result;

        stories = stories.concat(data);
        next = paging.next;
        count += 1;
    }

    return stories;
};

export const igCreateStory = async (
    post: Partial<IPostWithAttachments>,
    credentialId: string,
    igPageId: string
): Promise<{ id: string }> => {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;

    if (!post.attachments?.length) {
        throw new MalouError(MalouErrorCode.POST_MUST_HAVE_MEDIA, { message: 'Post must have media', metadata: { post } });
    }
    const {
        type,
        urls: { original, igFit },
    } = post.attachments[0];

    const endpoint = `/${igPageId}/media`;
    const method = 'post';

    const params: any = {
        media_type: 'STORIES',
    };

    switch (type) {
        case MediaType.PHOTO:
            params.image_url = igFit || original;
            break;
        case MediaType.VIDEO:
            params.video_url = original;
            break;
        default:
            throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_MEDIA_NOT_SUPPORTED, {
                message: 'Media type not supported in facebook create story',
                metadata: { type },
            });
    }

    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const igGetStory = async (credentialId: string, igPostId: string) => {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;
    const endpoint = igPostId;
    const method = 'get';

    const params = {
        fields: `media_url,media_type,timestamp,permalink`,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const igGetStoryInsights = async (credentialId: string, socialId: string): Promise<FbGraphApiResponseIgStoryInsights> => {
    const credential = await _getCredentialById(credentialId);
    const token = credential.userAccessToken;

    const genericInsights = await igGetStoryGenericInsights(token, socialId, credentialId);
    const navigationInsights = await igGetStoryNavigationInsights(token, socialId, credentialId);

    return {
        ...genericInsights,
        insights: {
            ...genericInsights?.insights,
            data: [...(genericInsights?.insights?.data ?? []), ...(navigationInsights?.insights?.data ?? [])],
        },
    };
};

export const igGetStoryGenericInsights = async (
    token: string,
    socialId: string,
    credentialId: string
): Promise<FbGraphApiResponseIgStoryInsights> => {
    const endpoint = socialId;
    const method = 'get';

    const params = {
        fields: 'insights.metric(impressions,reach,replies,views)',
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

export const igGetStoryNavigationInsights = async (
    token: string,
    socialId: string,
    credentialId: string
): Promise<FbGraphApiResponseIgStoryInsights> => {
    const endpoint = socialId;
    const method = 'get';

    const params = {
        fields: 'insights.metric(navigation).breakdown(story_navigation_action_type)',
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
        credentialId,
    });
};

/**
 * List page albums
 * https://developers.facebook.com/docs/graph-api/reference/page/albums/#Reading
 */
export const getPageAlbums = (): Promise<any> => {
    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
        message: 'FB method not implemented',
        metadata: {
            platform: PlatformKey.FACEBOOK,
        },
    });
};

/**
 * Add photo to existing page album
 * https://developers.facebook.com/docs/graph-api/reference/photo#Reading
 */
export const createPhotoInAlbum = function () {
    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
        message: 'FB method not implemented',
        metadata: {
            platform: PlatformKey.FACEBOOK,
        },
    });
};

/**
 * Get page photos (posts and albums)
 * https://developers.facebook.com/docs/graph-api/reference/photo#Reading
 */
export const getPagePhotos = (): Promise<any> => {
    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
        message: 'FB method not implemented',
        metadata: {
            platform: PlatformKey.FACEBOOK,
        },
    });
};

/**
 * Delete page photo
 * https://developers.facebook.com/docs/graph-api/reference/photo/#Deleting
 */
export const deletePagePhoto = function () {
    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
        message: 'FB method not implemented',
        metadata: {
            platform: PlatformKey.FACEBOOK,
        },
    });
};

/**
 * https://developers.facebook.com/docs/graph-api/reference/v4.0/comment
 */
export const updateVisitorPostComment = function () {
    throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
        message: 'FB method not implemented',
        metadata: {
            platform: PlatformKey.FACEBOOK,
        },
    });
};

export const genericFbApiCall = function (url: string, method: string, params: Record<string, any>) {
    return FB.api(url, method, params);
};

export const getPermissionsStatusFromGranularScopes = ({
    debuggedToken,
    platformScopes,
    platformPageId,
    associatedFbPageId = '',
    brandPageSocialId = '',
}: PermissionStatusRequirements): CredentialValidityStatus => {
    const granularScopes = debuggedToken.granular_scopes;
    const missing = platformScopes
        .map((scope) => {
            const granularScope = granularScopes?.find((gs) => gs.scope === scope);
            if (!granularScope) {
                return scope;
            }
            if (granularScope.target_ids) {
                return granularScope.target_ids.find(
                    (target) => target === platformPageId || target === brandPageSocialId || target === associatedFbPageId
                )
                    ? null
                    : scope;
            }
            return null;
        })
        .filter(isNotNil);

    return {
        isValid: !missing.length && debuggedToken.is_valid,
        missing,
        dataExpiresAt: debuggedToken.data_access_expires_at,
    };
};

export const getPermissionsStatusFromScopes = ({
    debuggedToken,
    platformScopes,
}: PermissionStatusRequirements): CredentialValidityStatus => {
    const scopeList = debuggedToken.scopes;
    const missing = platformScopes.filter((ps) => !scopeList.includes(ps));

    return {
        isValid: !missing.length && debuggedToken.is_valid,
        missing,
        dataExpiresAt: debuggedToken.data_access_expires_at,
    };
};

export const refreshPageAccessToken = (
    pageId: string,
    userAccessToken: string,
    credentialId: string
): Promise<{ access_token?: string; id: string }> =>
    _callFbApi({
        endpoint: pageId,
        method: 'get',
        params: { fields: 'access_token' },
        token: userAccessToken,
        credentialId,
    });

// private

/**
 * Refresh existing user access token
 */
const _refreshUserAccessToken = function (userAccessToken: string) {
    return FB.api('oauth/access_token', {
        client_id: Config.platforms.facebook.api.appId,
        client_secret: Config.platforms.facebook.api.appSecret,
        grant_type: 'fb_exchange_token',
        fb_exchange_token: userAccessToken,
    });
};

function _callFbApi({
    endpoint,
    method,
    params,
    token,
}: {
    endpoint: string;
    method: string;
    params: Object;
    token: string;
    credentialId?: string;
}): Promise<any> {
    return new Promise((resolve, reject) =>
        FB.api(endpoint, method, { ...params, access_token: token }, (res) => {
            if (res.error) {
                reject(res.error);
            }
            resolve(res);
        })
    );
}

const _callWithPageAccessToken = async ({
    credential,
    pageId,
    endpoint,
    method,
    params,
    pageAccessToken,
}: {
    credential: FacebookCredential;
    pageId: string;
    endpoint: string;
    method: string;
    params: Record<string, any>;
    pageAccessToken: string;
}) => {
    try {
        const res = await _callFbApi({
            endpoint,
            method,
            params,
            token: pageAccessToken,
            credentialId: credential.id,
        });
        await facebookCredentialsRepository.findOneAndUpdate({
            filter: {
                _id: toDbId(credential.id),
                'pageAccess.pageAccessToken': pageAccessToken,
            },
            update: {
                'pageAccess.$.lastSeenWorking': new Date(),
            },
        });
        return res;
    } catch (error: any) {
        if (MEDIA_ERROR_SUBCODE_TO_IGNORE === error.error_subcode) {
            return { data: [] };
        }
        if (!facebookPageAccessTokenErrorCodes.includes(error.code)) {
            try {
                const res = await _callFbApi({
                    // try using directly userAccessToken, it seems that for most endpoints userAccessToken is enough
                    // and it can happen that pageAccessToken does not work even though we have the right authorizations
                    endpoint,
                    method,
                    params,
                    token: credential.userAccessToken,
                    credentialId: credential.id,
                });
                return res;
            } catch (e: any) {
                logger.info('[FB_CREDENTIAL] Failed with backup user access token strategy - ', {
                    error: e?.message ?? e,
                    pageId,
                    endpoint,
                    method,
                    params,
                });
            }
            throw error;
        }

        // if pageId is a fbPageId it will just return the same id
        const fbPageId = credential.getFbPageIdBySocialId(pageId);
        assert(fbPageId, 'Missing fbPageId');

        const res = await refreshPageAccessToken(fbPageId, credential.userAccessToken, credential.id);
        const newPageAccessToken = res.access_token || pageAccessToken;
        if (!res.access_token) {
            logger.error('[FB_CREDENTIAL] Cannot refresh page access token');
        }
        await facebookCredentialsRepository.findOneAndUpdate({
            filter: {
                _id: toDbId(credential.id),
                'pageAccess.pageAccessToken': pageAccessToken,
            },
            update: {
                'pageAccess.$.pageAccessToken': newPageAccessToken,
                'pageAccess.$.lastSeenWorking': new Date(),
            },
        });
        return _callFbApi({
            endpoint,
            method,
            params,
            token: newPageAccessToken,
            credentialId: credential.id,
        });
    }
};

const _getCredentialById = async function (credentialId: string): Promise<FacebookCredential> {
    if (!credentialId) {
        throw new MalouError(MalouErrorCode.INVALID_DATA, {
            message: '[FB_CREDENTIAL] Need credentialId', // Frontend uses this message to handle errors
            metadata: {
                platform: PlatformKey.FACEBOOK,
            },
        });
    }

    const credential = await facebookCredentialsRepository.getCredentialById(credentialId);

    if (!credential) {
        throw new MalouError(MalouErrorCode.NOT_FOUND, {
            message: 'Credential not found',
            metadata: {
                platform: PlatformKey.FACEBOOK,
                credentialId,
            },
        });
    }

    return credential;
};

/**
 * Get's page access token for given platform and credential then fetch permissions for that platform
 */
const _fetchPlatformPagePermissions = async (credential: FacebookCredential, socialId: string, platformKey: string) => {
    // const credentialId = platformCredential[0]?.credentialId
    // const credential = await _getCredentialById(credentialId)
    const pageAccessToken = await getPageAccessTokenBySocialId(credential, socialId);
    return {
        status: PermissionStatus.AVAILABLE,
        data: await checkAccessToken(pageAccessToken, platformKey),
    };
};

/**
 * Get's permissions for given userAccessToken
 */
const _fetchPlatformUserPermissions = async (credential: FacebookCredential) => {
    const userAccessToken = credential.userAccessToken;
    return userAccessToken
        ? {
              status: PermissionStatus.AVAILABLE,
              data: await fetchDebugAccessToken(userAccessToken),
          }
        : { status: PermissionStatus.NO_ACCESS, data: null };
};

const _getMissingAccess = (scope: string[], platformKey: string) => {
    switch (platformKey) {
        case PlatformKey.FACEBOOK:
            return scopes.FACEBOOK.map((val) => (!scope.includes(val) ? val : []))
                .flat()
                .join(', ');
        case PlatformKey.INSTAGRAM:
            return scopes.INSTAGRAM.map((val) => (!scope.includes(val) ? val : []))
                .flat()
                .join(', ');
        default:
            throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_PLATFORM_NOT_SUPPORTED, {
                message: `Platform not supported in getMissingAccess`,
                metadata: { platformKey },
            });
    }
};

const _getEventPermissions = (permissions: {
    userPermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
    fbPagePermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
    igPagePermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
}) => {
    const eventsPerms = {};
    eventsPermissions.forEach((event) => {
        eventsPerms[event.key] = _checkPlatformPermissions(permissions, event.permission);
    });
    return eventsPerms;
};

const _checkPlatformPermissions = (
    permissions:
        | FacebookPermissionStatus
        | {
              userPermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
              fbPagePermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
              igPagePermissions: { status: PermissionStatus; data: null } | FacebookEntityPermissionStatus;
          },
    eventPermission: ActionPermissionList
) => {
    switch (`${eventPermission.platformKey} | ${eventPermission.type}`) {
        case 'facebook | USER':
        case 'instagram | USER':
            return permissions.userPermissions.status !== PermissionStatus.AVAILABLE
                ? false
                : _comparePermissions(permissions.userPermissions.data, eventPermission.list);

        case 'facebook | PAGE':
            return permissions.fbPagePermissions.status !== PermissionStatus.AVAILABLE
                ? false
                : _comparePermissions(permissions.fbPagePermissions.data, eventPermission.list);

        case 'instagram | PAGE':
            return permissions.igPagePermissions.status !== PermissionStatus.AVAILABLE
                ? false
                : _comparePermissions(permissions.igPagePermissions.data, eventPermission.list);

        default:
            return false;
    }
};

const _comparePermissions = (permission: DebuggedToken | null, requiredScope: string[][]) => {
    if (!permission?.is_valid) {
        return false;
    }

    const permissionsLists = requiredScope.map((plRoutePermissions) => plRoutePermissions.filter((e) => !permission.scopes.includes(e)));

    const missingList = permissionsLists.find((e) => !e.length)
        ? permissionsLists.find((e) => e.length === 0)
        : permissionsLists.reduce((a, b) => (a.length > b.length ? a : b));

    return !missingList?.length;
};

export const getPermissionsStatusForPlatformUseCase =
    (getPermissionStatus: (params: PermissionStatusRequirements) => CredentialValidityStatus, genericFbCall: (...args) => Promise<any>) =>
    async (
        credential: FacebookCredential,
        platformKey: string,
        socialId: string,
        debugToken: DebuggedToken
    ): Promise<CredentialValidityStatus> => {
        switch (platformKey) {
            case PlatformKey.FACEBOOK:
                return getPermissionStatus({
                    debuggedToken: debugToken,
                    platformScopes: scopes.FACEBOOK_PAGE_SCOPE, // if parentPage needed check: https://github.com/malou-io/app-malou-api/pull/1260
                    platformPageId: socialId,
                    associatedFbPageId: undefined,
                });
            case PlatformKey.INSTAGRAM:
                try {
                    await genericFbCall(`${socialId}?fields=id&access_token=${credential.userAccessToken}`, 'GET', {});
                } catch (error: any) {
                    if (error?.message?.match(/Application does not have permission for this action/)) {
                        logger.warn('This ig account is not business: ', {
                            socialId,
                        });
                        return {
                            isValid: false,
                            missing: [],
                            miscellaneous: 'ig_not_business',
                        };
                    } else if (
                        error?.message?.match(/Unsupported get request. Object with ID/) &&
                        error?.message?.match(/does not exist/)
                    ) {
                        logger.warn('This ig account was not found or not associated to a facebook account: ', {
                            socialId,
                        });
                        return {
                            isValid: false,
                            missing: [],
                            miscellaneous: 'ig_not_found',
                        };
                    }
                }
                const fbPageId = credential.pageAccess?.find((pat) => pat.igPageId === socialId)?.fbPageId || null;
                const validity = fbPageId
                    ? getPermissionStatus({
                          debuggedToken: debugToken,
                          platformScopes: scopes.INSTAGRAM_PAGE_SCOPE,
                          platformPageId: socialId,
                          associatedFbPageId: fbPageId,
                          brandPageSocialId: undefined,
                      })
                    : { isValid: false, missing: [] };
                return validity;
            default:
                return { isValid: false, missing: [] };
        }
    };

const _getAccountWithPageAccess = async (userAccessToken: string, pageId: string, credentialId: string): Promise<AccountWithPageAccess> => {
    const endpoint = `${pageId}?fields=id,instagram_business_account,access_token`;
    const method = 'get';
    const params = {};
    return _callFbApi({
        endpoint,
        method,
        params,
        token: userAccessToken,
        credentialId,
    });
};

export const getPageAccessTokenByCredentialIdAndSocialId = async (credentialId: string, socialId: string): Promise<string> => {
    const credential = await _getCredentialById(credentialId);
    return getPageAccessTokenBySocialId(credential, socialId);
};

export const getPageAccessTokenBySocialId = async (credential: FacebookCredential, socialId: string): Promise<string> => {
    const pageAccess = credential.pageAccess.find((pAT) => pAT.fbPageId === socialId || pAT.igPageId === socialId);
    if (!pageAccess?.pageAccessToken) {
        logger.info(`[FACEBOOK_CREDENTIAL] - Trying to fetch pageAccess for pageId: `, socialId);
        const accounts = await getUserAccountsAndStores(credential.id);
        if (!accounts || 'error' in accounts) {
            throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_CANNOT_FETCH_ACCOUNTS_AND_STORES);
        }
        const account = accounts.data.find(
            (accountData) => accountData.id === socialId || accountData.instagram_business_account?.id === socialId
        );
        let accountWithPageAccess: AccountWithPageAccess | undefined;
        if (!account) {
            const debuggedToken = await fetchDebugAccessToken(credential.userAccessToken);
            const showListScope = debuggedToken.granular_scopes?.find((el) => el.scope === 'pages_show_list');
            const accountIdList = showListScope?.target_ids ?? [];
            const accountsWithPageAccess = await Promise.all(
                accountIdList.map((id) => _getAccountWithPageAccess(credential.userAccessToken, id, credential.id))
            );
            accountWithPageAccess = accountsWithPageAccess.find(
                (accountData) => accountData.id === socialId || accountData.instagram_business_account?.id === socialId
            );
            if (!accountWithPageAccess) {
                throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_CANNOT_FETCH_ACCOUNTS_AND_STORES, {
                    message: 'No access to this page',
                    metadata: { socialId },
                });
            }
        }
        const validAccount = account ?? accountWithPageAccess;
        assert(validAccount, 'Missing valid account');
        const access = {
            fbPageId: validAccount.id,
            igPageId: validAccount.instagram_business_account?.id ?? null,
            pageAccessToken: validAccount.access_token,
            lastSeenWorking: null,
        };
        assert(access.pageAccessToken, 'Missing pageAccessToken');
        // upsert the access in pageAccess array. Cannot do two update operations in the same db call.
        await facebookCredentialsRepository.findOneAndUpdate({
            filter: { _id: toDbId(credential.id) },
            update: { $pull: { pageAccess: { fbPageId: validAccount.id } } },
        });
        await facebookCredentialsRepository.findOneAndUpdate({
            filter: { _id: toDbId(credential.id) },
            update: { $addToSet: { pageAccess: access } },
        });
        return access.pageAccessToken;
    }
    return pageAccess.pageAccessToken;
};

const _getAllCommentsFromPaging = (elemWithComments: any[]): Promise<any[]> =>
    Promise.all(
        elemWithComments?.map(async (element) => {
            let comments = element.comments?.data || [];
            let nextComments = element.comments?.paging?.next;
            let offset = 0;
            let count = 0;
            while (count < +Config.platforms.facebook.api.nextCount && nextComments) {
                const result = await axios.get(`${nextComments}&offset=${offset}&limit=${COMMENTS_NUMBER_PER_PAGE}`);
                const {
                    data: { data, paging },
                } = result;
                comments = comments.concat(data);
                nextComments = paging?.next;
                // We added an offset to avoid looping through the same 'next' page in the pagination, must be an issue with graph API
                offset += COMMENTS_NUMBER_PER_PAGE;
                count += 1;
            }
            if (element.comments) {
                element.comments.data = comments;
            }
            return element;
        })
    );

/** *
 * Facebook only : can have three levels of comments/replies
 */
const _getAllCommentsRepliesFromPaging = (elemWithComments: any[]): Promise<any[]> =>
    Promise.all(
        elemWithComments?.map(async (element) => {
            if (!element.comments) {
                return element;
            }
            const elementComments: any[] = [];
            for (const comment of element.comments?.data) {
                if (!comment?.comments?.data) {
                    elementComments.push(comment);
                    continue;
                }
                let replies = comment?.comments?.data || [];
                let nextRepliedComments = comment.comments?.paging?.next;
                let count = 0;
                while (count < +Config.platforms.facebook.api.nextCount && nextRepliedComments) {
                    const result = await axios.get(nextRepliedComments);
                    const {
                        data: { data, paging },
                    } = result;
                    replies = replies.concat(data);
                    nextRepliedComments = paging?.next;
                    count += 1;
                }
                comment.comments.data = replies;
                elementComments.push(comment);
            }
            element.comments.data = elementComments;
            return element;
        })
    );

export const _handleOauthCallBackUseCase =
    (
        facebookApiAdapter: any,
        credentialsRepository: EntityRepository<ICredential>,
        getNewOrganizationIds: (authId: string, userId: ID, organizationId: ID | null, restaurantId: ID) => Promise<ID[]>
    ) =>
    async ({ user, restaurantId, code }: { user: IUserWithOrganizations; restaurantId: string; code: string }) => {
        const organizationId = user.organizations?.length === 1 ? user.organizations[0]?._id : null;
        const token = await facebookApiAdapter.fetchAccessToken(code);
        const accounts = await facebookApiAdapter.getAccountsAndStores(token.access_token);
        if (!accounts || 'error' in accounts) {
            return null;
        }
        const existingCredential = await credentialsRepository.findOne({
            filter: {
                active: true,
                authId: accounts.id,
                key: PlatformKey.FACEBOOK,
            },
            options: { lean: true },
        });
        const organizationIds = await getNewOrganizationIds(accounts.id, user._id, organizationId, restaurantId);
        const allOrganizationIds = [...organizationIds, ...(existingCredential?.organizationIds ?? [])].filter(isNotNil);
        const allOrganizationIdsUniq = uniqBy(allOrganizationIds, (id) => id.toString());
        const update = {
            active: true,
            authId: accounts.id,
            key: PlatformKey.FACEBOOK,
            userAccessToken: token.access_token,
            expiresIn: token.expires_in,
            name: accounts.name,
            pageAccess: accounts.data.map((account) => ({
                fbPageId: account.id,
                igPageId: account.instagram_business_account?.id ?? null,
                pageAccessToken: account.access_token,
            })),
            organizationIds: toDbIds(allOrganizationIdsUniq),
        };
        const updated = await credentialsRepository.upsert({
            filter: {
                active: true,
                authId: accounts.id,
                key: PlatformKey.FACEBOOK,
            },
            update,
            options: {
                new: true,
                lean: true,
            },
        });
        return updated;
    };

export const isFbTimeoutError = (fbError?: Error): boolean => isFacebookTimeoutError(fbError?.message);

export const igGetPageInfoForDiagnostic = async function (userAccessToken: string, igPageId: string, userName: string) {
    const token = userAccessToken;
    const endpoint = igPageId;
    const method = 'get';
    const params = {
        fields: `business_discovery.username(${userName}){profile_picture_url,username,biography,name}`,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
    });
};

export const igGetPagePostsForDiagnostic = async function (userAccessToken: string, igPageId: string, userName: string) {
    const token = userAccessToken;
    const endpoint = igPageId;
    const method = 'get';
    const params = {
        // eslint-disable-next-line max-len
        fields: `business_discovery.username(${userName}){profile_picture_url,name,followers_count,media.limit(20){timestamp,like_count,comments_count}}`,
    };
    return _callFbApi({
        endpoint,
        method,
        params,
        token,
    });
};

export const igGetPagePostsForStoreLocator = async function ({
    token,
    pageId,
    accountName,
}: {
    token: string;
    pageId: string;
    accountName: string;
}) {
    const method = 'get';
    const publicationsCount = 5;

    const params = {
        // eslint-disable-next-line max-len
        fields: `business_discovery.username(${accountName}){profile_picture_url,name,followers_count,media_count,media.limit(${publicationsCount}){like_count,caption,permalink,media_type,thumbnail_url,media_url,children{media_url,media_type,thumbnail_url}}}`,
    };
    return _callFbApi({
        endpoint: pageId,
        method,
        params,
        token,
    });
};

/**
 * https://developers.facebook.com/docs/video-api/guides/reels-publishing
 */
export namespace FbReels {
    export async function initUploadSession(
        pageAccessToken: string,
        pageId: string
    ): Promise<FacebookApiTypes.Videos.InitUploadSessionResponse> {
        const endpoint = `${pageId}/video_reels`;
        const method = 'post';
        const params = { upload_phase: 'start' };
        const token = pageAccessToken;
        return _callFbApi({ endpoint, method, params, token });
    }

    export async function publishReel(
        pageAccessToken: string,
        pageId: string,
        videoId: string,
        videoData: {
            title: string;
            description: string;
            locationId?: string;
        }
    ): Promise<FacebookApiTypes.Reels.PublishReelResponse> {
        const endpoint = `${pageId}/video_reels`;
        const method = 'post';
        const params = {
            video_id: videoId,
            upload_phase: 'finish',
            video_state: 'PUBLISHED',
            title: videoData.title,
            description: videoData.description,
            place: videoData.locationId,
        };
        const token = pageAccessToken;
        return _callFbApi({ endpoint, method, params, token });
    }

    export async function getReel(pageAccessToken: string, videoId: string): Promise<FacebookApiTypes.Reels.GetReelResponse> {
        const endpoint = `${videoId}`;
        const method = 'get';
        const params = { fields: 'id,created_time,updated_time,title,description,source,permalink_url,status,picture,post_id,thumbnails' };
        const token = pageAccessToken;
        return _callFbApi({ endpoint, method, params, token });
    }

    export async function getReels(
        pageAccessToken: string,
        pageId: string,
        filters?: { startDate: Date; endDate: Date }
    ): Promise<FacebookApiTypes.Reels.GetReelResponse[]> {
        return _getReels<false>(pageAccessToken, pageId, false, filters);
    }

    export async function getReelsWithInsights(
        pageAccessToken: string,
        pageId: string,
        filters: { startDate: Date; endDate: Date }
    ): Promise<FacebookApiTypes.Reels.GetReelWithInsightsResponse[]> {
        return _getReels<true>(pageAccessToken, pageId, true, filters);
    }

    // eslint-disable-next-line no-inner-declarations
    async function _getReels<T>(
        pageAccessToken: string,
        pageId: string,
        withInsights: T,
        filters?: { startDate: Date; endDate: Date }
    ): Promise<(T extends true ? FacebookApiTypes.Reels.GetReelWithInsightsResponse : FacebookApiTypes.Reels.GetReelResponse)[]> {
        const isReadyReel = (reel: FacebookApiTypes.Reels.GetReelResponse) =>
            reel.status.video_status === FacebookApiTypes.Videos.VideoStatus.READY;

        const since = filters?.startDate ? Math.round(filters.startDate.getTime() / 1000) : undefined;
        const until = filters?.endDate ? Math.round(filters.endDate.getTime() / 1000) : undefined;
        const endpoint = `${pageId}/video_reels`;
        const method = 'get';
        let fields = 'id,created_time,updated_time,title,description,source,permalink_url,status,picture,thumbnails';
        if (withInsights) {
            fields += ',video_insights';
        }
        const params = {
            fields: fields,
            limit: 50,
            since,
            until,
        };
        const token = pageAccessToken;

        const res: IFacebookDataWithPaging<any> = await _callFbApi({
            endpoint,
            method,
            params,
            token,
        });
        const reels = res.data?.filter(isReadyReel) ?? [];
        let next = res.paging?.next;
        let count = 0;
        while (next && count < +Config.platforms.facebook.api.nextCount) {
            const axiosRes = await axios.get<IFacebookDataWithPaging<any>>(next);
            reels.push(...(axiosRes.data.data?.filter(isReadyReel) ?? []));
            next = axiosRes.data.paging?.next;
            count++;
        }
        return reels;
    }
}

/**
 * Used for reels and stories
 */
export namespace FbVideos {
    export async function uploadVideo(
        pageAccessToken: string,
        uploadUrl: string,
        videoUrl: string
    ): Promise<FacebookApiTypes.Videos.UploadVideoResponse> {
        const headers = { Authorization: `OAuth ${pageAccessToken}`, file_url: videoUrl };
        const axiosRes = await axios.post<FacebookApiTypes.Videos.UploadVideoResponse>(uploadUrl, {}, { headers });
        return axiosRes.data;
    }

    export async function getUploadStatus(
        pageAccessToken: string,
        videoId: string
    ): Promise<FacebookApiTypes.Videos.GetVideoUploadStatusResponse> {
        const endpoint = `${videoId}`;
        const method = 'get';
        const params = { fields: 'status' };
        const token = pageAccessToken;
        const res = _callFbApi({ endpoint, method, params, token });
        return res;
    }

    export async function getVideoSource(
        pageAccessToken: string,
        videoId: string
    ): Promise<FacebookApiTypes.Videos.GetVideoSourceResponse> {
        const endpoint = `${videoId}`;
        const method = 'get';
        const params = { fields: 'source' };
        const token = pageAccessToken;
        const res = _callFbApi({ endpoint, method, params, token });
        return res;
    }
}

export namespace FbStories {
    /**
     * @return the facebook video id and upload url
     */
    export const initUploadSession = async function (
        credentialId: string,
        pageId: string
    ): Promise<FacebookApiTypes.Videos.InitUploadSessionResponse> {
        const credential = await _getCredentialById(credentialId);
        const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
        const endpoint = `/${pageId}/video_stories`;
        const method = 'post';
        const params = {
            upload_phase: 'start',
        };

        const res = await _callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });

        return res;
    };

    /**
     * @return the published facebook video status
     */
    export const publishVideoStory = async function (
        credentialId: string,
        pageId: string,
        videoId: string
    ): Promise<FacebookApiTypes.Stories.PublishStoryResponse> {
        const credential = await _getCredentialById(credentialId);
        const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
        const endpoint = `/${pageId}/video_stories`;
        const method = 'post';
        const params = {
            video_id: videoId,
            upload_phase: 'finish',
        };

        const res = await _callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });

        return res;
    };

    export const publishPhotoStory = async function (
        credentialId: string,
        pageId: string,
        storyId: string
    ): Promise<FacebookApiTypes.Stories.PublishStoryResponse> {
        const credential = await _getCredentialById(credentialId);
        const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
        const endpoint = `/${pageId}/photo_stories`;
        const method = 'post';
        const params = {
            photo_id: storyId,
        };

        return _callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });
    };

    /**
     * @return list of currently active stories on Facebook
     */
    export const getStories = async function (credentialId: string, pageId: string): Promise<FacebookApiTypes.Stories.GetStoryResponse[]> {
        const credential = await _getCredentialById(credentialId);
        const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
        const endpoint = `/${pageId}/stories`;
        const method = 'get';
        const params = {
            status: 'published',
        };

        const res = await _callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });
        return res.data;
    };

    export const getStoryImage = async function (
        credentialId: string,
        pageId: string,
        mediaId: string
    ): Promise<FacebookApiTypes.Stories.GetStoryImageResponse> {
        const credential = await _getCredentialById(credentialId);
        const pageAccessToken = await getPageAccessTokenBySocialId(credential, pageId);
        const endpoint = `/${mediaId}`;
        const method = 'get';
        const params = {
            fields: 'images',
        };

        return _callWithPageAccessToken({
            credential,
            pageId,
            endpoint,
            method,
            params,
            pageAccessToken,
        });
    };
}
