import { singleton } from 'tsyringe';

import { IFacebookAccount } from ':modules/credentials/platforms/facebook/facebook.interface';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';

@singleton()
export class GetSearchedPageUseCase {
    async execute({
        query,
        onlyWithLocation,
        whitelistedPageIds,
        wantedPlatformSocialId,
        city,
    }: {
        query: string;
        onlyWithLocation: boolean;
        whitelistedPageIds?: string[];
        wantedPlatformSocialId?: string;
        city?: string;
    }): Promise<Pick<IFacebookAccount, 'name' | 'link' | 'id' | 'location'>[]> {
        const pagesDataForQuery = await facebookCredentialsUseCases.pagesSearch(query, {
            onlyWithLocation,
            whitelistedPageIds: whitelistedPageIds ?? [],
            wantedPlatformSocialId,
        });
        if (!wantedPlatformSocialId) {
            return pagesDataForQuery;
        }
        if (pagesDataForQuery.length) {
            return pagesDataForQuery;
        }

        // Useful for restaurants like "Restaurant Le Cevenol" => "Le Cevenol" on Facebook
        if (query.match(/restaurant/gi)) {
            const updatedQuery = query.replace(/restaurant/gi, '').trim();
            const pagesDataWithoutRestaurantWord = await facebookCredentialsUseCases.pagesSearch(updatedQuery, {
                onlyWithLocation,
                whitelistedPageIds: whitelistedPageIds ?? [],
                wantedPlatformSocialId,
            });
            if (pagesDataWithoutRestaurantWord.length) {
                return pagesDataWithoutRestaurantWord;
            }
        }

        // Useful for Del Arte restaurants
        if (city) {
            const pagesDataForCity = await facebookCredentialsUseCases.pagesSearch(`${query} ${city}`, {
                onlyWithLocation,
                whitelistedPageIds: whitelistedPageIds ?? [],
                wantedPlatformSocialId,
            });
            if (pagesDataForCity.length) {
                return pagesDataForCity;
            }
        }

        return [];
    }
}
