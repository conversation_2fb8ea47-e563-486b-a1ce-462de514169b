import { singleton } from 'tsyringe';

import { MalouErrorCode, PlatformKey } from '@malou-io/package-utils';

import { GenericJobDefinition } from ':agenda-jobs/job-template/generic-job-definition';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { DoorDashProvider } from ':providers/doordash/doordash.provider';
import { SlackChannel, SlackService } from ':services/slack.service';

@singleton()
export class ExtendDoorDashCredentialExpirationJob extends GenericJobDefinition {
    constructor(
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _doordashProvider: DoorDashProvider,
        private readonly _slackService: SlackService
    ) {
        super({
            agendaJobName: AgendaJobName.EXTEND_DOORDASH_CREDENTIAL,
        });
    }

    async executeJob(): Promise<void> {
        try {
            const doordashPlatform = await this._platformsRepository.findOne({
                filter: {
                    key: PlatformKey.DOORDASH,
                    socialId: { $ne: null },
                },
                projection: {
                    _id: true,
                    socialId: true,
                },
                options: {
                    lean: true,
                },
            });
            if (!doordashPlatform || !doordashPlatform.socialId) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    metadata: {
                        platformKey: PlatformKey.DOORDASH,
                        message: 'No DoorDash platform found to extend cookie validity',
                    },
                });
            }

            const data = await this._doordashProvider.getStoreDetails({ storeId: doordashPlatform.socialId });
            if (!data) {
                throw new MalouError(MalouErrorCode.DOORDASH_STORE_NOT_FOUND, {
                    metadata: {
                        storeId: doordashPlatform.socialId,
                        platformId: doordashPlatform._id,
                        message: 'Error occurred while fetching DoorDash store details',
                    },
                });
            }
            return;
        } catch (error) {
            this._slackService.sendAlert({
                channel: SlackChannel.APP_ALERTS,
                data: {
                    err: error,
                    message: 'Error while extending Doordash credential',
                    jobName: this.agendaJobName,
                },
            });
        }
    }
}
