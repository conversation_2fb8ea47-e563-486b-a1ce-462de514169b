import { Page } from '@playwright/test';
import { expect } from 'baseTest';

import { RestaurantModel } from '@malou-io/package-models';
import { BusinessCategory } from '@malou-io/package-utils';

export async function createRestaurant({
    page,
    restaurantName,
    type,
    accountName,
}: {
    page: Page;
    restaurantName: string;
    type: BusinessCategory;
    accountName: string;
}) {
    const restaurant = await RestaurantModel.findOne({ name: restaurantName }, { _id: 1 }, { lean: true });
    await page.goto(`/restaurants/${restaurant?._id.toString()}/settings/platforms/connection`);
    switch (type) {
        case BusinessCategory.LOCAL_BUSINESS:
            await page.getByTestId('platforms-edit-btn-gmb').click();
            await page.getByTestId('platforms-delete-btn-gmb').click();
            await page.getByTestId('malou-dialog-primary-btn').click();
            break;
        case BusinessCategory.BRAND:
            await page.getByTestId('platforms-edit-btn-facebook').click();
            await page.getByTestId('platforms-delete-btn-facebook').click();
            await page.getByTestId('malou-dialog-primary-btn').click();
            break;
    }
    await page.goto('/restaurants/list');
    await page.getByTestId('sidenav-content-restaurant-info').click();
    await page.getByTestId('restaurants-open-add-restaurant-btn').click();
    if (type === BusinessCategory.LOCAL_BUSINESS) {
        await page.getByTestId('connect-restaurant-modal-0-type-option').click();
    } else {
        await page.getByTestId('connect-restaurant-modal-1-type-option').click();
    }
    await page.getByTestId('connect-restaurant-modal-btn').click();
    await page.getByTestId('create-restaurant-account-input').click();
    await page.locator('mat-option').filter({ hasText: accountName }).first().click();
    await page.locator('app-create-restaurant-from-platform-modal').getByText(restaurantName).click();
    await page.getByTestId('connect-restaurant-modal-next-btn').click();
    await page.waitForResponse(
        async (response) => {
            if (response.url().includes('restaurants') && response.status() === 200) {
                if (response.request().method() === 'PUT') {
                    return true;
                }
            }
            return false;
        },
        { timeout: 30000 }
    );
    await expect(await page.locator('app-after-create-restaurant-modal')).toBeVisible();

    await page.getByTestId('after-create-restaurant-secondary-btn').click();
}
